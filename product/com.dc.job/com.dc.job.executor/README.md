## Job

---

#### AdDomainSyncJobHandler
- *定时同步ad域，调用php接口(api/pe/system/active-directory/sync)* 

#### AuditReportJobHandler
- *定时导出审计报表，调用php报表导出接口(/api/v1/report/sql-operation-report/audit-report-export)，根据php接口返回信息，判断此次导出成功失败*

#### AuthExpiredJobHandler(RuleExpiredJobHandler)
- *定时验证规则是否过期，调用php接口(api/pe/permission/expire-alerts/alert)*

#### ClearVisitFrequencyJobHandler
- *清理30天以上的visit_frequency表数据*

#### DataBackupJobHandler
- *定时备份图库数据(不确定，此handler可能已废弃)*

#### DumpJobHandler
- *dump导入导出，目前支持的数据库类型为oracle、mysql、MariaDB、postGreSql，根据不同的数据库类型，需要拼接不同的cmd命令，然后起进程Process执行此命令，将进程的输入流错误流合并后，获取到执行日志，将日志结果打印到本地，方便之后的日志显示；根据日志中的结果判断执行成功失败。*

#### FJNXCleanTempFilesJobHandler
- *福建农信需求:定时清理过期文件，主线无此handler*

#### FJNXSynchronizeConnectionJobHandler
- *福建农信需求:同步实例和业务系统，主线无此handler*

#### FJNXSynchronizeUsersJobHandler
- *福建农信需求:同步用户，主线无此handler*

#### HTSynchronizeDatabaseJobHandler
- *华泰需求:同步数据库，主线无此handler*

#### 华泰需求:同步用户
- *华泰需求:同步用户，主线无此handler*

#### IllegalOperationAlertJobHandler
- *非法操作告警，调用php接口(api/v1/alert/alert-notify/illegal-operation)*

#### MockDataJobHandler
- *测试数据生成任务*

#### NoLockChangesJobHandler
- *无锁变更任务，需要拼接cmd命令，然后起进程Process执行此命令，将进程的输入流错误流合并后，获取到执行日志，将日志结果打印到本地，方便之后的日志显示；根据日志中的结果判断执行成功失败*

#### PwdUpdateJobHandler
- *定时更新密码，调用php接口(api/v1/security/resource-users/update-password)，只在每天凌晨左右触发一次*

#### RecycleBinClearDataJobHandler
- *定时清理误操作恢复备份数据，从配置库中查询出误操作恢复备份数据的保留时间，然后清理过期的误操作恢复备份的数据，需对数据表和关联表都进行清理*

#### RecycleBinJobHandler
- *误操作恢复的任务，从redis中获取需要执行的sql，调用summer去执行*

#### SchemaUpdateJobHandler
- *定时同步schema，从配置库中查询到所有的实例，过滤出需要同步的实例，判断当前实例的执行器，调用的iceage，获取当前实例的真实schema，然后同配置库中保存的schema信息做对比，有新建的schema，则保存到配置库中，有删除的schema，则从配置库中删除。*

#### ScriptExecuteJobHandler
- *定时查找 work_order_execute 表，找到现在需要执行的工单，判断当前工单实例的执行器，调用对应的summer接口*

#### SensitiveScanJobHandler
- *敏感数据智能扫描，调用php接口(/api/v1/security/sensitive-distinguish-tasks/scan)*

#### SqlExecuteJobHandler
- *任务管理-sql执行，首先判断按照策略模式指定的可执行时间是否满足，然后调用summer拆分sql，然后调用parser获取误操作恢复、脱敏等信息，将信息组合完成后，调用summer的job-export接口，之后循环调用task-info、task-result接口获取返回数据，将此信息更新到日志文件中，并根据此信息判断实行成功失败。*

#### StructCompareJobHandler
- *结构比对*

#### UserSynchronizeJobHandler
- *定时同步用户信息(不确定，具体作用不可知)*






