package com.dc.executor.job.handler;

import com.dc.job.enums.SqlExecuteStatusType;
import com.dc.repository.mysql.mapper.DatabaseConnectionMapper;
import com.dc.springboot.core.client.ProxyInternalClient;
import com.dc.springboot.core.component.JSON;
import com.dc.springboot.core.model.data.Client;
import com.dc.springboot.core.model.data.RowsData;
import com.dc.springboot.core.model.execution.SqlListExecuteMessage;
import com.dc.springboot.core.model.parser.dto.DatabaseConnectionDto;
import com.dc.type.DatabaseType;
import com.dc.job.biz.model.ReturnT;
import com.dc.job.handler.annotation.XxlJob;
import com.dc.job.util.IpUtil;
import com.dc.executor.model.ParamDTO;
import com.dc.executor.util.DCJobLogger;
import com.dc.utils.CipherUtils;
import com.dc.utils.http.FileDownloadAuthUtil;
import com.dc.utils.http.FileUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 调用 proxy 进行执行 2次。
 */
@Slf4j
@Component
public class DumpJobHandler extends AbstractJobHandler {

    @Resource
    private DatabaseConnectionMapper databaseConnectionMapper;

    @Resource
    private ProxyInternalClient proxyInternalClient;

    private final Gson gson = new GsonBuilder().serializeNulls().create();

    @XxlJob("DumpJobHandler")
    public ReturnT<String> dumpJobHandler(String param) throws Exception {

        ParamDTO paramDTO = getParamDTO(param);

        String importDumpFile = null;

        try {

            this.updateRunningStatus(paramDTO);

            if (paramDTO.getConnectId() != null && paramDTO.getDbType() != null && paramDTO.getOperation() != null) {

                String displayDumpName = paramDTO.getDumpName();
                String filePath = getExportDumpPath();
                String importDumpPath = getImportDumpPath(); // 创建[导入文件]在job服务器的存储路径
                importDumpFile = importDumpPath + paramDTO.getDumpName();
                if (verifyImportFile(paramDTO, importDumpPath)) {
                    return ReturnT.SUCCESS;
                }

                DatabaseConnectionDto instance = jobMapper.toDatabaseConnectionDto(this.databaseConnectionMapper.getConnectionByUniqueKey(paramDTO.getConnectId()));
                String password = getPassword(instance, paramDTO);

                String prefix = "";
                String cmd = "";
                String cmdStar = "";
                DCJobLogger.log("db_type:" + paramDTO.getDbType());
                if (DatabaseType.ORACLE.getValue().equals(paramDTO.getDbType())) {
                    prefix = getOraclePrefix(paramDTO);
                    cmd = getOracleCmd(paramDTO, instance, filePath, importDumpPath, paramDTO.getDumpName(), password);
                    cmdStar = getOracleCmd(paramDTO, instance, filePath, importDumpPath, displayDumpName, "*****");
                } else if (Arrays.asList(DatabaseType.MYSQL.getValue(), DatabaseType.MARIA_DB.getValue(), DatabaseType.GOLDEN_DB.getValue()).contains(paramDTO.getDbType())) {
                    prefix = getMysqlPrefix(paramDTO);
                    cmd = getMysqlCmd(paramDTO, instance, filePath, importDumpPath, paramDTO.getDumpName(), password);
                    cmdStar = getMysqlCmd(paramDTO, instance, filePath, importDumpPath, displayDumpName, "*****");
                } else if (Arrays.asList(DatabaseType.PG_SQL.getValue(), DatabaseType.RASE_SQL.getValue()).contains(paramDTO.getDbType())) {
                    prefix = getPostGreSqlPrefix(paramDTO);
                    cmd = getPostGreSqlCmd(paramDTO, instance, filePath, importDumpPath, paramDTO.getDumpName());
                    cmdStar = getPostGreSqlCmd(paramDTO, instance, filePath, importDumpPath, displayDumpName);
                }

                String environmentHomePath = "";
                if (DatabaseType.ORACLE.getValue().equals(paramDTO.getDbType())) {
                    environmentHomePath = verifyOracleVersion(paramDTO, instance.getVersion());
                    if (!environmentHomePath.isEmpty()) {
                        prefix = environmentHomePath + prefix;
                    } else {
                        return ReturnT.SUCCESS;
                    }
                }

                DCJobLogger.log("\n" + prefix + " " + cmdStar);

                ProcessBuilder processBuilder = new ProcessBuilder(prefix, cmd);

                this.buildEnvironment(environmentHomePath, getOracleNlsCharacterSet(paramDTO, instance), processBuilder, paramDTO, password);

                processBuilder.redirectErrorStream(true); // input和error流合并

                Process process = buildProcess(processBuilder, paramDTO, prefix, cmd, password);

                InputStream inputStream = process.getInputStream();
                InputStream errorStream = process.getErrorStream();

                SequenceInputStream mergeStream = new SequenceInputStream(inputStream, errorStream); // input和error流合并
                StringBuilder messageBuilder = new StringBuilder();
                boolean mysqlError = false;

                BufferedReader br = new BufferedReader(new InputStreamReader(mergeStream, getCharset()));
                try {
                    String line = null;
                    while ((line = br.readLine()) != null) {

                        DCJobLogger.log(line);
                        messageBuilder.append(line).append("\n");

                        mysqlError = mysqlError || mySqlError(line, paramDTO);

                        try {
                            Thread.sleep(10); // 最低10才有效
                        } catch (InterruptedException e) {
                            DCJobLogger.schedulingTermination();
                            return ReturnT.SUCCESS;
                        }

                    }
                } catch (IOException e) {
                    log.error("read log error!", e);
                } finally {
                    try {
                        inputStream.close();
                        errorStream.close();
                        mergeStream.close();
                        br.close();
                    } catch (IOException e) {
                        log.error("close stream error!", e);
                    }
                }

                try {
                    process.waitFor();
                } catch (InterruptedException e) {
                    DCJobLogger.schedulingTermination();
                    return ReturnT.SUCCESS;
                }

                int status = judgeStatus(messageBuilder, paramDTO, mysqlError, filePath);

                updateFinishStatus(paramDTO, status);

                process.destroy();

            } else {
                log.info("实例id、数据库类型、操作类型不能为空！");
                DCJobLogger.log("实例id、数据库类型、操作类型不能为空！");
            }

        } catch (Exception e) {
            log.error("call dumpJobHandler error!", e);
            DCJobLogger.log("导入导出出错: " + e);
            handleDumpJobError(paramDTO);
        } finally {
            FileUtil.deleteFile(importDumpFile);
        }

        return ReturnT.SUCCESS;
    }

    private ParamDTO getParamDTO(String param) {
        byte[] bytes = param.getBytes(StandardCharsets.UTF_8);
        param = new String(bytes);
        return gson.fromJson(param, ParamDTO.class);
    }

    private void updateRunningStatus(ParamDTO paramDTO) {
        if (paramDTO.getJobId() != null) {
            dcJobMapper.beginUpdate(paramDTO.getJobId(), SqlExecuteStatusType.running.getValue());
        }

        if (paramDTO.getLogId() != 0L && paramDTO.getJobId() != null) {
            int jobTriggerCount = jobLogMapper.findJobTriggerCount(paramDTO.getJobId());
            long triggerCount = jobTriggerCount + 1L;
            jobLogMapper.updateBeginTriggerStatus(paramDTO.getLogId(), SqlExecuteStatusType.running.getValue(), triggerCount);
            jobLogMapper.updateExecutorTomcatAddress(paramDTO.getLogId(), IpUtil.getIpPort(ip, port)); // 更新执行器服务的ip和port
        }
    }

    private String getExportDumpPath() {
        String filePath = jobConfig.getUploadLocation() + File.separator + "downloads" + File.separator;
        File fileDown = new File(filePath);
        if (!fileDown.exists()) {
            fileDown.mkdirs();
        }
        return filePath;
    }

    private String getImportDumpPath() {
        String importDumpPath = jobConfig.getUploadLocation() + File.separator + "uploads" + File.separator;
        File fileUp = new File(importDumpPath);
        if (!fileUp.exists()) {
            fileUp.mkdirs();
        }
        return importDumpPath;
    }

    private boolean verifyImportFile(ParamDTO paramDTO, String importDumpPath) {
        // 下载[导入文件]到job所在服务器
        // operation -> 1:exp;2:imp
        if (paramDTO.getOperation().equals(DumpOperationType.IMP.getValue())) {
            return getImportFileError(paramDTO, importDumpPath);
        }
        return false;
    }

    private boolean getImportFileError(ParamDTO paramDTO, String importDumpPath) {

        try {

            String filename = "?filename=" + URLEncoder.encode(paramDTO.getDumpDownloadName());

            int ts = (int) (System.currentTimeMillis() / 1000);
            String tsUrl = "&ts=" + ts;

            String md5AuthToken = FileDownloadAuthUtil.buildDownloadAuthToken(ts, paramDTO.getDumpDownloadName());
            String tokenUrl = "&token=" + md5AuthToken;

            String url = jobConfig.getPath().getDcBackend() + "/api/v1/system/files/download" + filename + tsUrl + tokenUrl;

            if (StringUtils.isNotBlank(paramDTO.getDumpName()) && paramDTO.getDumpName().contains(" ")) {
                String dumpName = ts + "_" + paramDTO.getDumpName().replaceAll(" ", "");
                paramDTO.setDumpName(dumpName);
            }

            String impPath = FileUtil.downloadHttpUrl(url, importDumpPath, paramDTO.getDumpName());
            File impFile = new File(impPath);
            if (!impFile.exists() || 0 == impFile.length()) {
                if (paramDTO.getJobId() != null) {
                    dcJobMapper.update(paramDTO.getJobId(), SqlExecuteStatusType.fail.getValue());
                }
                if (paramDTO.getLogId() != 0L) {
                    jobLogMapper.updateTriggerStatus(paramDTO.getLogId(), SqlExecuteStatusType.fail.getValue(), new Date());
                }
                DCJobLogger.log("无法获得导入文件！");
                return true;
            }
        } catch (Exception e) {
            log.error("get import file error!", e);
            DCJobLogger.log("获得导入文件出错！");
            if (paramDTO.getJobId() != null) {
                dcJobMapper.update(paramDTO.getJobId(), SqlExecuteStatusType.fail.getValue());
            }
            if (paramDTO.getLogId() != 0L) {
                jobLogMapper.updateTriggerStatus(paramDTO.getLogId(), SqlExecuteStatusType.fail.getValue(), new Date());
            }
            return true;
        }

        return false;
    }

    private String getPassword(DatabaseConnectionDto instance, ParamDTO paramDTO) {
        String password = CipherUtils.sm4decrypt(instance.getPassword());
        if (DatabaseType.ORACLE.getValue().equals(paramDTO.getDbType())) {
            password = "\"" + password + "\"";
        }
        return password;
    }

    private String getOracleNlsCharacterSet(ParamDTO paramDTO, DatabaseConnectionDto instance) {
        String oracleNlsCharacterSet = "";
        if (DatabaseType.ORACLE.getValue().equals(paramDTO.getDbType())) {
            try {
                SqlListExecuteMessage executeMessage = new SqlListExecuteMessage();
                String schemaName = paramDTO.getSchema();
                executeMessage.setConnectionConfig(instance.buildConnectionConfig(schemaName, null));
                executeMessage.setSqlList(List.of("select userenv('language') as VALUE from dual",
                        "select 'alter table '||owner||'.'||'\"'||table_name||'\"'||' allocate extent' as ALTER_SQL from all_tables where (num_rows=0 or num_rows is null) and upper(owner)=upper('" + schemaName + "')"));

                Client proxyClient = Client.getClient(jobConfig.getPath().getDcIceage());
                List<RowsData> rowsDataList = proxyInternalClient.execute(proxyClient, executeMessage);

                oracleNlsCharacterSet = (String) rowsDataList.get(0).get(0).get("VALUE");

                List<String> alterSqlList = rowsDataList.get(1).stream().map(m -> (String) m.get("ALTER_SQL")).collect(Collectors.toList());

                executeMessage.setSqlList(alterSqlList);

                proxyInternalClient.execute(proxyClient, executeMessage);
            } catch (Exception e) {
                log.error("call proxy to get oracleNlsCharacterSet error!", e);
            }
        }
        return oracleNlsCharacterSet;
    }

    private StringBuilder buildTableStringBuilder(ParamDTO paramDTO) {
        StringBuilder tableStringBuilder = new StringBuilder();
        if (!paramDTO.isFullTable()) {
            if (DatabaseType.ORACLE.getValue().equals(paramDTO.getDbType())) {
                for (String table : paramDTO.getTables()) {
                    tableStringBuilder.append(paramDTO.getSchema()).append(".\"").append(table).append("\",");
                }
            } else if (Arrays.asList(DatabaseType.MYSQL.getValue(), DatabaseType.MARIA_DB.getValue(), DatabaseType.GOLDEN_DB.getValue()).contains(paramDTO.getDbType())) {
                for (String table : paramDTO.getTables()) {
                    tableStringBuilder.append(table).append(" ");
                }
                for (String view : paramDTO.getViews()) {
                    tableStringBuilder.append(view).append(" ");
                }
            }
        }
        return tableStringBuilder;
    }

    private String buildRows(ParamDTO paramDTO) {
        String rows;
        // 导出内容: 1:数据和结构;2:结构
        if (paramDTO.getExportContent().equals(DumpExportContentType.DATA_AND_STRUCTURE.getValue())) {
            rows = Arrays.asList(DatabaseType.MYSQL.getValue(), DatabaseType.MARIA_DB.getValue(), DatabaseType.GOLDEN_DB.getValue()).contains(paramDTO.getDbType()) ? "" : "rows=y";
        } else if (paramDTO.getExportContent().equals(DumpExportContentType.ONLY_THE_STRUCTURE.getValue())) {
            rows = Arrays.asList(DatabaseType.MYSQL.getValue(), DatabaseType.MARIA_DB.getValue(), DatabaseType.GOLDEN_DB.getValue()).contains(paramDTO.getDbType()) ? "--no-data" : "rows=n";
        } else {
            rows = "";
        }
        return rows;
    }

    private String buildUseTables(ParamDTO paramDTO, StringBuilder tableStringBuilder) {
        return DatabaseType.ORACLE.getValue().equals(paramDTO.getDbType()) || paramDTO.isFullTable()
                ? ""
                : paramDTO.isCreateSchema()
                ? "--tables " + tableStringBuilder.substring(0, tableStringBuilder.length() - 1)
                : "";
    }

    private String buildLastTables(ParamDTO paramDTO, StringBuilder tableStringBuilder) {
        return DatabaseType.ORACLE.getValue().equals(paramDTO.getDbType()) || paramDTO.isFullTable()
                ? ""
                : paramDTO.isCreateSchema()
                ? ""
                : tableStringBuilder.substring(0, tableStringBuilder.length() - 1);
    }

    private String buildPostGreSqlTables(ParamDTO paramDTO, String schemaName) {
        if (!paramDTO.isFullTable()) {
            StringBuilder tableStringBuilder = new StringBuilder();
            for (String table : paramDTO.getTables()) {
                tableStringBuilder.append("--table ").append(schemaName).append(".").append(table).append(" ");
            }
            for (String view : paramDTO.getViews()) {
                tableStringBuilder.append("--table ").append(schemaName).append(".").append(view).append(" ");
            }
            return tableStringBuilder.toString();
        }
        return "";
    }

    private String getOraclePrefix(ParamDTO paramDTO) {
        if (paramDTO.getOperation().equals(DumpOperationType.EXP.getValue())) {
            return "exp";
        } else if (paramDTO.getOperation().equals(DumpOperationType.IMP.getValue())) {
            return "imp";
        } else {
            return "";
        }
    }

    private String getOracleCmd(ParamDTO paramDTO, DatabaseConnectionDto instance, String filePath, String importDumpPath, String dumpName, String password) {
        String userName = instance.getUsername();
        String ip = instance.getIp();
        String port = instance.getPort();
        String serviceName = instance.getService_name();

        String compress = paramDTO.isCompress() ? "COMPRESS=y" : "COMPRESS=n";
        String grants = paramDTO.isAuthorization() ? "GRANTS=y" : "GRANTS=n";
        String consistent = paramDTO.isAgreement() ? "CONSISTENT=y" : "CONSISTENT=n";
        String indexes = paramDTO.isIndexes() ? "INDEXES=y" : "INDEXES=n";
        String constraints = paramDTO.isConstraint() ? "CONSTRAINTS=y" : "CONSTRAINTS=n";
        String direct = paramDTO.isDirect() ? "DIRECT=y" : "DIRECT=n";
        String triggers = paramDTO.isTrigger() ? "TRIGGERS=y" : "TRIGGERS=n";
        String ignore = paramDTO.isForce() ? "ignore=y" : "ignore=n";

        String rows = buildRows(paramDTO);
        StringBuilder tableStringBuilder = buildTableStringBuilder(paramDTO);

        if (paramDTO.getOperation().equals(DumpOperationType.EXP.getValue())) {
            if (paramDTO.isFullTable()) {
                return String.format("%s/%s@%s:%s/%s file=%s%s owner=%s log=%s%s %s %s %s %s %s %s %s %s",
                        userName, password, ip, port, serviceName,
                        filePath, dumpName, paramDTO.getSchema(),
                        filePath, paramDTO.getLogName(), compress, grants, consistent,
                        indexes, constraints, direct, triggers, rows);
            } else {
                return String.format("%s/%s@%s:%s/%s file=%s%s tables=%s log=%s%s %s %s %s %s %s %s %s %s",
                        userName, password, ip, port, serviceName,
                        filePath, dumpName, tableStringBuilder.substring(0, tableStringBuilder.length() - 1),
                        filePath, paramDTO.getLogName(), compress, grants, consistent,
                        indexes, constraints, direct, triggers, rows);
            }
        } else if (paramDTO.getOperation().equals(DumpOperationType.IMP.getValue())) {
            if (paramDTO.getSourceSchema() == null || paramDTO.getTargetSchema() == null
                    || paramDTO.getSourceSchema().isEmpty() || paramDTO.getTargetSchema().isEmpty()) {
                return String.format("%s/%s@%s:%s/%s file=%s%s full=y log=%s%s %s",
                        userName, password, ip, port, serviceName,
                        importDumpPath, dumpName,
                        importDumpPath, paramDTO.getLogName(), ignore);
            } else {
                return String.format("%s/%s@%s:%s/%s file=%s%s fromuser=%s touser=%s log=%s%s %s",
                        userName, password, ip, port, serviceName,
                        importDumpPath, dumpName, paramDTO.getSourceSchema(),
                        paramDTO.getTargetSchema(), importDumpPath, paramDTO.getLogName(), ignore);
            }
        } else {
            return "";
        }
    }

    private String getMysqlPrefix(ParamDTO paramDTO) {
        if (paramDTO.getOperation().equals(DumpOperationType.EXP.getValue())) {
            return "mysqldump";
        } else if (paramDTO.getOperation().equals(DumpOperationType.IMP.getValue())) {
            return "";
        } else {
            return "";
        }
    }

    private String getMysqlCmd(ParamDTO paramDTO, DatabaseConnectionDto instance, String filePath, String importDumpPath, String dumpName, String password) {
        String userName = instance.getUsername();
        String ip = instance.getIp();
        String port = (instance.getPort() == null || instance.getPort().length() == 0) ? "3306" : instance.getPort();

        String routines = paramDTO.isRoutines() ? "--routines" : "";
        String events = paramDTO.isEvents() ? "--events" : "";
        String createSchema = paramDTO.isCreateSchema() ? "" : "--no-create-db";
        String triggers = paramDTO.isTrigger() ? "--triggers" : "--skip-triggers";
        String ignore = paramDTO.isForce() ? "-f" : "";

        String rows = buildRows(paramDTO);
        StringBuilder tableStringBuilder = buildTableStringBuilder(paramDTO);

        String useSchema = paramDTO.isCreateSchema() ? "--databases " + paramDTO.getSchema() : "";
        String useTables = buildUseTables(paramDTO, tableStringBuilder);
        String lastSchema = paramDTO.isCreateSchema() ? "" : paramDTO.getSchema();
        String lastTables = buildLastTables(paramDTO, tableStringBuilder);

        if (paramDTO.getOperation().equals(DumpOperationType.EXP.getValue())) {
            String cmd = "";
            if (paramDTO.isFullTable()) {
                cmd = String.format("--column-statistics=0 --user=%s --password=%s --host=%s --port=%s %s %s %s %s %s " +
                                "--log-error=%s%s --verbose %s --result-file=%s%s --ssl-mode=DISABLED %s",
                        userName, password, ip, port, rows, routines, events, createSchema, triggers,
                        filePath, paramDTO.getLogName(), useSchema, filePath, dumpName, lastSchema);
                //判断是 goldendb 类型cmd 拼接 --set-gtid-purged=OFF
                if (DatabaseType.GOLDEN_DB.getValue().equals(paramDTO.getDbType())) {
                    cmd = cmd + " --set-gtid-purged=OFF";
                }
            } else {
                cmd = String.format("--column-statistics=0 --user=%s --password=%s --host=%s --port=%s %s %s %s %s %s " +
                                "--log-error=%s%s --verbose %s %s --result-file=%s%s --ssl-mode=DISABLED %s %s",
                        userName, password, ip, port, rows, routines, events, createSchema, triggers,
                        filePath, paramDTO.getLogName(), useSchema, useTables,
                        filePath, dumpName, lastSchema, lastTables);
                if (DatabaseType.GOLDEN_DB.getValue().equals(paramDTO.getDbType())) {
                    cmd = cmd + " --set-gtid-purged=OFF";
                }
            }
            return cmd;
        } else if (paramDTO.getOperation().equals(DumpOperationType.IMP.getValue())) {
            return String.format("mysql -u%s -p%s -h%s -P%s --ssl-mode=DISABLED --default-character-set=utf8 %s -v" +
                            " -D %s < %s'%s'",
                    userName, password, ip, port, ignore, paramDTO.getSchema(),
                    importDumpPath, dumpName);
        } else {
            return "";
        }
    }

    private String getPostGreSqlPrefix(ParamDTO paramDTO) {
        if (paramDTO.getOperation().equals(DumpOperationType.EXP.getValue())) {
            return "pg_dump";
        } else if (paramDTO.getOperation().equals(DumpOperationType.IMP.getValue())) {
            return "/usr/bin/psql";
        } else {
            return "";
        }
    }

    private String getPostGreSqlCmd(ParamDTO paramDTO, DatabaseConnectionDto instance, String filePath, String importDumpPath, String dumpName) {

        String ip = instance.getIp();
        String port = instance.getPort();
        String userName = instance.getUsername();

        String schemaOnly = paramDTO.getExportContent().equals(DumpExportContentType.ONLY_THE_STRUCTURE.getValue()) ? "--schema-only" : "";
        String noOwner = paramDTO.isNoOwner() ? "--no-owner" : ""; // 不包含所有者
        String noPrivileges = paramDTO.isNoPrivileges() ? "--no-privileges" : ""; // 不包含权限
        String inserts = paramDTO.isInserts() ? "--inserts" : ""; // 使用insert代替copy
        String create = paramDTO.isNoCreate() ? "" : "--create"; // 不包含create database
        String clean = paramDTO.isNoClean() ? "" : "--clean"; // 不包含drop database
        String ignore = paramDTO.isForce() ? "off" : "on";

        String catalogName = paramDTO.getCatalog();
        String schemaName = paramDTO.getSchema();
        String table = buildPostGreSqlTables(paramDTO, schemaName);

        if (paramDTO.getOperation().equals(DumpOperationType.EXP.getValue())) {
            return String.format("--file %s%s --host %s --port %s " +
                            "--username %s --no-password --format=p %s %s %s %s %s %s " +
                            "--verbose --schema %s %s %s",
                    filePath, dumpName, ip, port, userName, schemaOnly, noOwner, noPrivileges,
                    inserts, create, clean, schemaName, table, catalogName);
        } else if (paramDTO.getOperation().equals(DumpOperationType.IMP.getValue())) {
            return String.format("-U %s --no-password -d %s -h %s -p %s -v ON_ERROR_STOP=%s -f %s%s",
                    userName, catalogName, ip, port, ignore, importDumpPath, dumpName);
        } else {
            return "";
        }
    }

    private String verifyOracleVersion(ParamDTO paramDTO, String version) {
        String environmentHomePath = "";

        if (StringUtils.isNotBlank(version) && StringUtils.isNotBlank(jobConfig.getOracle())) {
            Map<String, String> oracleMap = JSON.parseObject(jobConfig.getOracle().replaceAll("'", "\""), new TypeReference<Map<String, String>>() {
            });
            for (Map.Entry<String, String> entry : oracleMap.entrySet()) {
                String versionRange = entry.getKey();
                String path = entry.getValue();
                String[] split = versionRange.split("-");
                String versionFrom = "";
                String versionTo = "";
                String versionSingle = "";
                if (split.length == 2) {
                    versionFrom = split[0];
                    versionTo = split[1];
                } else if (split.length == 1) {
                    versionSingle = split[0];
                }

                if (!versionFrom.isEmpty() && !versionTo.isEmpty()) {
                    if (versionFrom.compareTo(version) <= 0 && versionTo.compareTo(version) > 0) {
                        environmentHomePath = path;
                        break;
                    }
                } else if (!versionSingle.isEmpty()) {
                    if (versionSingle.compareTo(version) == 0) {
                        environmentHomePath = path;
                        break;
                    }
                }
            }
        } else {
            DCJobLogger.log("oracle version 为空！");
        }

        if (!environmentHomePath.isEmpty()) {
            if (!environmentHomePath.endsWith(File.separator)) {
                environmentHomePath = environmentHomePath + File.separator;
            }
        } else {
            DCJobLogger.log(String.format("当前oracle version [%s] 不支持导入导出！", version));
            if (paramDTO.getJobId() != null) {
                dcJobMapper.update(paramDTO.getJobId(), 3);
            }
            if (paramDTO.getLogId() != 0L) {
                jobLogMapper.updateTriggerStatus(paramDTO.getLogId(), 3, new Date());
            }
        }

        return environmentHomePath;
    }

    private void buildEnvironment(String environmentHomePath, String oracleNlsCharacterSet, ProcessBuilder processBuilder, ParamDTO paramDTO, String password) {
        Map<String, String> environment = processBuilder.environment();

        if (DatabaseType.ORACLE.getValue().equals(paramDTO.getDbType())) {
            if (!environmentHomePath.isEmpty()) {
                if (environmentHomePath.endsWith(File.separator)) {
                    environmentHomePath = environmentHomePath.substring(0, environmentHomePath.length() - 1);
                }

                environment.put("LD_LIBRARY_PATH", environmentHomePath + ":$LD_LIBRARY_PATH");
                DCJobLogger.log("\n" + environment.get("LD_LIBRARY_PATH"));

                if (!oracleNlsCharacterSet.isEmpty()) {
                    environment.put("NLS_LANG", oracleNlsCharacterSet);
                    DCJobLogger.log("\n" + environment.get("NLS_LANG"));
                }
            }
        } else if (Arrays.asList(DatabaseType.PG_SQL.getValue(), DatabaseType.RASE_SQL.getValue()).contains(paramDTO.getDbType())) {
            environment.put("PGPASSWORD", password);
        }
    }

    private Process buildProcess(ProcessBuilder processBuilder, ParamDTO paramDTO, String prefix, String cmd, String password) throws IOException {
        Process process;
        if (DatabaseType.ORACLE.getValue().equals(paramDTO.getDbType())) {
            process = processBuilder.start();
        } else if (Arrays.asList(DatabaseType.MYSQL.getValue(), DatabaseType.MARIA_DB.getValue(), DatabaseType.GOLDEN_DB.getValue()).contains(paramDTO.getDbType())) {
            if (paramDTO.getOperation().equals(DumpOperationType.EXP.getValue())) {
                process = Runtime.getRuntime().exec(prefix + " " + cmd);
            } else {
                String[] command = {"/bin/bash", "-c", cmd};
                process = Runtime.getRuntime().exec(command);
            }
        } else if (Arrays.asList(DatabaseType.PG_SQL.getValue(), DatabaseType.RASE_SQL.getValue()).contains(paramDTO.getDbType())) {
            if (paramDTO.getOperation().equals(DumpOperationType.EXP.getValue())) {
                String[] envp = {String.format("PGPASSWORD=%s", password)};
                process = Runtime.getRuntime().exec(prefix + " " + cmd, envp);
            } else {
                String[] command = {"/bin/bash", "-c", prefix + " " + cmd};
                String[] envp = {String.format("PGPASSWORD=%s", password)};
                process = Runtime.getRuntime().exec(command, envp);
            }
        } else {
            process = Runtime.getRuntime().exec(prefix + " " + cmd);
        }
        return process;
    }

    private String getCharset() {
        String osName = System.getProperty("os.name");
        if (osName.contains("Linux")) {
            return "UTF8";
        } else if (osName.contains("Windows")) {
            return "GBK";
        } else {
            return "UTF8";
        }
    }

    private boolean mySqlError(String line, ParamDTO paramDTO) {
        if (Arrays.asList(DatabaseType.MYSQL.getValue(), DatabaseType.MARIA_DB.getValue(), DatabaseType.GOLDEN_DB.getValue()).contains(paramDTO.getDbType())) {
            if (line.toUpperCase().contains("ERROR") && !line.contains("ERROR_FOR_DIVISION_BY_ZERO")) {
                String errorRegex = "\\bERROR\\b(?![^\\w\\s])"; // \b 表示单词边界，确保ERROR前后没有其他单词字符
                // 编译正则表达式模式
                Pattern pattern = Pattern.compile(errorRegex, Pattern.CASE_INSENSITIVE); // CASE_INSENSITIVE使匹配不区分大小写
                // 创建Matcher对象并尝试在logMessage中查找匹配项
                Matcher matcher = pattern.matcher(line.toUpperCase());
                if (matcher.find()) {
                    return true;
                }
            } else if (line.contains("Couldn't find table")) {
                return true;
            }
        }
        return false;
    }

    private int judgeStatus(StringBuilder messageBuilder, ParamDTO paramDTO, boolean mysqlError, String filePath) {
        if (DatabaseType.ORACLE.getValue().equals(paramDTO.getDbType())) {
            if (messageBuilder.toString().contains("FromUser")
                    && (messageBuilder.toString().contains("在导出文件中未找到")
                    || messageBuilder.toString().contains("not found in export file"))) {
                return SqlExecuteStatusType.fail.getValue();
            } else if (messageBuilder.toString().contains("is not a valid username")
                    || messageBuilder.toString().contains("未成功终止")
                    || messageBuilder.toString().contains("导出未成功终止")
                    || messageBuilder.toString().contains("未成功终止导入")) {
                return SqlExecuteStatusType.fail.getValue();
            } else if (messageBuilder.toString().contains("成功终止")
                    || messageBuilder.toString().contains("导出成功终止")
                    || messageBuilder.toString().contains("成功终止导入")
                    || messageBuilder.toString().contains("Export terminated successfully")
                    || messageBuilder.toString().contains("Import terminated successfully")) {
                return SqlExecuteStatusType.success.getValue();
            } else {
                return SqlExecuteStatusType.fail.getValue();
            }
        } else if (Arrays.asList(DatabaseType.MYSQL.getValue(), DatabaseType.MARIA_DB.getValue(), DatabaseType.GOLDEN_DB.getValue()).contains(paramDTO.getDbType())) {
            boolean errorFile = mysqlError || isErrorFile(filePath + paramDTO.getLogName());
            if (errorFile) {
                return SqlExecuteStatusType.fail.getValue();
            } else {
                return SqlExecuteStatusType.success.getValue();
            }
        } else if (Arrays.asList(DatabaseType.PG_SQL.getValue(), DatabaseType.RASE_SQL.getValue()).contains(paramDTO.getDbType())) {
            if (messageBuilder.toString().contains("pg_dump: aborting because of server version mismatch")
                    || messageBuilder.toString().contains("ERROR:")) {
                return SqlExecuteStatusType.fail.getValue();
            } else {
                return SqlExecuteStatusType.success.getValue();
            }
        }
        return SqlExecuteStatusType.running.getValue();
    }

    private boolean isErrorFile(String fileName) {
        boolean isError = false;
        File file = new File(fileName);
        if (!file.exists() || 0 == file.length()) {
            log.info("mysql导入导出logFile为空");
            return false;
        }
        BufferedReader reader = null;
        try {
            reader = new BufferedReader(new FileReader(file));
            String tempString = null;
            while ((tempString = reader.readLine()) != null) {
                DCJobLogger.log(tempString);
                if (tempString.toUpperCase().contains("ERROR") && !tempString.contains("ERROR_FOR_DIVISION_BY_ZERO")) {
                    String errorRegex = "\\bERROR\\b(?![^\\w\\s])"; // \b 表示单词边界，确保ERROR前后没有其他单词字符
                    // 编译正则表达式模式
                    Pattern pattern = Pattern.compile(errorRegex, Pattern.CASE_INSENSITIVE); // CASE_INSENSITIVE使匹配不区分大小写
                    // 创建Matcher对象并尝试在logMessage中查找匹配项
                    Matcher matcher = pattern.matcher(tempString.toUpperCase());
                    if (matcher.find()) {
                        return true;
                    }
                } else if (tempString.contains("Couldn't find table")) {
                    isError = true;
                }
            }
            reader.close();
        } catch (Exception e) {
            log.error("read log in isErrorFile function error!", e);
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (Exception e) {
                    log.error("close reader in isErrorFile function error!", e);
                }
            }
        }
        return isError;

    }

    private void updateFinishStatus(ParamDTO paramDTO, int status) {
        if (paramDTO.getJobId() != null) {
            dcJobMapper.update(paramDTO.getJobId(), status);
        }
        if (paramDTO.getLogId() != 0L) {
            jobLogMapper.updateTriggerStatus(paramDTO.getLogId(), status, new Date());
            if (SqlExecuteStatusType.success.getValue().equals(status)) {
                jobLogMapper.updateFileDownloadLink(paramDTO.getLogId(), paramDTO.getDumpName());
                DCJobLogger.schedulingSuccessful();
            } else if (SqlExecuteStatusType.fail.getValue().equals(status)) {
                DCJobLogger.schedulingFailed();
            }
        }
    }

    private void handleDumpJobError(ParamDTO paramDTO) {
        try {
            if (paramDTO.getJobId() != null) {
                dcJobMapper.update(paramDTO.getJobId(), SqlExecuteStatusType.fail.getValue());
            }
            if (paramDTO.getLogId() != 0L) {
                jobLogMapper.updateTriggerStatus(paramDTO.getLogId(), SqlExecuteStatusType.fail.getValue(), new Date());
            }
        } catch (Exception e) {
            log.error("update dcJob/jobLog table status error!", e);
            DCJobLogger.log("连接数据库出错: " + e);
        }
    }

    @AllArgsConstructor
    enum DumpOperationType {
        EXP(1, "导出"),
        IMP(2, "导入"),
        ;

        final Integer value;
        final String name;

        public Integer getValue() {
            return value;
        }
    }

    @AllArgsConstructor
    enum DumpExportContentType {
        DATA_AND_STRUCTURE(1, "数据和结构"),
        ONLY_THE_STRUCTURE(2, "只有结构"),
        ;

        final Integer value;
        final String name;

        public Integer getValue() {
            return value;
        }
    }

}
