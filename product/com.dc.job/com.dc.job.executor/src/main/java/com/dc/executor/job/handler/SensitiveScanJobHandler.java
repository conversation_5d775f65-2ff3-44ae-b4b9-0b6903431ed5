package com.dc.executor.job.handler;

import com.dc.executor.util.JobCommonUtil;
import com.dc.job.biz.model.ReturnT;
import com.dc.job.enums.SqlExecuteStatusType;
import com.dc.job.handler.annotation.XxlJob;
import com.dc.repository.mysql.model.JobLog;
import com.dc.utils.http.HttpClientUtils;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.StringEntity;
import org.springframework.stereotype.Component;


@Slf4j
@Component
public class SensitiveScanJobHandler extends AbstractJobHandler {

    @XxlJob("SensitiveScanJobHandler")
    public ReturnT<String> userSynchronizeJobHandler(String param) throws Exception {
        try {
            log.info("Start Sensitive Scan");

            JsonNode jsonNode = json.getObjectMapper().readTree(param);
            JsonNode isRepeat = jsonNode.get("is_repeat");
            JsonNode logId = jsonNode.get("log_id");
            JsonNode repeatType = jsonNode.get("repeat_type");
            JsonNode dailyStrategy = jsonNode.get("daily_strategy");

            if (JobCommonUtil.needSkipBasedOnStrategy(repeatType == null ? null : repeatType.asInt(), dailyStrategy == null ? null : dailyStrategy.asText(), jobConfig.getPath().getDcBackend())) {
                return ReturnT.SUCCESS;
            }

            // 获取调度记录
            JobLog load = jobLogMapper.load(logId.asLong());

            // 更新触发时间为真正得执行任务的时间
            this.updateTriggerTime(logId.asLong());

            // 更新状态为执行中
            this.updateBeginTriggerStatus(load.getJobId(), logId.asLong());

            HttpClientUtils.doPost(jobConfig.getPath().getDcBackend() + "/api/v1/security/sensitive-distinguish-tasks/scan", new StringEntity(param));

            this.updateFinalStatus(SqlExecuteStatusType.success, logId.asLong(), isRepeat.asBoolean(), load.getJobId()); // 更新最终状态

        } catch (Exception e) {
            log.error("call sensitiveScanJobHandler error!", e);
        }
        return ReturnT.SUCCESS;
    }

}
