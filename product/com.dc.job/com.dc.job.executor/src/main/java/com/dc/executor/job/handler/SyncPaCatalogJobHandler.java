package com.dc.executor.job.handler;

import com.dc.executor.config.JobConfig;
import com.dc.job.biz.model.ReturnT;
import com.dc.job.handler.annotation.XxlJob;
import com.dc.utils.http.HttpClientUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component(value = "JOBSPL")
public class SyncPaCatalogJobHandler {

    private static Logger logger = LoggerFactory.getLogger(SyncPaCatalogJobHandler.class);

    @Resource
    private JobConfig jobConfig;

    private static final String summerInterfaceSyncPaCatalog = "/dc-summer/pa/instance/sync-buss";

    @XxlJob("SyncPaCatalogJobHandler")
    public ReturnT<String> syncPaCatalogJobHandler(String param) throws Exception {
        ReturnT<String> result = ReturnT.SUCCESS;
        try {
            logger.info("Start paCatalog Synchronize");
            HttpClientUtils.doGet(jobConfig.getPath().getDcSummer() + summerInterfaceSyncPaCatalog, null);
        } catch (Exception e) {
            logger.error("call SyncPaCatalogJobHandler error!", e);
        }
        return result;
    }
}
