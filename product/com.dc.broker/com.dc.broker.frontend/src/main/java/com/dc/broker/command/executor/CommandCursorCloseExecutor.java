package com.dc.broker.command.executor;

import com.dc.broker.command.model.CommandCursorCreateMessage;
import com.dc.broker.command.model.CommandSingleMessage;
import com.dc.broker.protocol.MessageBase;
import com.dc.broker.session.BrokerConnectionContext;
import com.dc.broker.sql.BrokerSQLCursor;
import com.dc.springboot.core.model.data.Result;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class CommandCursorCloseExecutor extends AbstractCommandExecutor {

    private final MessageBase.Message message;

    private final BrokerConnectionContext connectionContext;

    @Override
    public MessageBase.Message execute() {
        CommandSingleMessage commandSingleMessage = gson.fromJson(message.getContent(), CommandSingleMessage.class);
        return this.boolResponse(message, "close cursor failed!",
                () -> connectionContext.getStatementProcessor().closeSQLCursor(commandSingleMessage.getParam().toString()));
    }

}
