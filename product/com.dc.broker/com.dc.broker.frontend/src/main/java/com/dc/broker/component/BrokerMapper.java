package com.dc.broker.component;

import com.dc.repository.mysql.model.DatabaseConnection;
import com.dc.repository.redis.model.EnvConnection;
import com.dc.repository.redis.model.EnvSchema;
import com.dc.repository.redis.model.EnvUser;
import com.dc.springboot.core.model.execution.ValidExecuteModel;
import com.dc.springboot.core.model.log.SensitiveAuthDetail;
import com.dc.springboot.core.model.log.SqlHistory;
import com.dc.springboot.core.model.parser.dto.DatabaseConnectionDto;
import com.dc.springboot.core.model.sensitive.DataMask;
import com.dc.type.DatabaseType;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface BrokerMapper {

    BrokerMapper INSTANCE = Mappers.getMapper(BrokerMapper.class);

    DatabaseConnectionDto toDatabaseConnectionDto(DatabaseConnection connection);

    @Mapping(target = "orderCode", ignore = true)
    @Mapping(target = "orderRelevance", ignore = true)
    @Mapping(target = "message", ignore = true)
    @Mapping(target = "recordStatus", ignore = true)
    @Mapping(target = "approvalUser", ignore = true)
    @Mapping(target = "organizationId", expression = "java(new java.util.ArrayList(java.util.List.of(user.getOrgIds().split(\",\"))))")
    @Mapping(target = "groupByTable", expression = "java(validExecuteModel.getSqlId()+\"^\"+schema.getSchemaId()+\"^\"+dataMask.getTableName())")
    @Mapping(target = "authTraceData", expression = "java(validExecuteModel.getSqlRecord().getAuthTraceData())")
    @Mapping(target = "isDelete", expression = "java(0L)")
    @Mapping(target = "gmtCreate", expression = "java(System.currentTimeMillis())")
    @Mapping(target = "gmtModified", expression = "java(sensitiveAuthDetail.getGmtCreate())")
    @Mapping(target = "originZh", expression = "java(com.dc.springboot.core.model.type.OriginType.of(validExecuteModel.getOrigin()).getName())")
    @Mapping(target = "name", expression = "java(user.getOperationUser())")
    @Mapping(target = "enableDesensitizeType", source = "validExecuteModel.sqlDesensitization.enableDesensitizeType")
    @Mapping(target = "sql", source = "validExecuteModel.sql")
    @Mapping(target = "schemaName", source = "schema.concatSchemaName")
    @Mapping(target = "schemaId", source = "schema.schemaId")
    @Mapping(target = "userId", source = "user.userId")
    @Mapping(target = "tableName", source = "dataMask.showTableName")
    @Mapping(target = "dbType", source = "databaseType.value")
    @Mapping(target = "environment", source = "connection.environment")
    @Mapping(target = "connectionDesc", source = "connection.connectionDesc")
    @Mapping(target = "instanceName", source = "connection.instanceName")
    SensitiveAuthDetail toSensitiveAuthDetail(DataMask dataMask,
                                              EnvUser user,
                                              EnvSchema schema,
                                              EnvConnection connection,
                                              ValidExecuteModel validExecuteModel,
                                              DatabaseType databaseType);

    @Mapping(target = "originZh", ignore = true)
    @Mapping(target = "windowId", ignore = true)
    @Mapping(target = "type", ignore = true)
    @Mapping(target = "timestamp", ignore = true)
    @Mapping(target = "taskId", ignore = true)
    @Mapping(target = "sessionId", ignore = true)
    @Mapping(target = "scriptId", ignore = true)
    @Mapping(target = "comment", expression = "java(\"\")")
    @Mapping(target = "organizationId", expression = "java(java.util.List.of(user.getOrgIds().split(\",\")))")
    @Mapping(target = "gmtCreate", expression = "java(System.currentTimeMillis())")
    @Mapping(target = "gmtModified", expression = "java(sqlHistory.getGmtCreate())")
    @Mapping(target = "name", expression = "java(user.getOperationUser())")
    @Mapping(target = "isDelete", expression = "java(0L)")
    @Mapping(target = "orderRelevance", source = "orderRelevance")
    @Mapping(target = "connectUser", source = "connection.userName")
    @Mapping(target = "authTraceData", source = "validExecuteModel.sqlRecord.authTraceData")
    @Mapping(target = "connectId", source = "connection.connectionId")
    @Mapping(target = "connectDesc", source = "connection.connectionDesc")
    @Mapping(target = "records", source = "validExecuteModel.sqlRecord")
    @Mapping(target = "dbTypeZh", source = "databaseType.name")
    @Mapping(target = "dbType", source = "databaseType.value")
    @Mapping(target = "userId", source = "user.userId")
    @Mapping(target = "schemaName", expression = "java(schema.getCatalogName() != null && schema.getCatalogName() != \"\" ? String.format(\"%s.%s\", schema.getCatalogName(), schema.getSchemaName()) : schema.getSchemaName())")
    @Mapping(target = "schemaId", source = "schema.schemaId")
    SqlHistory toSqlHistory(EnvUser user,
                            EnvSchema schema,
                            EnvConnection connection,
                            ValidExecuteModel validExecuteModel,
                            DatabaseType databaseType,
                            String orderRelevance);

    @Mapping(target = "sql", source = "sqlHistory.records.sql")
    @Mapping(target = "message", source = "sqlHistory.records.message")
    @Mapping(target = "orderCode", source = "sqlHistory.records.orderCode")
    @Mapping(target = "enableDesensitizeType", source = "sqlHistory.records.enableDesensitizeType")
    @Mapping(target = "approvalUser", source = "sqlHistory.records.approvalUser")
    @Mapping(target = "recordStatus", source = "sqlHistory.records.recordStatus")
    @Mapping(target = "sensitiveLevelName", ignore = true)
    @Mapping(target = "sensitiveLevelId", ignore = true)
    @Mapping(target = "sensitiveLevelColor", ignore = true)
    @Mapping(target = "groupByTable", ignore = true)
    @Mapping(target = "distinguishRuleName", ignore = true)
    @Mapping(target = "distinguishRuleId", ignore = true)
    @Mapping(target = "desensitizeRuleName", ignore = true)
    @Mapping(target = "desensitizeRuleId", ignore = true)
    @Mapping(target = "connectionDesc", ignore = true)
    @Mapping(target = "columnName", ignore = true)
    @Mapping(target = "authSensitive", ignore = true)
    @Mapping(target = "tableName", ignore = true)
    void updateSensitiveAuthDetail(@MappingTarget SensitiveAuthDetail sensitiveAuthDetail,
                                   SqlHistory sqlHistory);

}
