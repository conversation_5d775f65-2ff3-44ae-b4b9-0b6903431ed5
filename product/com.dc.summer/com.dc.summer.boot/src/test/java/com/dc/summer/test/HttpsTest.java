package com.dc.summer.test;


import com.dc.SummerPeApplication;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

@SpringBootTest(classes = SummerPeApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class HttpsTest {

    @Resource
    private RestTemplate restsTemplate;

    @Test
    void testHttps() throws Exception {
        ResponseEntity<String> forEntity = restsTemplate.getForEntity("https://www.baidu.com/s?wd=测试https网站&rsv_spt=1&rsv_iqid=0x86bd8929000aeae8&issp=1&f=8&rsv_bp=1&rsv_idx=2&ie=utf-8&rqlang=cn&tn=baiduhome_pg&rsv_enter=0&rsv_dl=tb&rsv_btype=t&rsv_t=4b85Pu%2BlBMi4Trgj7CQMlebJ7PndknR834vd8aGZRnTn5A6Vdpgb8EU0nm0E3Fl7iCVJ&oq=%25E6%25B5%258B%25E8%25AF%2595https%25E7%25BD%2591%25E7%25AB%2599&rsv_pq=def855600017b4e0",
                String.class);
        System.out.println(forEntity.getStatusCode());
        System.out.println(forEntity.getBody());
    }

}
