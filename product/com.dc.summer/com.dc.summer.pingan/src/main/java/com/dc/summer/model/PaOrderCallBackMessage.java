package com.dc.summer.model;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.LowerCamelCaseStrategy.class) /*平安回调对方是驼峰参数*/
@ToString(callSuper = true)
public class PaOrderCallBackMessage implements Serializable {

    /* 签报ID， 关联平台工单ID */
    @NotBlank(message = "签报ID不能为空")
    private String taskId;

    /* 签报状态 1:审批中、2：完成、3：作废、4：第三方签报退回修改*/
    @NotBlank(message = "签报状态不能为空")
    private String taskStatus;  //注意：这个状态审批不通过也是完成状态,不能用来判断工单状态

    /* 签报流程详情， 签报所有审批步骤*/
    @Valid
    @NotNull(message = "签报流程详情不能为空")
    private List<PaOrderFlowResult> taskDetail;

}
