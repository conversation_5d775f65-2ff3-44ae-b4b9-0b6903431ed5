package com.dc.summer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dc.repository.mysql.column.OrderApplyContent;
import com.dc.repository.mysql.mapper.DatabaseConnectionMapper;
import com.dc.repository.mysql.mapper.DcWorkOrderMapper;
import com.dc.repository.mysql.mapper.SchemaMapper;
import com.dc.repository.mysql.mapper.UserMapper;
import com.dc.repository.mysql.model.DatabaseConnection;
import com.dc.repository.mysql.model.Order;
import com.dc.repository.mysql.model.Schema;
import com.dc.repository.mysql.model.User;
import com.dc.summer.model.PaOrderEditSensColumn;
import com.dc.summer.model.WorkFlowCallbackMessage;
import com.dc.summer.service.PaOrderBiz;
import com.dc.springboot.core.model.type.OrderCurrentStatus;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.pingan.cdsf.driver.bridger.dto.*;
import com.pingan.cdsf.driver.bridger.service.EditSensTypeSdkService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service("after_dc_err_des_feedback_eoa_order")
public class PaSubmitEditSensTypeOrderService implements PaOrderBiz {

    private final Gson gson = new GsonBuilder().serializeNulls().create();

    @Resource
    DcWorkOrderMapper wkOrderMapper;

    @Resource
    UserMapper userMapper;

    @Resource
    DatabaseConnectionMapper connectionMapper;

    @Resource
    SchemaMapper schemaMapper;

    @Override
    public Object workflowCallback(WorkFlowCallbackMessage message) {

        try {
            log.info("workflow callback :: " + gson.toJson(message));
            if (OrderCurrentStatus.SUCCESS.getValue() != message.getStatus()) {
                log.info("误脱敏反馈工单未通过,orderId:" + message.getOrderId());
                return null;
            }

            Order order = wkOrderMapper.getById(message.getOrderId());
            if (ObjectUtils.isEmpty(order)) {
                //throw new ServerException("order not found.");
                log.error("order not found.");
                return null;
            }

            User user = userMapper.selectOne(new QueryWrapper<User>().lambda()
                    .eq(User::getUniqueKey, order.getApply_user()));
            if (ObjectUtils.isEmpty(user)) {
                //throw new ServerException("user not found.");
                log.error("user not found.");
                return null;
            }

            OrderApplyContent applyContent = order.getApply_content();
            if (ObjectUtils.isEmpty(applyContent)) {
                log.error("applyContent isEmpty.");
                return null;
            }

            String connectId = applyContent.getConnect_id();
            DatabaseConnection connection = connectionMapper.getConnectionByUniqueKey(connectId);
            if (ObjectUtils.isEmpty(connection)) {
                //throw new ServerException("connection not found.");
                log.error("connection not found.");
                return null;
            }

            //从工单内容中获取
            String jsonStr = gson.toJson(order.getApply_content().getFields());
            List<PaOrderEditSensColumn> fields = gson.fromJson(jsonStr, new TypeToken<List<PaOrderEditSensColumn>>() {
            }.getType());
            Set<String> schemaIdList = fields.stream().map(PaOrderEditSensColumn::getSchema_id).collect(Collectors.toSet());
            List<Schema> schemas = schemaMapper.selectList(new QueryWrapper<Schema>().lambda()
                    .in(Schema::getUnique_key, schemaIdList)
                    .eq(Schema::getIs_delete, 0));

            Map<String, String> schemaNameMap = null;
            if (null != schemas && schemas.size() > 0) {
                schemaNameMap = schemas.stream().collect(Collectors.toMap(Schema::getUnique_key, Schema::getSchema_name));
            }

            //目前页面设计只只是单数据库选择
            Long dbId = null;
            List<EditSensTypeColumnDto> columnList = new ArrayList<>();
            for (PaOrderEditSensColumn field : fields) {
                dbId = field.getDb_id();
                if (null == dbId) {
                    log.error("dbId isEmpty");
                    continue;
                }

                if (null != schemaNameMap && schemaNameMap.containsKey(field.getSchema_id())) {
                    String schemaName = schemaNameMap.get(field.getSchema_id());
                    List<Integer> list = List.of(field.getSens_type_list());
                    if (ObjectUtils.isEmpty(list)) {
                        continue;
                    }
                    EditSensTypeColumnDto column = new EditSensTypeColumnDto();
                    column.setColumnName(field.getColumn_name());
                    column.setSchemaName(schemaName);
                    column.setTableName(field.getTable_name());
                    column.setColumnId(field.getColumn_id());
                    column.setSensTypeList(list);
                    columnList.add(column);
                }
            }

            if (ObjectUtils.isEmpty(dbId)) {
                log.error("fetch pa sdk dbId not found.");
                return null;
            }

            EditSensTypeDbDto dbInfo = new EditSensTypeDbDto();
            dbInfo.setDbId(dbId);
            //非必填參數
//            dbInfo.setSid(connection.getSync());
//            dbInfo.setDbHost(connection.getIp());
//            dbInfo.setDbName(connection.getDb_name());
//            dbInfo.setDbPort(Integer.valueOf(connection.getPort()));
//            dbInfo.setDbType(DatabaseType.of(connection.getDb_type()).getName());

            EditSensTypeParam param = new EditSensTypeParam();
            param.setDbInfo(dbInfo);
            param.setColumnList(columnList);
            param.setUmCode(user.getUsername().toUpperCase(Locale.ROOT));
            param.setRemark(order.getApply_reason());


            EditSensTypeSdkService service = com.dc.springboot.core.component.Resource.getBean(EditSensTypeSdkService.class);
            service.submitEditSensType(param);
            log.info("submit EditSensType done.");
        } catch (Exception e) {
            log.error("误脱敏反馈调用失败：" + e.getMessage());
        }
        return null;
    }
}

