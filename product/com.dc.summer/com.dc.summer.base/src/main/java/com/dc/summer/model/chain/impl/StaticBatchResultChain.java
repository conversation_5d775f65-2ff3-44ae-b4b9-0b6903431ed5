package com.dc.summer.model.chain.impl;

import com.dc.springboot.core.component.Resource;
import com.dc.springboot.core.model.result.WebSQLExecuteInfo;
import com.dc.springboot.core.model.result.WebSQLQueryResult;
import com.dc.springboot.core.model.chain.AbstractChain;
import com.dc.summer.model.data.WebAsyncTaskInfo;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.service.ResultService;
import com.dc.summer.service.sql.WebSQLContextInfo;

import java.util.concurrent.atomic.AtomicInteger;

public class StaticBatchResultChain extends AbstractChain<WebSQLQueryResult> {

    private final WebAsyncTaskInfo asyncTaskInfo;

    private final ResultService resultService;

    private final WebSQLContextInfo contextInfo;

    private final DBRProgressMonitor monitor;

    private final AtomicInteger index = new AtomicInteger(0);

    public StaticBatchResultChain(WebAsyncTaskInfo asyncTaskInfo, WebSQLContextInfo contextInfo, DBRProgressMonitor monitor) {
        this.asyncTaskInfo = asyncTaskInfo;
        this.contextInfo = contextInfo;
        this.monitor = monitor;
        this.resultService = Resource.getBean(ResultService.class);
    }

    @Override
    public boolean proceed(WebSQLQueryResult queryResult) {
        if (queryResult.isConfirm()) {
            // 批量执行需要向 redis 中存放 stage 为 id 的结果集
            if (asyncTaskInfo.getStage() != null) {
                queryResult.setSqlIndex(asyncTaskInfo.getStage());
            }
            WebSQLExecuteInfo executeInfo = new WebSQLExecuteInfo().addQueryResult(queryResult);
            resultService.addListValue(asyncTaskInfo.getTaskId(), executeInfo, contextInfo.getExpirationTime(), index.get());
            monitor.subTask(WebSQLContextInfo.REDIS_LIST_ID + index.incrementAndGet());
        }
        return false;
    }

}
