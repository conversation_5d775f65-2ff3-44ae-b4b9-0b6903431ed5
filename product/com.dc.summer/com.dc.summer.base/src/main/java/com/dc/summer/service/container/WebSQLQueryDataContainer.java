
package com.dc.summer.service.container;

import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.Log;
import com.dc.summer.model.DBPContextProvider;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.data.DBDDataFilter;
import com.dc.summer.model.data.DBDDataReceiver;
import com.dc.summer.model.exec.*;
import com.dc.summer.model.sql.SQLQuery;
import com.dc.summer.model.sql.SQLQueryContainer;
import com.dc.summer.model.sql.SQLScriptContext;
import com.dc.summer.model.sql.data.SQLQueryDataContainer;
import com.dc.summer.model.struct.DBSDataContainer;
import com.dc.summer.model.struct.DBSObject;
import lombok.Getter;
import org.eclipse.core.runtime.IAdaptable;

import java.io.PrintWriter;
import java.util.List;

/**
 * Web SQL query data container.
 */
public class WebSQLQueryDataContainer implements DBSDataContainer, DBPContextProvider, IAdaptable {

    private static final Log log = Log.getLog(WebSQLQueryDataContainer.class);

    private final DBPDataSource dataSource;
    private final String query;
    @Getter
    private final SQLQueryDataContainer queryDataContainer;
    private final DBCExecutionContext context;


    public WebSQLQueryDataContainer(DBPDataSource dataSource,
                                    DBCExecutionContext context,
                                    String query,
                                    long total,
                                    String tableName) {
        this.dataSource = dataSource;
        this.query = query;
        this.context = context;
        SQLScriptContext scriptContext = new SQLScriptContext(null,
                this, null, new PrintWriter(System.err, true), null);
        queryDataContainer = new SQLQueryDataContainer(this, new SQLQuery(dataSource, query), scriptContext, log, total, tableName);
    }

    @Nullable
    @Override
    public DBSObject getParentObject() {
        return dataSource;
    }

    @NotNull
    @Override
    public String getName() {
        return query;
    }



    @Override
    public boolean isPersisted() {
        return false;
    }

    @Nullable
    @Override
    public String getDescription() {
        return queryDataContainer.getDescription();
    }

    @Nullable
    @Override
    public DBPDataSource getDataSource() {
        return dataSource;
    }

    @Override
    public String[] getSupportedFeatures() {
        return queryDataContainer.getSupportedFeatures();
    }

    @NotNull
    @Override
    public DBCStatistics readData(@NotNull DBCExecutionSource source, @NotNull DBCSession session, @NotNull DBDDataReceiver dataReceiver, @Nullable DBDDataFilter dataFilter, long firstRow, long maxRows, long flags, int fetchSize, List<Object> data) throws DBCException {
        return queryDataContainer.readData(source, session, dataReceiver, dataFilter, firstRow, maxRows, flags, fetchSize, data);
    }

    @Override
    public DBCStatistics readData(DBCExecutionSource source, DBCSession session, DBDDataReceiver dataReceiver, DBDDataFilter dataFilter, long firstRow, long maxRows, long flags, int fetchSize, int stage, List<Object> data) throws DBCException {
        return queryDataContainer.readData(source, session, dataReceiver, dataFilter, firstRow, maxRows, flags, stage, fetchSize, data);
    }

    @Override
    public long countData(@NotNull DBCExecutionSource source, @NotNull DBCSession session, @Nullable DBDDataFilter dataFilter, long flags) throws DBCException {
        return queryDataContainer.countData(source, session, dataFilter, flags);
    }

    @Nullable
    @Override
    public DBCExecutionContext getExecutionContext() {
        return context;
    }

    @SuppressWarnings("unchecked")
    @Override
    public <T> T getAdapter(Class<T> clazz) {
        if (clazz == SQLQueryContainer.class) {
            return (T) queryDataContainer;
        }
        return null;
    }
}
