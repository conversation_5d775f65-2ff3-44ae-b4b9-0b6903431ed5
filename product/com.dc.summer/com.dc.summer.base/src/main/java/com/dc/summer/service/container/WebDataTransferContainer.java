package com.dc.summer.service.container;

import com.dc.summer.Log;
import com.dc.springboot.core.model.result.WebSQLQueryResultSet;
import com.dc.springboot.core.model.result.WebSQLResultsInfo;
import com.dc.summer.model.exec.*;
import com.dc.summer.service.transfer.WebDataTransferResultSet;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.data.DBDDataFilter;
import com.dc.summer.model.data.DBDDataReceiver;
import com.dc.summer.model.sql.SQLQuery;
import com.dc.summer.model.sql.SQLQueryContainer;
import com.dc.summer.model.sql.data.SQLQueryDataContainer;
import com.dc.summer.model.struct.DBSDataContainer;
import com.dc.summer.model.struct.DBSObject;
import org.eclipse.core.runtime.IAdaptable;

import java.util.List;

public class WebDataTransferContainer implements DBSDataContainer, IAdaptable {

    private static final Log log = Log.getLog(WebDataTransferContainer.class);

    private final WebSQLQueryResultSet webSQLQueryResultSet;
    private final WebDataTransferResultSet webDataTransferResultSet;

    private final DBPDataSource dataSource;
    private final SQLQueryDataContainer queryDataContainer;


    public WebDataTransferContainer(DBCExecutionContext context, WebSQLQueryResultSet webSQLQueryResultSet, WebSQLResultsInfo resultsInfo, String tableName) {
        this.webSQLQueryResultSet = webSQLQueryResultSet;
        this.dataSource = resultsInfo.getDataContainer().getDataSource();
        this.webDataTransferResultSet = new WebDataTransferResultSet(webSQLQueryResultSet, resultsInfo.getAttributes(), this.dataSource);
        queryDataContainer = new SQLQueryDataContainer(() -> context,
                new SQLQuery(dataSource, resultsInfo.getDataContainer().getName()),
                null,
                log,
                webSQLQueryResultSet.getColumns().length,
                tableName);
    }

    @Override
    public String getName() {
        return webSQLQueryResultSet.getResultId();
    }

    @Override
    public String getDescription() {
        return "Transfer Container";
    }

    @Override
    public boolean isPersisted() {
        return false;
    }

    @Override
    public DBSObject getParentObject() {
        return this.getDataSource();
    }

    @Override
    public DBPDataSource getDataSource() {
        return dataSource;
    }

    @Override
    public String[] getSupportedFeatures() {
        return new String[]{DBSDataContainer.FEATURE_DATA_SELECT, DBSDataContainer.FEATURE_DATA_COUNT};
    }

    @Override
    public DBCStatistics readData(DBCExecutionSource source, DBCSession session, DBDDataReceiver dataReceiver, DBDDataFilter dataFilter, long firstRow, long maxRows, long flags, int fetchSize, int stage, List<Object> data) throws DBCException {
        if (webSQLQueryResultSet != null) {
            dataReceiver.fetchStart(session, webDataTransferResultSet, firstRow, maxRows);

            long exportLimit = webSQLQueryResultSet.getExportRowsLimit();
            long times = exportLimit; //次数
            while ((exportLimit == 0 || times-- > 0) && webDataTransferResultSet.nextRow()) {
                dataReceiver.fetchRow(session, webDataTransferResultSet);
            }
            if (webDataTransferResultSet.nextRow()) {
                webSQLQueryResultSet.setExportLimit(true);
            }
            dataReceiver.fetchEnd(session, webDataTransferResultSet);
        }
        return new DBCStatistics();
    }

    @Override
    public long countData(DBCExecutionSource source, DBCSession session, DBDDataFilter dataFilter, long flags) throws DBCException {
        return 0;
    }

    @SuppressWarnings("unchecked")
    @Override
    public <T> T getAdapter(Class<T> clazz) {
        if (clazz == SQLQueryContainer.class) {
            return (T) queryDataContainer;
        }
        return null;
    }
}
