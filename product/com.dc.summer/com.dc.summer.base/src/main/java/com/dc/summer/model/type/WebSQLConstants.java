
package com.dc.summer.model.type;

import java.util.Arrays;
import java.util.List;

/**
 * Web SQL constants.
 */
public class WebSQLConstants {

    public static final String QUOTA_PROP_ROW_LIMIT = "sqlResultSetRowsLimit";
    public static final String QUOTA_PROP_MEMORY_LIMIT = "sqlResultSetMemoryLimit";
    public static final String QUOTA_PROP_QUERY_LIMIT = "sqlMaxRunningQueries";
    public static final String QUOTA_PROP_SQL_QUERY_TIMEOUT = "sqlQueryTimeout";
    public static final String QUOTA_PROP_TEXT_PREVIEW_MAX_LENGTH = "sqlTextPreviewMaxLength";
    public static final String QUOTA_PROP_BINARY_PREVIEW_MAX_LENGTH = "sqlBinaryPreviewMaxLength";
    public static final String QUOTA_PROP_RM_FILE_SIZE_LIMIT = "resourceManagerFileSizeLimit";

    public static final int TEXT_PREVIEW_MAX_LENGTH = 4 * 1024;
    public static final int BINARY_PREVIEW_MAX_LENGTH = 255 * 1024;

    public static final String VALUE_TYPE_ATTR = "$type";

    public static final String VALUE_TYPE_COLLECTION = "collection";
    public static final String VALUE_TYPE_MAP = "map";
    public static final String VALUE_TYPE_DOCUMENT = "document";
    public static final String VALUE_TYPE_CONTENT = "content";
    public static final String VALUE_TYPE_GEOMETRY = "geometry";

    public static final String ATTR_TEXT = "text";
    public static final String ATTR_COLUMN_TYPE = "column_type";
    public static final String ATTR_BINARY = "binary";
    public static final String ATTR_DATA = "data";
    public static final String ATTR_SRID = "srid";
    public static final String ATTR_PROPERTIES = "properties";

    public static final List<String> BLOB_DATA_TYPE_LIST = Arrays.asList("TINYBLOB", "MEDIUMBLOB", "LONGBLOB", "IMAGE", "BLOB", "BINARY", "VARBINARY", "BYTEA", "LONGVARBINARY", "LONG RAW", "LONG VARBINARY");

}
