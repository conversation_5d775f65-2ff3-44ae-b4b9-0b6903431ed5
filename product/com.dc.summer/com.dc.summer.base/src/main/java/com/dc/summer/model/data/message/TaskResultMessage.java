package com.dc.summer.model.data.message;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;


@Data
@ApiModel("任务结果信息")
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class TaskResultMessage extends TaskInfoMessage {

    @ApiModelProperty(value = "阶段", example = "1")
    private Integer stage;

    @JsonProperty("start")
    @ApiModelProperty(value = "开始", example = "1")
    private Integer begin;

    @ApiModelProperty(value = "终止", example = "5")
    private Integer end;

    public TaskResultMessage(String token, String taskId, Integer begin, Integer end) {
        super(token, taskId);
        this.begin = begin;
        this.end = end;
    }

    public boolean hasStage() {
        return stage != null && stage > 0;
    }

    public boolean hasBeginEnd() {
        return begin != null && end != null && begin > 0 && end >= begin;
    }

}
