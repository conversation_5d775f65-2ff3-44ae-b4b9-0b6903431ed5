
package com.dc.summer.model.data.generator;

import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.ModelPreferences;
import com.dc.summer.data.transfer.DTUtils;
import com.dc.summer.model.*;
import com.dc.summer.model.data.DBDAttributeBinding;
import com.dc.summer.model.data.DBDDisplayFormat;
import com.dc.summer.model.data.DBDDocument;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.SQLUtils;
import com.dc.summer.model.sql.generator.SQLGeneratorBase;
import com.dc.summer.model.struct.DBSAttributeBase;
import com.dc.summer.model.struct.DBSDataContainer;
import com.dc.utils.CommonUtils;
import org.apache.commons.lang3.ArrayUtils;

import java.util.*;
import java.util.stream.Collectors;

public abstract class SQLGeneratorResultSet extends SQLGeneratorBase<SQLGeneratorResultSetController> {


    protected SQLGeneratorResultSetController getController() {
        return objects.get(0);
    }

    public List<Object[]> getSelectedRows() {

        Object[][] rows = getController().getResultSet().getRows();

        return getController()
                .getRowNumbers()
                .stream()
                .map(rowNumber -> rows[rowNumber])
                .collect(Collectors.toList());

    }

    protected String getEntityName(SQLGeneratorResultSetController object) {
        DBSDataContainer source = object.getResultsInfo().getDataContainer();
        DBPDataSource dataSource = source.getDataSource();
        return DTUtils.getTableName(dataSource, source, !isFullyQualifiedNames(), true);
    }

    protected abstract void generateSQL(DBRProgressMonitor monitor, StringBuilder sql, SQLGeneratorResultSetController object)
            throws DBException;

    protected Collection<? extends DBSAttributeBase> getAllAttributes(DBRProgressMonitor monitor, SQLGeneratorResultSetController object) {
        DBDAttributeBinding[] bindings = object.getResultsInfo().getAttributes();
        String label = ModelPreferences.getPreferences().getString(ModelPreferences.VIRTUAL_ROW_ID_LABEL);
        return ArrayUtils.isEmpty(bindings) ?
                Collections.emptyList() :
                Arrays.stream(bindings)
                        .filter(attr -> !(attr.getDataKind() == DBPDataKind.ROWID && label.equals(attr.getLabel())))
                        .collect(Collectors.toList());
    }

    protected List<DBDAttributeBinding> getKeyAttributes(DBRProgressMonitor monitor, SQLGeneratorResultSetController object) {
        String label = ModelPreferences.getPreferences().getString(ModelPreferences.VIRTUAL_ROW_ID_LABEL);
        return Arrays.stream(object.getResultsInfo().getAttributes())
                .filter(DBDAttributeBinding::isPrimaryKey)
                .filter(CommonUtils.distinctByColumn(
                        DBDAttributeBinding::getName,
                        attr -> label.equals(attr.getLabel()) ? attr.getName() : attr.getLabel(),
                        attr -> attr.getMetaAttribute().getEntityName(),
                        DBDAttributeBinding::getTypeID,
                        DBDAttributeBinding::getTypeName))
                .collect(Collectors.toList());
    }


    void appendKeyConditions(@NotNull StringBuilder sql, Collection<DBDAttributeBinding> keyAttributes, Object[] values) {
        if (!ArrayUtils.isEmpty(values)) {
            Object firstCellValue = values[0];
            if (firstCellValue instanceof DBDDocument) {
                DBDDocument document = (DBDDocument) firstCellValue;
                Object idName = document.getDocumentProperty(DBDDocument.PROP_ID_ATTRIBUTE_NAME);
                Object documentId = document.getDocumentId();
                if (idName != null && documentId != null) {
                    sql.append(idName).append(" = ").append(SQLUtils.quoteString(getController().getResultsInfo().getDataContainer().getDataSource(), documentId.toString()));
                    return;
                }
            }
        }
        boolean hasAttr = false;
        for (DBDAttributeBinding attr : keyAttributes) {
            if (hasAttr) sql.append(" AND ");
            appendValueCondition(getController(), sql, attr, values);
            hasAttr = true;
        }
    }

    private void appendValueCondition(SQLGeneratorResultSetController object, StringBuilder sql, DBDAttributeBinding binding, Object[] values) {
        Object value = getCellValue(object, binding, values);
        sql.append(DBUtils.getObjectFullName(binding, DBPEvaluationContext.DML));
        if (DBUtils.isNullValue(value)) {
            sql.append(" IS NULL");
        } else {
            sql.append("=");
            appendAttributeValue(object, sql, binding, values);
        }
    }

    protected void appendAttributeValue(SQLGeneratorResultSetController object, StringBuilder sql, DBDAttributeBinding binding, Object[] values) {
        DBPDataSource dataSource = binding.getDataSource();
        Object value = getCellValue(object, binding, values);
        DBSAttributeBase attribute = binding.getAttribute();
        if (attribute != null && attribute.getDataKind() == DBPDataKind.DATETIME && isUseCustomDataFormat()) {
            sql.append(
                    SQLUtils.quoteString(dataSource, SQLUtils.convertValueToSQL(dataSource, attribute, DBUtils.findValueHandler(dataSource, attribute), value, DBDDisplayFormat.UI)));
        } else {
            sql.append(
                    SQLUtils.convertValueToSQL(dataSource, attribute, value));
        }
    }

    private Object getCellValue(@NotNull SQLGeneratorResultSetController object,
                                @NotNull DBDAttributeBinding attribute,
                                @NotNull Object[] values) {
        return DBUtils.getAttributeValue(
                attribute,
                object.getResultsInfo().getAttributes(),
                values,
                null);
    }

}
