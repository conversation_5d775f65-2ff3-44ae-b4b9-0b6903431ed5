package com.dc.summer.model.data;

import com.dc.annotation.NotNull;
import com.dc.summer.model.data.message.TaskResultMessage;
import com.dc.summer.model.runtime.AbstractJob;
import com.dc.summer.model.type.WebAsyncTaskType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Data
@ApiModel("Web异步任务信息")
public class WebAsyncTaskInfo implements Cloneable {

    @NotNull
    @ApiModelProperty(value = "任务ID")
    private String taskId;

    @NotNull
    @ApiModelProperty(value = "任务名称")
    private String name;

    @ApiModelProperty(value = "任务状态")
    private WebAsyncTaskType status;

    @ApiModelProperty(value = "任务阶段")
    private Integer stage;

    @ApiModelProperty(value = "任务异常")
    private Throwable jobError;

    @JsonIgnore
    @ApiModelProperty(value = "执行任务", hidden = true)
    private AbstractJob job;

    @JsonIgnore
    @ApiModelProperty(value = "自动关闭", hidden = true)
    private boolean autoClose;

    @ApiModelProperty(value = "token")
    private String token;

    @JsonIgnore
    @ApiModelProperty(value = "挂载任务信息", hidden = true)
    private WebAsyncTaskInfo mountTaskInfo;

    @ApiModelProperty(value = "执行方式")
    private String exec;

    @JsonIgnore
    @ApiModelProperty(value = "是否继续执行 - true，继续 false，回滚", hidden = true)
    private boolean isContinueExecution = true;

    @JsonIgnore
    @ApiModelProperty(value = "任务范围")
    private Integer scope;

    public WebAsyncTaskInfo(String taskId, String name, String token) {
        this.taskId = taskId;
        this.name = name;
        this.token = token;
    }

    public void setStage(Integer stage) {
        this.stage = stage;
        this.noticeAll();
    }

    public void setStatus(WebAsyncTaskType status) {
        this.status = status;
        this.noticeAll();
    }

    public void exec() {
        this.exec(null);
    }

    public void exec(String taskName) {
        if (isRunning()) {
            return;
        }
        this.setStatus(WebAsyncTaskType.RUNNING);
        this.getJob().schedule(taskName);
        if (exec == null) {
            exec = "Async";
        }
        if (mountTaskInfo != null) {
            if (isFinished() && isContinueExecution) {
                log.info("执行挂载任务 taskId is [{}]", mountTaskInfo.getTaskId());
                mountTaskInfo.exec();
                mountTaskInfo.setExec("Mount");
                mountTaskInfo = null;
            } else {
                this.cancel();
            }
        }
    }

    /**
     * 取消挂载任务，并缺设置状态为，异步任务状态
     */
    public void cancel() {
        if (mountTaskInfo != null) {
            mountTaskInfo.setStatus(status);
            mountTaskInfo.setExec("Invalid");
            mountTaskInfo.setContinueExecution(false);
            mountTaskInfo.cancel();
            mountTaskInfo = null;
        }
    }

    @JsonIgnore
    public boolean isOver() {
        if (status == null) {
            return false;
        }
        switch (status) {
            case FINISHED:
            case SYS_ERROR:
            case SQL_ERROR:
            case INTERRUPTED:
                return true;
            default:
                return false;
        }
    }

    @JsonIgnore
    public boolean isRunning() {
        return status != null && status == WebAsyncTaskType.RUNNING;
    }

    @JsonIgnore
    public boolean isCancel() {
        return status != null && (status == WebAsyncTaskType.CANCELED || status == WebAsyncTaskType.INTERRUPTED);
    }

    @JsonIgnore
    public boolean isFinished() {
        return status != null && status == WebAsyncTaskType.FINISHED;
    }

    @JsonIgnore
    public TaskResultMessage getTaskResultMessage(String token) {
        TaskResultMessage taskResultMessage = new TaskResultMessage();
        taskResultMessage.setToken(token);
        taskResultMessage.setTaskId(taskId);
        taskResultMessage.setBegin(1);
        taskResultMessage.setEnd(stage);
        return taskResultMessage;
    }

    private void noticeAll() {
        synchronized (this) {
            this.notifyAll();
        }
    }

    @Override
    public WebAsyncTaskInfo clone() {
        try {
            return (WebAsyncTaskInfo) super.clone();
        } catch (CloneNotSupportedException e) {
            throw new AssertionError("Clone not supported", e);
        }
    }

}
