package com.dc.summer.service.impl;

import com.dc.repository.redis.service.impl.RedisServiceWithCompress;
import com.dc.springboot.core.component.JSON;
import com.dc.springboot.core.component.PortContext;
import com.dc.springboot.core.model.exception.ResultException;
import com.dc.springboot.core.model.exception.ServiceException;
import com.dc.springboot.core.model.result.WebSQLExecuteInfo;
import com.dc.summer.model.data.WebAsyncTaskInfo;
import com.dc.summer.model.data.WebAsyncTaskReact;
import com.dc.summer.model.data.message.TaskInfoMessage;
import com.dc.summer.model.data.message.TaskResultMessage;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.thread.SummerThreadScheduler;
import com.dc.summer.model.type.ExecuteType;
import com.dc.summer.service.ResultService;
import com.dc.summer.service.sql.WebSQLContextInfo;
import com.dc.utils.net.IPUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class ResultServiceImpl implements ResultService {


    @Resource
    private JSON json;

    @Resource
    private RedisServiceWithCompress redis;

    @Resource
    private PortContext portContext;

    @Resource
    private SummerThreadScheduler scheduler;

    private static final Cache<String, WebSQLExecuteInfo> EXECUTE_INFO_CACHE = Caffeine.newBuilder()
            .initialCapacity(100)
            .maximumSize(1000)
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .expireAfterAccess(30, TimeUnit.SECONDS)
            .build();

    public String getRedisPrefix(String taskId, int index) {
        StringBuilder builder = new StringBuilder();
        builder.append("summer");
        builder.append("_");
        if (StringUtils.isNotBlank(IPUtils.getByNetworkInterface())) {
            builder.append(IPUtils.getByNetworkInterface().replace(".", "_"));
        } else {
            builder.append(portContext.getLocalIp().replace(".", "_"));
        }
        builder.append("_");
        builder.append(taskId);
        builder.append("_");
        builder.append(index);
        return builder.toString();
    }

    @Override
    public void saveStringValue(String taskId, WebSQLExecuteInfo value, Long expirationTime) throws JsonProcessingException {
        if (expirationTime == null || expirationTime <= 0L) {
            return;
        }
        redis.set(getRedisPrefix(taskId, 0), json.getObjectMapper().writeValueAsString(value), expirationTime);
    }

    @Override
    public void addListValue(String taskId, WebSQLExecuteInfo value, Long expirationTime, int index) {
        if (expirationTime == null || expirationTime <= 0L) {
            return;
        }
        String key = getRedisPrefix(taskId, index);
        Runnable runnable = () -> {
            try {
                redis.set(key, json.getObjectMapper().writeValueAsString(value), expirationTime);
            } catch (JsonProcessingException e) {
                log.error("saveListValue error : ", e);
            }
        };

        EXECUTE_INFO_CACHE.put(key, value);
        scheduler.exec(ExecuteType.GET_SET_RESULT, runnable);
    }

    @Override
    public WebSQLExecuteInfo getStringValue(TaskInfoMessage message) throws ResultException {
        WebAsyncTaskInfo asyncTaskInfo;
        try {
            asyncTaskInfo = this.getAsyncTaskInfo(message);
        } catch (DBCException e) {
            throw new ServiceException("获取异步任务失败。", e);
        }

        TaskResultMessage taskResultMessage = asyncTaskInfo.getTaskResultMessage(message.getToken());
        return this.getStringValue(taskResultMessage);
    }

    @Override
    public WebSQLExecuteInfo getStringValue(TaskResultMessage message) throws ResultException {
        log.debug("获取结果: {}", message);
        List<WebSQLExecuteInfo> webSQLExecuteInfos = new ArrayList<>();
        String taskId = message.getTaskId();

        try {

            int begin = 0;
            int end = 0;

            if (message.hasStage()) {
                begin = message.getStage();
                end = begin;
            }
            if (message.hasBeginEnd()) {
                begin = message.getBegin();
                end = message.getEnd();
            }

            if (begin > 0 && end > 0) {
                for (int i = begin - 1; i < end; i++) {
                    final String key = getRedisPrefix(taskId, i);
                    WebSQLExecuteInfo executeInfo = EXECUTE_INFO_CACHE.getIfPresent(key);
                    if (executeInfo == null) {
                        String data = redis.get(key);
                        executeInfo = json.getObjectMapper().readValue(data, WebSQLExecuteInfo.class);
                    } else {
                        EXECUTE_INFO_CACHE.invalidate(key);
                    }
                    webSQLExecuteInfos.add(executeInfo);
                }
            } else {
                String data = redis.get(getRedisPrefix(taskId, 0));
                WebSQLExecuteInfo webSQLExecuteInfo = json.getObjectMapper().readValue(data, WebSQLExecuteInfo.class);
                webSQLExecuteInfos = Collections.singletonList(webSQLExecuteInfo);
            }

        } catch (Exception e) {
            log.error("JSON 转换异常", e);
            throw new ResultException(e.getMessage());
        }

        if (webSQLExecuteInfos.isEmpty()) {
            throw new ResultException("没有找到结果集！");
        }

        WebSQLExecuteInfo webSQLExecuteInfo = webSQLExecuteInfos.get(0);
        webSQLExecuteInfo.setTaskId(taskId);
        if (webSQLExecuteInfos.size() > 1) {
            for (int i = 1; i < webSQLExecuteInfos.size(); i++) {
                webSQLExecuteInfo.addQueryResults(webSQLExecuteInfos.get(i).getQueryResults());
            }
        }

        return webSQLExecuteInfo;
    }

    @Override
    public WebAsyncTaskInfo getAsyncTaskInfo(TaskInfoMessage message) throws DBCException {
        WebSQLContextInfo contextInfo = WebSQLContextInfo.getSimpleContext(message.getToken());
        return contextInfo.asyncTaskStatus(message.getTaskId(), false);
    }

    @Override
    public WebSQLExecuteInfo asyncSqlExecuteResults(TaskResultMessage message) throws ResultException {

        WebSQLExecuteInfo executeInfo = null;

        try {
            executeInfo = getStringValue(message);
        } catch (Exception e) {
            log.error("获取异步SQL执行结果失败: " + e.getMessage());
        }

        try {
            WebSQLContextInfo contextInfo = WebSQLContextInfo.getSimpleContext(message.getToken());
            WebAsyncTaskInfo webAsyncTaskInfo = contextInfo.asyncTaskStatus(message.getTaskId(), false);
            if (webAsyncTaskInfo != null) {
                Integer stage = webAsyncTaskInfo.getStage();
                if (webAsyncTaskInfo.isOver() && webAsyncTaskInfo.isAutoClose()) {
                    if (stage != null) {
                        if ((message.hasStage() && stage.compareTo(message.getStage()) <= 0) ||
                                (message.hasBeginEnd() && stage.compareTo(message.getEnd()) <= 0)) {
                            scheduler.exec(ExecuteType.CLOSE_SESSION, contextInfo::close);
                        }
                    } else {
                        scheduler.exec(ExecuteType.CLOSE_SESSION, contextInfo::close);
                    }
                }
                log.info("task-info: {}", json.getObjectMapper().writeValueAsString(webAsyncTaskInfo));
            }
        } catch (Exception e) {
            if (executeInfo == null) {
                throw new ResultException("处理异步SQL执行任务失败", e);
            } else {
                log.error("处理异步SQL执行任务失败: " + e.getMessage());
            }
        }

        return executeInfo;
    }

    @Override
    public WebAsyncTaskReact syncSqlExecuteResults(TaskInfoMessage infoMessage) throws DBCException, ResultException, InterruptedException {

        long startTimeMillis = System.currentTimeMillis();

        long timeoutMillis = Duration.ofSeconds(30).toMillis();

        WebAsyncTaskReact webAsyncTaskReact = new WebAsyncTaskReact();

        while (true) {

            WebAsyncTaskInfo asyncTaskInfo = this.getAsyncTaskInfo(infoMessage);

            //拷贝WebAsyncTaskInfo,防止其他线程修改status，导致返回状态错误
            WebAsyncTaskInfo asyncTaskInfoClone = asyncTaskInfo.clone();

            log.debug("async task info: {}", asyncTaskInfoClone);
            webAsyncTaskReact.setTaskInfo(asyncTaskInfoClone);

            Integer stage = asyncTaskInfoClone.getStage();
            if (stage != null && asyncTaskInfo.getScope() == null) {
                asyncTaskInfo.setScope(0);
            }

            Integer scope = asyncTaskInfo.getScope();
            // 任务完成，范围已经蔓延到指定阶段
            if (asyncTaskInfoClone.isOver() && (stage != null && stage.equals(scope))) {
                return webAsyncTaskReact;
            }

            // 任务完成，或者任务执行中（范围没有蔓延到指定阶段）
            if (asyncTaskInfoClone.isOver() || (stage != null && !stage.equals(scope))) {
                if (scope != null) {
                    scope++;
                }

                TaskResultMessage resultMessage = new TaskResultMessage(infoMessage.getToken(), infoMessage.getTaskId(), scope, stage);

                webAsyncTaskReact.setExecuteInfo(this.getStringValue(resultMessage));

                asyncTaskInfo.setScope(stage);

                return webAsyncTaskReact;
            }

            long elapsed = System.currentTimeMillis() - startTimeMillis;
            final long remainingTimeMillis = timeoutMillis - elapsed;
            // 超时机制
            if (remainingTimeMillis <= 0) {
                log.warn("Timeout waiting for async task to complete after {} ms", timeoutMillis);
                return webAsyncTaskReact;
            }

            // 等待唤醒
            synchronized (asyncTaskInfo) {
                log.debug("Waiting for the task to complete: {} ms", remainingTimeMillis);
                if (Objects.equals(asyncTaskInfo.getStage(), asyncTaskInfoClone.getStage()) && asyncTaskInfo.isRunning()) {
                    asyncTaskInfo.wait(remainingTimeMillis);
                }
            }
        }
    }

}
