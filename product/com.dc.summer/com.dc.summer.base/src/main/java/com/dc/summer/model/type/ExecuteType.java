package com.dc.summer.model.type;

import lombok.Getter;


@Getter
public enum ExecuteType {

    EXECUTE_SQL,

    EXECUTE_EXPORT,

    CLOSE_SESSION,

    WIN_HEARTBEAT,

    <PERSON>ILL_EXECUTE,

    PREVIEW_BLOB,

    EXECUTE_ORDER,
    OR<PERSON><PERSON>_PRODUCER,
    OR<PERSON><PERSON>_CONSUMER,

    SU<PERSON>IT_UPDATE,

    CHECK_RECYCLE,

    JOB_EXPORT,
    EXECUTE_BATCH,

    ALERT,
    SYNC_SCHEMA,

    PRINT_LOG,

    REFRESH_DATASOURCE,

    CLOSE_DATASOURCE,

    SEND_EMAIL,

    MOCK_DATA,

    SUBMIT_TASK,

    CALLBAC<PERSON>_TASK,

    TEST_CONNECTION,

    PREPARE_CONNECTION,

    SYNC_PA_TASK,

    SYNC_PA_MASSIVE_DATA,

    EXECUTE_CHAIN,

    GET_SET_RESULT,

    ;

}
