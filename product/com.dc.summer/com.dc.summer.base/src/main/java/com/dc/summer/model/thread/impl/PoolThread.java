package com.dc.summer.model.thread.impl;


import com.dc.springboot.core.model.thread.AbstractExecuteThread;
import com.dc.summer.model.type.ExecuteType;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Set;

@Service
public class PoolThread extends AbstractExecuteThread<ExecuteType> {
    @Override
    public Collection<ExecuteType> getTypes() {
        return Set.of(
                ExecuteType.CLOSE_SESSION,
                ExecuteType.WIN_HEARTBEAT,
                ExecuteType.KILL_EXECUTE,
                ExecuteType.REFRESH_DATASOURCE,
                ExecuteType.CLOSE_DATASOURCE,
                ExecuteType.TEST_CONNECTION,
                ExecuteType.PREPARE_CONNECTION,
                ExecuteType.SYNC_PA_MASSIVE_DATA
        );
    }

    @Override
    public String getName() {
        return "pool";
    }
}
