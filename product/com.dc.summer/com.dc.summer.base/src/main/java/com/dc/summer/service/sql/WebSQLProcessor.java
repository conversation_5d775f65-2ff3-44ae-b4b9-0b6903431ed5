package com.dc.summer.service.sql;

import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.springboot.core.model.chain.ChainBuilder;
import com.dc.springboot.core.model.data.ResultFormat;
import com.dc.springboot.core.model.database.TokenConfig;
import com.dc.springboot.core.model.execution.BatchExecuteModel;
import com.dc.springboot.core.model.execution.SqlExecuteModel;
import com.dc.springboot.core.model.execution.ValidExecuteModel;
import com.dc.springboot.core.model.log.SqlHistory;
import com.dc.springboot.core.model.result.*;
import com.dc.springboot.core.model.sensitive.DataDesensitizeProcessor;
import com.dc.springboot.core.model.sensitive.SqlDesensitization;
import com.dc.springboot.core.model.type.SqlExecuteStatus;
import com.dc.summer.DBException;
import com.dc.summer.ModelPreferences;
import com.dc.summer.model.*;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.data.*;
import com.dc.summer.model.data.model.PreviewBlobModel;
import com.dc.summer.model.data.result.ConsoleType;
import com.dc.summer.model.exec.*;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.impl.AbstractExecutionSource;
import com.dc.summer.model.impl.data.PrimaryKeyProcessor;
import com.dc.summer.model.impl.local.LocalSession;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.runtime.DBRRunnableParametrized;
import com.dc.summer.model.sql.SQLQuery;
import com.dc.summer.model.sql.SQLQueryType;
import com.dc.summer.model.sql.SQLSyntaxManager;
import com.dc.summer.model.sql.SqlFieldData;
import com.dc.summer.model.sql.parser.SQLRuleManager;
import com.dc.summer.model.statement.DBPBindStatementProvider;
import com.dc.summer.model.struct.*;
import com.dc.summer.model.type.WebDataFormat;
import com.dc.summer.model.type.WebSQLConstants;
import com.dc.summer.registry.statement.BindStatementProviderDescriptor;
import com.dc.summer.registry.statement.BindStatementProviderRegistry;
import com.dc.summer.service.container.WebSQLQueryDataContainer;
import com.dc.summer.service.receiver.KeyDataReceiver;
import com.dc.summer.service.receiver.WebBlobPreviewDataReceiver;
import com.dc.summer.service.receiver.WebSQLQueryDataReceiver;
import com.dc.utils.CommonUtils;
import com.dc.utils.Pair;
import com.dc.utils.http.FileUtil;
import com.dc.utils.http.HttpClientUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.tika.config.TikaConfig;
import org.apache.tika.io.TikaInputStream;
import org.apache.tika.metadata.Metadata;
import org.apache.tika.mime.MediaType;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * SQL processor.
 */
@Slf4j
public class WebSQLProcessor {

    private static final int MAX_RESULTS_COUNT = 100;

    @Getter
    private final SQLSyntaxManager syntaxManager;

    @Getter
    private final SQLRuleManager ruleManager;

    @Getter
    private final WebSQLContextInfo contextInfo;

    private final WebSQLDataDraw dataDraw;

    private static final BindStatementProviderRegistry BIND_STATEMENT_PROVIDER_REGISTRY = BindStatementProviderRegistry.getInstance();

    WebSQLProcessor(WebSQLContextInfo contextInfo, boolean initSyntaxRule) {
        this.contextInfo = contextInfo;
        this.dataDraw = new WebSQLDataDraw();
        if (initSyntaxRule) {
            syntaxManager = new SQLSyntaxManager();
            syntaxManager.init(
                    contextInfo.getDataSource().getSQLDialect(), contextInfo.getDataSourceContainer().getPreferenceStore());
            ruleManager = new SQLRuleManager(syntaxManager);
            ruleManager.loadRules(contextInfo.getDataSource(), false);
        } else {
            syntaxManager = null;
            ruleManager = null;
        }
    }

    public DBCExecutionContext getExecutionContext() {
        return DBUtils.getDefaultContext(contextInfo.getDataSource(), false);
    }

    private DBCExecutionContext getExecutionContext(@NotNull DBSDataContainer dataContainer) {
        return DBUtils.getDefaultContext(dataContainer, false);
    }

    public void destroyContext(@NotNull WebSQLContextInfo context) {
        context.dispose();
    }

    @NotNull
    public <T extends BatchExecuteModel> List<WebSQLQueryResult> processQuery(
            @NotNull DBRProgressMonitor monitor,
            List<T> batchExecuteModels,
            @Nullable WebDataFormat dataFormat,
            @NotNull ResultFormat resultFormat,
            @NotNull DBCExecutionPurpose purpose,
            boolean isErrorContinue,
            Function<T, ChainBuilder<WebSQLQueryResult>> functionBeforeStream,
            Function<T, ChainBuilder<WebSQLQueryResult>> functionDuringCheck,
            Function<T, ChainBuilder<WebSQLQueryResult>> functionAfterStream,
            boolean needHighLight,
            boolean needCacheResult,
            @Nullable TokenConfig tokenConfig) {

        contextInfo.setStatus(WebSQLContextStatus.NORMAL);

        List<WebSQLQueryResult> queryResults = new LinkedList<>();

        Iterator<? extends BatchExecuteModel> iterator = batchExecuteModels.iterator();

        while (iterator.hasNext()) {

            long startTime = System.currentTimeMillis();

            BatchExecuteModel batchExecuteModel = iterator.next();

            ValidExecuteModel validExecuteModel = batchExecuteModel.getValidExecuteModel();

            T executeModel = batchExecuteModel instanceof ValidExecuteModel ? (T) validExecuteModel : (T) batchExecuteModel;

            WebSQLQueryResult queryResult = new WebSQLQueryResult();

            DBPDataSource dataSource = contextInfo.getDataSource();

            queryResult.setDatabaseType(dataSource.getContainer().getConnectionConfiguration().getDatabaseType());

            HighLightInfo highLightInfo = queryResult.getHighLightInfo(
                    monitor,
                    batchExecuteModel.getOperation(),
                    dataSource,
                    contextInfo.getAutoCommit(),
                    batchExecuteModel.getHighLightState(),
                    needHighLight,
                    validExecuteModel.isContainsDblink());

            String executeSql = batchExecuteModel.getSql();

            queryResult.setSql(executeSql);

            DBCExecutionContext executionContext = null;

            SqlWarningsInfo sqlWarningsInfo = queryResult.getSqlWarningsInfo(monitor);

            PrimaryKeyProcessor primaryKeyModel = new PrimaryKeyProcessor(batchExecuteModel.getPrimaryKeyColumns(), batchExecuteModel.getTableName());

            boolean cancelLimit = setRowsLimitFromDriverMaxRow(batchExecuteModel);

            functionBeforeStream.apply(executeModel).exec(queryResult);

            resetSqlRecordSql(validExecuteModel, dataSource, batchExecuteModel.getOffset(), batchExecuteModel.getLimit());

            final List<Object> data = batchExecuteModel.getData();

            try (DBCSession session = contextInfo.openSession(monitor, purpose, "Query SQL")) {

                executionContext = contextInfo.getExecutionContext();

                if (functionDuringCheck.apply(executeModel).exec(queryResult)) {

                    contextInfo.confirmInterrupted();

                    queryResult.setExecuted(true);

                    long rowsLimit = List.of(SQLQueryType.SELECT, SQLQueryType.CALL).contains(SQLQueryType.of(batchExecuteModel.getOperation())) ? batchExecuteModel.getRowsLimit() : 0;

                    DBSDataContainer dataContainer = new WebSQLQueryDataContainer(executionContext.getDataSource(), executionContext, executeSql, rowsLimit, batchExecuteModel.getTableName());

                    highLightInfo.execValidate(executionContext);

                    DBPConnectionConfiguration connectionConfiguration = executionContext.getConfiguration();

                    DBRRunnableParametrized<DBCSession> runnableConfirm = batchExecuteModel.isConfirm() ?
                            param -> {

                                highLightInfo.isReExecute();

                                final SQLQuery sqlQuery = new SQLQuery(dataSource, executeSql);

                                AbstractExecutionSource source = new AbstractExecutionSource(dataContainer, session.getExecutionContext(), WebSQLProcessor.this, sqlQuery);

                                final DBCStatementType statementType = CollectionUtils.isNotEmpty(data) ? DBCStatementType.QUERY : DBCStatementType.SCRIPT;

                                try (DBCStatement dbStat = DBUtils.makeStatement(source, session, statementType, sqlQuery, batchExecuteModel.getOffset(), batchExecuteModel.getLimit())) {

                                    if (statementType == DBCStatementType.QUERY && dbStat instanceof JDBCPreparedStatement) {

                                        // 使用转字符集绑定参数
                                        String bindingId = dataSource.getContainer().getDriver().getProviderDescriptor().getId();
                                        BindStatementProviderDescriptor bindStatementProviderDescriptor = BIND_STATEMENT_PROVIDER_REGISTRY.getBinding(bindingId);
                                        DBDValueHandler valueHandler = null;
                                        DBSDataType dataType = null;
                                        if (bindStatementProviderDescriptor != null) {
                                            DBPBindStatementProvider bindStatementProvider = bindStatementProviderDescriptor.getInstance();
                                            valueHandler = bindStatementProvider.getValueHandler();
                                            dataType = bindStatementProvider.getDataType(session);
                                        }

                                        for (int i = 0; i < data.size(); i++) {
                                            Object value = data.get(i);
                                            if (value instanceof String && valueHandler != null) {
                                                valueHandler.bindValueObject(session, dbStat, dataType, i, value);
                                            } else {
                                                ((JDBCPreparedStatement) dbStat).setObject(i + 1, value);
                                            }
                                        }
                                    }

//                                    DBExecUtils.setStatementFetchSize(dbStat, offset, limit, 2000);

                                    contextInfo.confirmInterrupted();

                                    boolean hasResultSet;

                                    try {
                                        if (validExecuteModel.getSqlHistory() != null) {
                                            validExecuteModel.getSqlHistory().refreshTime();
                                            validExecuteModel.getSqlHistory().setSessionId(contextInfo.getExecutionContext().getProcessId());
                                        }
                                        hasResultSet = dbStat.executeStatement();
                                    } finally {
                                        sqlWarningsInfo.beforeExecute(dbStat, dataSource, session, executeSql);
                                    }

                                    fillQueryResults(
                                            dataContainer,
                                            dbStat,
                                            hasResultSet,
                                            queryResult,
                                            dataFormat,
                                            resultFormat,
                                            batchExecuteModel.getSqlDesensitization(),
                                            batchExecuteModel.getClobSizeLimit(),
                                            batchExecuteModel.getRowsOffset(),
                                            rowsLimit,
                                            batchExecuteModel.getExportLimit(),
                                            batchExecuteModel.getPageSize(),
                                            primaryKeyModel,
                                            needCacheResult,
                                            batchExecuteModel.getLimit(),
                                            batchExecuteModel.getSqlFieldDataList(),
                                            tokenConfig,
                                            connectionConfiguration,
                                            executeSql,
                                            data);
                                } catch (Exception e) {
                                    contextInfo.confirmInterrupted(e);
                                    throw new InvocationTargetException(e);
                                }
                            } :
                            param -> {
                                // Nothing to do
                            };

                    DBExecUtils.tryExecuteRecover(session, executionContext.getDataSource(), runnableConfirm, contextInfo.recoverBefore(), contextInfo.recoverAfter());
                    String useDatabase = batchExecuteModel.getUseDatabase();
                    if (StringUtils.isNotBlank(useDatabase)) {
                        WebSQLContextInfo.setDefaults(monitor, connectionConfiguration.getDatabaseName(), useDatabase, executionContext);
                    }
                    highLightInfo.execSuccess();
                    queryResult.setStatus(SqlExecuteStatus.SUCCESS.getValue());
                } else if (!isErrorContinue || contextInfo.isInterrupted() || contextInfo.isClosed()) {
                    break;
                }
            } catch (Exception e) {
                //设置执行结果状态
                if ("/".equals(executeSql)) {
                    log.debug("忽略执行 / 语句引起的异常。", e);
                    highLightInfo.execSuccess();
                    queryResult.setStatus(SqlExecuteStatus.SUCCESS.getValue());
                } else {
                    contextInfo.setLastException(e);
                    log.error("执行SQL错误！", e);
                    highLightInfo.execError(e);
                    queryResult.setMessage(e.getMessage());
                    queryResult.setStatus(contextInfo.isInterrupted() ? SqlExecuteStatus.USER_ABORT.getValue() : SqlExecuteStatus.EXEC_ERROR.getValue());
                    queryResult.setErrorPositions(getErrorPositions(executionContext, monitor, executeSql, e));
                    if (!isErrorContinue || contextInfo.isInterrupted()) {
                        break;
                    }
                }
            } finally {
                queryResult.setDuration(System.currentTimeMillis() - startTime);
                if (queryResult.isLimit() && cancelLimit) {
                    queryResult.setLimit(false);
                }
                queryResult.setHasNextSql(iterator.hasNext());
                queryResult.setSingle(batchExecuteModels.size() == 1);
                queryResult.setSqlRecord(validExecuteModel.getSqlRecord());
                queryResult.setSensitiveAuthDetailList(validExecuteModel.getSensitiveAuthDetailList());
                queryResult.setSqlHistory(validExecuteModel.getSqlHistory());
                highLightInfo.execFinally(executionContext);
                sqlWarningsInfo.afterExecute(executionContext);
                functionAfterStream.apply(executeModel).exec(queryResult);
                queryResults.add(queryResult);
            }
        }
        return queryResults;
    }

    private void resetSqlRecordSql(ValidExecuteModel validExecuteModel, DBPDataSource dataSource, long offset, long limit) {
        if (validExecuteModel.isQueryDataSizeControl()) {
            contextInfo.getDataSource().getContainer().getPreferenceStore().setValue(ModelPreferences.RESULT_SET_MAX_ROWS_USE_SQL, true);
            try (DBCStatement virtualStatement = DBUtils.makeStatement(null, new LocalSession(dataSource),
                    DBCStatementType.LOCAL, new SQLQuery(dataSource, validExecuteModel.getSqlRecord().getSql()), offset, limit)) {
                validExecuteModel.getSqlRecord().setSql(virtualStatement.getQueryString());
            } catch (Exception ignored) {
                // nothing to do here
            }
        } else {
            contextInfo.getDataSource().getContainer().getPreferenceStore().setValue(ModelPreferences.RESULT_SET_MAX_ROWS_USE_SQL, false);
        }
    }

    private boolean setRowsLimitFromDriverMaxRow(SqlExecuteModel sqlExecuteModel) {
        try {
            Map<String, String> properties = contextInfo.getConfiguration().getProperties();
            String driverLimit = properties.get("maxRows");
            if (StringUtils.isNotEmpty(driverLimit) && CommonUtils.isNumeric(driverLimit)) {
                long driverRowLimit = Long.parseLong(driverLimit);
                if (driverRowLimit < sqlExecuteModel.getRowsLimit() || sqlExecuteModel.getRowsLimit() == 0) {
                    sqlExecuteModel.setRowsLimit(driverRowLimit);
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("驱动属性maxRow失败！" + e.getMessage());
        }
        return false;
    }

    private DBPErrorAssistant.ErrorPosition[] getErrorPositions(
            DBCExecutionContext executionContext, DBRProgressMonitor monitor, String executeSql, Throwable e) {

        if (executionContext != null) {
            DBPErrorAssistant errorAssistant = DBUtils.getAdapter(DBPErrorAssistant.class, executionContext.getDataSource());
            if (errorAssistant != null) {
                return errorAssistant.getErrorPosition(monitor, executionContext, executeSql, e);
            }
        }
        return null;
    }


    public WebSQLExecuteInfo blobDataPreview(
            @NotNull DBRProgressMonitor monitor,
            @NotNull PreviewBlobModel previewBlobModel,
            @Nullable WebDataFormat dataFormat,
            @NotNull String url) {

        long startTime = System.currentTimeMillis();
        WebSQLExecuteInfo executeInfo = new WebSQLExecuteInfo();

        DBPDataSource dataSource = contextInfo.getDataSource();

        try {
            boolean retry = false;
            int attempt = 0;
            final int maxAttempts = 2;

            String originalTableName = previewBlobModel.getTableName();
            do {
                attempt++;
                try {
                    DBCExecutionContext executionContext = this.contextInfo.getExecutionContext();

                    String column = DBUtils.getFullyQualifiedName(dataSource, previewBlobModel.getColumnName());

                    if (retry) {
                        previewBlobModel.setTableName(DBUtils.getFullyQualifiedName(dataSource, originalTableName));
                    } else {
                        //处理三层架构数据库，比如sql server
                        String[] tableSplit = StringUtils.split(previewBlobModel.getTableName(), ".");

                        String[] schemaTable = ArrayUtils.insert(0, tableSplit, previewBlobModel.getSchemaName().split("\\."));

                        String tableName = DBUtils.getFullyQualifiedName(dataSource, schemaTable);

                        previewBlobModel.setTableName(tableName);
                    }

                    previewBlobModel.setColumnName(column);

                    String sql = getPreviewBlobSql(previewBlobModel, dataSource.getInfo());

                    DBSDataContainer dataContainer = new WebSQLQueryDataContainer(dataSource, executionContext, sql, 0, previewBlobModel.getTableName());

                    SQLQuery sqlQuery = new SQLQuery(dataSource, sql);
                    try (DBCSession session = executionContext.openSession(monitor, DBCExecutionPurpose.getById(contextInfo.getPurpose()), "Blob SQL")) {

                        DBExecUtils.tryExecuteRecover(session, dataSource, param -> {
                            AbstractExecutionSource source = new AbstractExecutionSource(dataContainer, session.getExecutionContext(), WebSQLProcessor.this, sqlQuery);

                            try (DBCStatement dbStat = DBUtils.makeStatement(source, session, DBCStatementType.SCRIPT, sqlQuery, 0, 0)) {

                                boolean hasResultSet = dbStat.executeStatement();
                                fillBlobResult(dataContainer, dbStat, hasResultSet, executeInfo, dataFormat, previewBlobModel.getColumnName(), url);
                            } catch (Exception e) {
                                log.error("blobDataPreview error : ", e);
                                throw new InvocationTargetException(e);
                            }
                        }, contextInfo.recoverBefore(), contextInfo.recoverAfter());
                    }

                    executeInfo.getBlobDataResult().setMessage("Success");
                    break;
                } catch (Exception e) {
                    if (attempt >= maxAttempts) {
                        contextInfo.setLastException(e);
                        BlobDataResult blobDataResult = new BlobDataResult();
                        blobDataResult.setMessage(e.getMessage());
                        executeInfo.setBlobDataResult(blobDataResult);
                    } else {
                        // 首次失败，设置标志以重试
                        retry = true;
                        log.warn("First attempt failed, will retry once. Error: " + e.getMessage());
                    }
                }
            } while (attempt < maxAttempts);

        } finally {
            executeInfo.getBlobDataResult().setDuration(System.currentTimeMillis() - startTime);
        }

        return executeInfo;
    }

    private String getPreviewBlobSql(PreviewBlobModel previewBlobModel, DBPDataSourceInfo dataSourceInfo) {
        //Pair<String, String> : typeName, value
        Map<String, Pair<String, String>> primaryKeyColumn = new HashMap<>();
        for (Map<String, String> map : previewBlobModel.getPropertyList()) {
            if (map.get("value") != null) {
                primaryKeyColumn.put(map.get("key"), new Pair<>(map.get("type_name"), map.get("value")));
            }
        }
        String conditionSql = "";
        int temp = 0;
        if (primaryKeyColumn.containsKey("ROWID") && primaryKeyColumn.get("ROWID") != null) {
            Pair<String, String> pair = primaryKeyColumn.get("ROWID");
            conditionSql = String.format("ROWID = '%s'", pair.getSecond());
        } else {
            for (String key : primaryKeyColumn.keySet()) {
                Pair<String, String> pair = primaryKeyColumn.get(key);
                String typeName = pair.getFirst();
                String value = pair.getSecond();
                if (value == null) {
                    continue;
                }
                String and = temp == 0 ? "" : " and ";
                String column = dataSourceInfo.getFormatColumnName(key);
                if (typeName.equalsIgnoreCase("DATE") || typeName.equalsIgnoreCase("DATETIME") || typeName.equalsIgnoreCase("TIMESTAMP")) {
                    conditionSql = conditionSql + and + column + " = TIMESTAMP '" + value + "'";
                } else {
                    conditionSql = conditionSql + and + column + " = '" + value + "'";
                }
                temp++;
            }
        }
        return String.format("SELECT %s FROM %s WHERE %s", previewBlobModel.getColumnName(), previewBlobModel.getTableName(), conditionSql);
    }

    public List<WebSQLQueryResult> updateResultsDataBatch(
            @NotNull DBRProgressMonitor monitor,
            @NotNull WebSQLResultsInfo resultsInfo,
            @Nullable List<WebSQLResultsBaseRow> rows,
            @NotNull boolean isErrorContinue,
            Function<WebSQLResultsBaseRow, ChainBuilder<WebSQLQueryResult>> functionBeforeStream,
            Function<WebSQLResultsBaseRow, ChainBuilder<WebSQLQueryResult>> functionDuringCheck,
            Function<WebSQLResultsBaseRow, ChainBuilder<WebSQLQueryResult>> functionAfterStream,
            SqlHistory sqlHistory,
            boolean needHighLight) {

        contextInfo.setStatus(WebSQLContextStatus.NORMAL);

        DBSEntity dataContainer;
        DBDAttributeBinding[] keyAttributes;

        DBDAttributeBinding[] allAttributes = resultsInfo.getAttributes();
        DBDAttributeBinding checkedAttribute = allAttributes[0].getParentObject() == null ? allAttributes[0] : allAttributes[0].getParentObject();

        boolean isDocument = checkedAttribute.getDataKind() == DBPDataKind.DOCUMENT;
        if (isDocument) {
            allAttributes = new DBDAttributeBinding[]{checkedAttribute};
            keyAttributes = new DBDAttributeBinding[]{checkedAttribute};
            dataContainer = new WebSQLExecuteBatchTable();
        } else {
            keyAttributes = Arrays.stream(allAttributes)
                    .filter(DBDAttributeBinding::isPrimaryKey)
                    .toArray(DBDAttributeBinding[]::new);
            dataContainer = new WebSQLExecuteBatchTable();
        }

        checkDataEditAllowed(dataContainer);

        List<WebSQLQueryResult> queryResults = new ArrayList<>(rows.size());

        for (WebSQLResultsBaseRow row : rows.stream()
                .sorted(Comparator.comparingInt(WebSQLResultsBaseRow::getOrder))
                .collect(Collectors.toList())) {

            long startTime = System.currentTimeMillis();

            Map<String, Object> options = new HashMap<>();
            options.put(DBSDataManipulator.OPTION_UPDATE_BINDING_SQL, row.getSql());

            WebSQLQueryResultSet queryResultSet = new WebSQLQueryResultSet();
            queryResultSet.setResultsInfo(resultsInfo);

            DBCStatistics statistics = new DBCStatistics();

            WebSQLQueryResult queryResult = new WebSQLQueryResult();

            DBPDataSource dataSource = contextInfo.getDataSource();

            queryResult.setDatabaseType(dataSource.getContainer().getConnectionConfiguration().getDatabaseType());

            HighLightInfo highLightInfo = queryResult.getHighLightInfo(
                    monitor,
                    row.getOperation(),
                    contextInfo.getDataSource(),
                    contextInfo.getAutoCommit(),
                    null,
                    needHighLight,
                    false);

            DBCExecutionContext executionContext = null;

            functionBeforeStream.apply(row).exec(queryResult);

            try (KeyDataReceiver keyReceiver = new KeyDataReceiver(resultsInfo);
                 DBCSession session = contextInfo.openSession(monitor, DBCExecutionPurpose.USER, "Update SQL")) {

                executionContext = contextInfo.getExecutionContext();

                if (functionDuringCheck.apply(row).exec(queryResult)) {

                    contextInfo.confirmInterrupted();

                    keyReceiver.setRow(row.getData().toArray());

                    DBSDataManipulator.ExecuteBatch executeBatch = generateUpdateResultsData(
                            row,
                            keyReceiver,
                            session,
                            (DBSDataManipulator) dataContainer,
                            executionContext,
                            allAttributes,
                            keyAttributes);

                    highLightInfo.execValidate(executionContext);

                    DBExecUtils.tryExecuteRecover(executionContext, dataSource, param -> {
                        highLightInfo.isReExecute();

                        contextInfo.confirmInterrupted();

                        try {
                            if (sqlHistory != null) {
                                sqlHistory.refreshTime();
                                sqlHistory.setSessionId(contextInfo.getExecutionContext().getProcessId());
                            }
                            executeBatch.execute(session, options, statistics);
                        } catch (DBCException e) {
                            contextInfo.confirmInterrupted(e);
                            throw new InvocationTargetException(e);
                        }
                    }, contextInfo.recoverBefore(), contextInfo.recoverAfter());

                    highLightInfo.execSuccess();
                    queryResult.setStatus(SqlExecuteStatus.SUCCESS.getValue());
                } else if (!isErrorContinue || contextInfo.isInterrupted() || contextInfo.isClosed()) {
                    break;
                }
            } catch (Exception e) {
                contextInfo.setLastException(e);
                log.error("修改SQL错误！", e);
                highLightInfo.execError(e);
                queryResult.setStatus(contextInfo.isInterrupted() ? SqlExecuteStatus.USER_ABORT.getValue() : SqlExecuteStatus.EXEC_ERROR.getValue());
                queryResult.setMessage(e.getMessage());
                if (!isErrorContinue || contextInfo.isInterrupted()) {
                    break;
                }
            } finally {
                FileUtil.deleteFile(String.format(WebSQLDataDraw.DATA_EXPORT_FOLDER, executionContext.getSessionId()));
                queryResult.setDuration(System.currentTimeMillis() - startTime);
                queryResultSet.setUpdateRowCount(statistics.getRowsUpdated());
                queryResult.setResultSet(Collections.singletonList(queryResultSet));
                highLightInfo.execFinally(executionContext);
                queryResult.setNoPermissionObjects(row.getNoPermissionObjects());
                queryResult.setSqlRecord(row.getSqlRecord());
                queryResult.setSqlHistory(sqlHistory);
                functionAfterStream.apply(row).exec(queryResult);
                queryResults.add(queryResult);
            }
        }

        return queryResults;

    }

    private DBSDataManipulator.ExecuteBatch generateUpdateResultsData(
            @Nullable WebSQLResultsBaseRow baseRow,
            @Nullable DBDDataReceiver keyReceiver,
            DBCSession session,
            DBSDataManipulator dataManipulator,
            DBCExecutionContext executionContext,
            DBDAttributeBinding[] allAttributes,
            DBDAttributeBinding[] keyAttributes) throws DBException {

        boolean isDocumentKey = keyAttributes.length == 1 && keyAttributes[0].getDataKind() == DBPDataKind.DOCUMENT;

        //only script generation (without execution)
        boolean withoutExecution = keyReceiver == null;

        WebExecutionSource executionSource = new WebExecutionSource(dataManipulator, executionContext, this);

        switch (SQLQueryType.of(baseRow.getOperation())) {
            case UPDATE:
                WebSQLResultsUpdateRow updateRow = ((WebSQLResultsUpdateRow) baseRow);
                Map<String, Object> updateValues = updateRow.getUpdateValues();
                List<Object> data = updateRow.getData();
                if (CommonUtils.isEmpty(data) || CommonUtils.isEmpty(updateValues)) {
                    break;
                }
                DBDAttributeBinding[] updateAttributes = new DBDAttributeBinding[updateValues.size()];

                int index = 0;
                for (String indexStr : updateValues.keySet()) {
                    int attrIndex = CommonUtils.toInt(indexStr, -1);
                    updateAttributes[index++] = allAttributes[attrIndex];
                }

                List<Object> realCellValues = new ArrayList<>(updateAttributes.length);
                for (DBDAttributeBinding updateAttribute : updateAttributes) {
                    Object realCellValue = convertInputCellValue(session, updateAttribute,
                            updateValues.get(String.valueOf(updateAttribute.getOrdinalPosition())), withoutExecution);
                    realCellValues.add(realCellValue);
                }

                List<Object> realKeyValues = new ArrayList<>(keyAttributes.length);
                for (DBDAttributeBinding keyAttribute : keyAttributes) {
                    Object realKeyValue = keyAttribute.getValueHandler().getValueFromObject(
                            session,
                            keyAttribute,
                            convertInputCellValue(session, keyAttribute,
                                    data.get(keyAttribute.getOrdinalPosition()), withoutExecution),
                            false,
                            true);
                    realKeyValues.add(realKeyValue);
                }

                List<Object> rowValues = new ArrayList<>(realCellValues.size() + realKeyValues.size());

                if (isDocumentKey) {
                    rowValues.addAll(realKeyValues);
                    rowValues.addAll(realCellValues);
                } else {
                    rowValues.addAll(realCellValues);
                    rowValues.addAll(realKeyValues);
                }

                DBSDataManipulator.ExecuteBatch updateBatch = dataManipulator.updateData(
                        session, updateAttributes, keyAttributes, keyReceiver, executionSource);
                updateBatch.add(rowValues.toArray());
                return updateBatch;
            case INSERT:
                List<Object> addedValues = baseRow.getData();
                if (CommonUtils.isEmpty(addedValues)) {
                    break;
                }
                Map<DBDAttributeBinding, Object> insertAttributes = new LinkedHashMap<>();

                for (int i = 0; i < allAttributes.length; i++) {
                    if (addedValues.get(i) != null) {
                        Object realCellValue = convertInputCellValue(session, allAttributes[i],
                                addedValues.get(i), withoutExecution);
                        insertAttributes.put(allAttributes[i], realCellValue);
                        addedValues.set(i, realCellValue);
                    }
                }

                DBSDataManipulator.ExecuteBatch insertBatch = dataManipulator.insertData(
                        session,
                        insertAttributes.keySet().toArray(new DBDAttributeBinding[0]),
                        keyReceiver,
                        executionSource,
                        new LinkedHashMap<>());
                insertBatch.add(insertAttributes.values().toArray());
                return insertBatch;
            case CREATE:
            case DELETE:
                List<Object> keyData = baseRow.getData();
                if (CommonUtils.isEmpty(keyData)) {
                    break;
                }
                Map<DBDAttributeBinding, Object> delKeyAttributes = new LinkedHashMap<>();

                for (int i = 0; i < allAttributes.length; i++) {
                    if (isDocumentKey || ArrayUtils.contains(keyAttributes, allAttributes[i])) {
                        Object realCellValue = convertInputCellValue(session, allAttributes[i],
                                keyData.get(i), withoutExecution);
                        delKeyAttributes.put(allAttributes[i], realCellValue);
                    }
                }

                DBSDataManipulator.ExecuteBatch deleteBatch = dataManipulator.deleteData(
                        session,
                        delKeyAttributes.keySet().toArray(new DBSAttributeBase[0]),
                        executionSource);
                deleteBatch.add(delKeyAttributes.values().toArray());
                return deleteBatch;
        }
        throw new DBException("SQL 值处理失败!");
    }

    @NotNull
    public DBDDocument makeDocumentInputValue(
            DBCSession session,
            DBSDocumentLocator dataContainer,
            WebSQLResultsInfo resultsInfo,
            WebSQLResultsUpdateRow row) throws DBException {
        // Document reference
        DBDDocument document = null;
        Map<String, Object> keyMap = new LinkedHashMap<>();
        DBDAttributeBinding[] attributes = resultsInfo.getAttributes();
        for (int j = 0; j < attributes.length; j++) {
            DBDAttributeBinding attr = attributes[j];
            Object plainValue = dataDraw.makePlainCellValue(session, attr, row.getData().get(j));
            if (plainValue instanceof DBDDocument) {
                // FIXME: Hack for DynamoDB. We pass entire document as a key
                // FIXME: Let's just return it back for now
                document = (DBDDocument) plainValue;
                break;
            }
            keyMap.put(attr.getName(), plainValue);
        }
        if (document == null) {
            document = dataContainer.findDocument(session.getProgressMonitor(), keyMap);
            if (document == null) {
                throw new DBCException("Error finding document by key " + keyMap);
            }
        }
        return document;
    }

    @Nullable
    public Object convertInputCellValue(DBCSession session, DBDAttributeBinding updateAttribute, Object cellRawValue, boolean justGenerateScript) throws DBCException {
        cellRawValue = dataDraw.makePlainCellValue(session, updateAttribute, cellRawValue);
        Object realCellValue = cellRawValue;
        // In some cases we already have final value here
        if (!(realCellValue instanceof DBDValue)) {
            try {
                realCellValue = updateAttribute.getValueHandler().getValueFromObject(
                        session,
                        updateAttribute,
                        cellRawValue,
                        false,
                        true);
            } catch (DBCException e) {
                //checks if this function is used only for script generation
                if (justGenerateScript) {
                    return null;
                } else {
                    throw e;
                }
            }
        }
        return realCellValue;
    }

    ////////////////////////////////////////////////
    // Misc

    private void checkRowIdentifier(WebSQLResultsInfo resultsInfo, DBDRowIdentifier rowIdentifier) throws RuntimeException {
        if (rowIdentifier == null || !rowIdentifier.isValidIdentifier()) {
            throw new RuntimeException("Can't detect row identifier for data container '" + resultsInfo.getDataContainer().getName() + "'. It must have at least one unique key.");
        }
    }

    private void checkDataEditAllowed(DBSEntity dataContainer) throws RuntimeException {
        if (!(dataContainer instanceof DBSDataManipulator)) {
            throw new RuntimeException("Data container '" + dataContainer.getName() + "' is not editable");
        }
    }

    private void fillQueryResults(
            @NotNull DBSDataContainer dataContainer,
            @NotNull DBCStatement dbStat,
            boolean hasResultSet,
            @NotNull WebSQLQueryResult queryResult,
            @Nullable WebDataFormat dataFormat,
            @NotNull ResultFormat resultFormat,
            @NotNull SqlDesensitization sqlDesensitization,
            @NotNull Integer clobSizeLimit,
            long offset,
            long rowsLimit,
            long exportLimit,
            long pageSize,
            PrimaryKeyProcessor primaryKeyModel,
            boolean needCacheResult,
            long limit,
            List<SqlFieldData> columnDataList,
            TokenConfig tokenConfig,
            DBPConnectionConfiguration connectionConfiguration,
            @NotNull String sql,
            @NotNull List<Object> data) throws DBException {

        //设置是否才用原格式
        if (resultFormat != null && resultFormat.getUseNativeFormat() != null) {
            dataContainer.getDataSource().getContainer().setUseNativeDateTimeFormat(resultFormat.getUseNativeFormat());
        }

        int maxResultsCount = resolveMaxResultsCount(dataContainer.getDataSource());
        List<WebSQLQueryResultSet> queryResultSets = new LinkedList<>();
        WebSQLQueryResultSet queryResultSet;
        boolean isLimit = false;

        for (int i = 0; i < maxResultsCount; i++) {
            if (hasResultSet) {
                try (DBCResultSet resultSet = dbStat.openResultSet()) {
                    if (resultSet == null) {
                        break;
                    }
                    DataDesensitizeProcessor desensitizeData = new DataDesensitizeProcessor(
                            dataContainer.getDataSource(),
                            sqlDesensitization.getEnableDesensitizeType(),
                            sqlDesensitization.getDataMasks(),
                            sqlDesensitization.getSymbol(),
                            sqlDesensitization.getNodeModels(),
                            sqlDesensitization.getGradedClassifiedModel(),
                            ConsoleType.of(tokenConfig == null ? null : tokenConfig.getConsole()));
                    try (WebSQLQueryDataReceiver dataReceiver = new WebSQLQueryDataReceiver(
                            contextInfo,
                            dataContainer,
                            dataFormat,
                            resultFormat,
                            desensitizeData,
                            clobSizeLimit,
                            primaryKeyModel,
                            sqlDesensitization.isNeedOriginalRows(),
                            needCacheResult,
                            offset,
                            limit,
                            columnDataList,
                            connectionConfiguration)) {
                        DBCSession session = dbStat.getSession();
                        dataReceiver.fetchStart(session, resultSet, 0L, 0L);
                        while (resultSet.nextRow() && (rowsLimit <= 0 || offset < rowsLimit)) {
                            dataReceiver.fetchRow(session, resultSet);
                            offset++;
                        }
                        dataReceiver.fetchEnd(session, resultSet);
                        queryResultSet = dataReceiver.getWebResultSet();
                        if (rowsLimit > 0 && offset >= rowsLimit) {
                            isLimit = true;
                            queryResultSet.setHasMoreData(false);
                        }
                        //导出提示需要
                        //之前的select限制
                        queryResultSet.setLimit(isLimit);
                        queryResultSet.setRowsLimit(rowsLimit);
                        //后来export限制和select限制分离
                        //这个区域的逻辑是select限制逻辑。在这里无法判断是否导出已受限制
                        queryResultSet.setExportRowsLimit(exportLimit);

                        //赋值前端的pageSize
                        queryResultSet.setPageSize(pageSize);

                        queryResultSet.setSql(sql);
                        queryResultSet.setData(data);
                    }
                }
                queryResultSets.add(queryResultSet);
            } else {
                long updateRowCount = dbStat.getUpdateRowCount();
                if (updateRowCount >= 0) {
                    queryResultSet = new WebSQLQueryResultSet();
                    queryResultSet.setUpdateRowCount(updateRowCount);
                    queryResultSets.add(queryResultSet);
                } else {
                    break;
                }
            }
            hasResultSet = dbStat.nextResults();
        }

        queryResult.setResultSet(queryResultSets);
        queryResult.setLimit(isLimit);
        log.debug("QueryResultSets [{}] complete filling.", queryResultSets.size());
    }

    private void fillBlobResult(
            @NotNull DBSDataContainer dataContainer,
            @NotNull DBCStatement dbStat,
            boolean hasResultSet,
            @NotNull WebSQLExecuteInfo executeInfo,
            @Nullable WebDataFormat dataFormat,
            @Nullable String columnName,
            @Nullable String url) throws DBException {
        int maxResultsCount = resolveMaxResultsCount(dataContainer.getDataSource());
        BlobDataResult blobDataResult = new BlobDataResult();
        for (int i = 0; i < maxResultsCount; i++) {
            if (hasResultSet) {
                try (DBCResultSet resultSet = dbStat.openResultSet()) {
                    if (resultSet == null) {
                        break;
                    }
                    try (WebBlobPreviewDataReceiver dataReceiver = new WebBlobPreviewDataReceiver(contextInfo, dataContainer, dataFormat)) {
                        dataReceiver.fetchStart(dbStat.getSession(), resultSet, 0L, 0L);
                        while (resultSet.nextRow()) {
                            dataReceiver.fetchRow(dbStat.getSession(), resultSet);
                        }
                        dataReceiver.fetchEnd(dbStat.getSession(), resultSet);
                        WebSQLQueryResultSet webSQLQueryResultSet = dataReceiver.getWebResultSet();
                        Object[] rows = webSQLQueryResultSet.getRows()[0];
                        WebSQLQueryResultColumn[] columns = webSQLQueryResultSet.getColumns();
                        int index = 0;
                        for (int j = 0; j < columns.length; j++) {
                            WebSQLQueryResultColumn column = columns[j];
                            if (column.getName().equalsIgnoreCase(columnName)) {
                                index = j;
                            }
                        }
                        byte[] data = (byte[]) ((Map<?, ?>) rows[index]).get(WebSQLConstants.ATTR_BINARY);
                        if ((data == null || data.length == 0) && ((Map<?, ?>) rows[index]).get(WebSQLConstants.ATTR_TEXT) != null) {
                            data = (((Map<?, ?>) rows[index]).get(WebSQLConstants.ATTR_TEXT)).toString().getBytes();
                        }
                        InputStream input = new ByteArrayInputStream(data);
                        String fileType = WebSQLMediumTypeUtil.getFileType(data, rows, index, columns);
                        String fileName = WebSQLMediumTypeUtil.getFileName(rows, index, columns);
                        Pattern pattern = Pattern.compile(".*\\." + fileType);
                        Matcher matcher = pattern.matcher(fileName);
                        //如果文件是可识别的且文件名中没有扩展名的，手动添加扩展名
                        if (!"binary".equals(fileType) && !matcher.matches()) {
                            fileName = String.format("%s.%s", fileName, fileType);
                        }

                        String pathResult = HttpClientUtils.doPost(url, input, fileName, "123");
                        String path = WebSQLMediumTypeUtil.parsePath(pathResult);
                        String upperLevel = "binary";
                        TikaConfig config = TikaConfig.getDefaultConfig();
                        Metadata metadata = new Metadata();
                        InputStream stream = TikaInputStream.get(data, metadata);
                        MediaType mediaType = null;
                        try {
                            mediaType = config.getMimeRepository().detect(stream, metadata);
                        } catch (IOException ioException) {
                            log.error("fillBlobResult error : ", ioException);
                        }
                        if (mediaType != null) {
                            String mimeType = mediaType.getType();
                            if (StringUtils.isNotBlank(mimeType)) {
                                String[] types = mimeType.split("/");
                                upperLevel = types[0];
                            }
                        }
                        blobDataResult.setPath(path);
                        blobDataResult.setNextLevel(fileType);
                        blobDataResult.setUpperLevel(upperLevel);

                    }
                }
            }
            hasResultSet = dbStat.nextResults();
        }
        executeInfo.setBlobDataResult(blobDataResult);
    }

    ///////////////////////////////////////////////////////
    // Utils
    private static int resolveMaxResultsCount(@Nullable DBPDataSource dataSource) {
        if (dataSource == null) {
            return MAX_RESULTS_COUNT;
        }
        return dataSource.getInfo().supportsMultipleResults() ? MAX_RESULTS_COUNT : 1;
    }

}
