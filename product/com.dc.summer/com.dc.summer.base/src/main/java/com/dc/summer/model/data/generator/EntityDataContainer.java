package com.dc.summer.model.data.generator;

import com.dc.summer.DBException;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.data.DBDDataFilter;
import com.dc.summer.model.data.DBDDataReceiver;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCExecutionSource;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.DBCStatistics;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.*;
import com.dc.summer.service.container.WebSQLQueryDataContainer;
import org.eclipse.core.runtime.IAdaptable;

import java.util.Collection;
import java.util.List;

public class EntityDataContainer implements DBSDataContainer, DBSEntity, IAdaptable {

    private final List<? extends DBSEntityAttribute> attributes;

    private final WebSQLQueryDataContainer dataContainer;

    public EntityDataContainer(List<? extends DBSEntityAttribute> attributes, WebSQLQueryDataContainer dataContainer) {
        this.attributes = attributes;
        this.dataContainer = dataContainer;
    }

    @Override
    public String getName() {
        return null;
    }

    @Override
    public String getDescription() {
        return null;
    }

    @Override
    public boolean isPersisted() {
        return false;
    }

    @Override
    public DBSObject getParentObject() {
        return null;
    }

    @Override
    public DBPDataSource getDataSource() {
        return null;
    }

    @Override
    public String[] getSupportedFeatures() {
        return new String[0];
    }

    @Override
    public DBCStatistics readData(DBCExecutionSource source, DBCSession session, DBDDataReceiver dataReceiver, DBDDataFilter dataFilter, long firstRow, long maxRows, long flags, int fetchSize, List<Object> data) throws DBCException {
        return null;
    }

    @Override
    public DBCStatistics readData(DBCExecutionSource source, DBCSession session, DBDDataReceiver dataReceiver, DBDDataFilter dataFilter, long firstRow, long maxRows, long flags, int fetchSize, int stage, List<Object> data) throws DBCException {
        return null;
    }

    @Override
    public long countData(DBCExecutionSource source, DBCSession session, DBDDataFilter dataFilter, long flags) throws DBCException {
        return 0;
    }

    @Override
    public DBSEntityType getEntityType() {
        return null;
    }

    @Override
    public List<? extends DBSEntityAttribute> getAttributes(DBRProgressMonitor monitor) throws DBException {
        return attributes;
    }

    @Override
    public DBSEntityAttribute getAttribute(DBRProgressMonitor monitor, String attributeName) throws DBException {
        return null;
    }

    @Override
    public Collection<? extends DBSEntityConstraint> getConstraints(DBRProgressMonitor monitor) throws DBException {
        return null;
    }

    @Override
    public Collection<? extends DBSEntityAssociation> getAssociations(DBRProgressMonitor monitor) throws DBException {
        return null;
    }

    @Override
    public Collection<? extends DBSEntityAssociation> getReferences(DBRProgressMonitor monitor) throws DBException {
        return null;
    }

    @Override
    public <T> T getAdapter(Class<T> clazz) {
        if (clazz == DBSDataContainer.class) {
            return (T) dataContainer.getQueryDataContainer();
        }
        return null;
    }
}
