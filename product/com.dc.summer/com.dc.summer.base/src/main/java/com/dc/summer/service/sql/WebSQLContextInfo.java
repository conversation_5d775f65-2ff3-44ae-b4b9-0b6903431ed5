package com.dc.summer.service.sql;

import com.dc.annotation.SQL;
import com.dc.code.NotNull;
import com.dc.function.BiConsumerFunction;
import com.dc.repository.redis.model.*;
import com.dc.repository.redis.rapper.EnvRapper;
import com.dc.springboot.core.component.FilterIgnoreFiled;
import com.dc.springboot.core.component.JSON;
import com.dc.springboot.core.component.Resource;
import com.dc.springboot.core.model.data.BatchMessage;
import com.dc.springboot.core.model.data.PreferencesMessage;
import com.dc.springboot.core.model.database.ConnectionConfig;
import com.dc.springboot.core.model.database.ConnectionTokenMessage;
import com.dc.springboot.core.model.database.TestConnectionMessage;
import com.dc.springboot.core.model.database.TokenConfig;
import com.dc.springboot.core.model.exception.ConnectionException;
import com.dc.springboot.core.model.exception.NotFindTokenException;
import com.dc.springboot.core.model.exception.ServiceException;
import com.dc.springboot.core.model.message.ExecuteEvent;
import com.dc.springboot.core.model.message.MessageConstants;
import com.dc.springboot.core.model.result.WebSQLResultsInfo;
import com.dc.summer.DBException;
import com.dc.summer.ModelPreferences;
import com.dc.summer.component.SummerMapper;
import com.dc.summer.config.SummerConfig;
import com.dc.summer.exec.handler.DataSourceConnectionHandler;
import com.dc.summer.exec.handler.StatementHandler;
import com.dc.summer.exec.handler.TestDataSourceConnectionHandler;
import com.dc.summer.exec.model.counter.HandlerCounter;
import com.dc.summer.exec.model.data.ConnectionConfiguration;
import com.dc.summer.exec.model.data.TestConnectionConfiguration;
import com.dc.summer.exec.model.exception.MaximumContextSizeException;
import com.dc.summer.exec.model.observer.ContextSubject;
import com.dc.summer.exec.model.thread.BufferThread;
import com.dc.summer.exec.model.type.OperationType;
import com.dc.summer.model.DBPCloseableObject;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPErrorAssistant;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.data.DBDAttributeBinding;
import com.dc.summer.model.data.WebAsyncTaskInfo;
import com.dc.summer.model.exec.*;
import com.dc.summer.model.log.LifeCycleMessage;
import com.dc.summer.model.runtime.AbstractJob;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.runtime.LoggingProgressMonitor;
import com.dc.summer.model.runtime.ProxyProgressMonitor;
import com.dc.summer.model.struct.DBSDataContainer;
import com.dc.summer.model.type.WebAsyncTaskType;
import com.dc.summer.service.MessageService;
import com.dc.summer.service.transfer.WebDataTransfer;
import com.dc.type.DatabaseType;
import com.dc.utils.LoggerUtils;
import com.dc.utils.StackTraceUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.map.LRUMap;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.Status;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * SQLContextInfo.
 */
@Slf4j
public class WebSQLContextInfo implements DBPCloseableObject {

    private static final DBRProgressMonitor MONITOR = new LoggingProgressMonitor();

    private static final String ISOLATED_TOKEN = "Isolated Token";

    private static final Map<String, WebSQLContextInfo> contexts = new ConcurrentHashMap<>();

    private static final Map<String, Integer> transactionIndexes = new ConcurrentHashMap<>();

    private static final AtomicInteger TASK_ID = new AtomicInteger();

    private final Map<String, WebAsyncTaskInfo> asyncTasks = new ConcurrentHashMap<>();
    private final AtomicInteger taskCount = new AtomicInteger();

    @Getter
    private final DataSourceConnectionHandler dataSourceContainer;
    @Getter
    private final DBPDataSource dataSource;
    @Getter
    private final WebSQLProcessor processor;
    @Getter
    private final WebDataTransfer transfer;
    @Getter
    private final String token;

    @Getter
    private final ConnectionConfiguration configuration;
    private final Map<String, WebSQLResultsInfo> resultIdToInfoMap = new LRUMap<>(50);
    private final AtomicInteger resultId = new AtomicInteger();

    private WebSQLResultsInfo previousWebSQLResultsInfo;

    @Getter
    private Integer purpose;

    @Getter
    private Boolean autoCommit;

    @Setter
    private Boolean autoConnect;

    @Getter
    private Boolean smartCommit;

    @Getter
    private String prefs;

    @Setter
    @Getter
    private Long expirationTime;

    @Getter
    private String charset;

    @Getter
    private volatile int serialNumber;

    private WebSQLContextStatus status;

    @Getter
    @Setter
    private volatile boolean hasTransaction;

    @Getter
    @Setter
    private Boolean forceBackup;

    @Getter
    @Setter
    private boolean changedSchema;

    @Getter
    @Setter
    private Exception lastException;

    @Getter
    @Setter
    private EnvConnection connection;

    @Getter
    @Setter
    private EnvUser user;

    @Getter
    @Setter
    private EnvSchema schema;

    @Getter
    @Setter
    private EnvReviewConfig reviewConfig;

    @Getter
    @Setter
    private EnvParamsConfig paramsConfig;

    @Getter
    @Setter
    private Map<String, Object> preferences;

    public void setSerialNumber(Integer serialNumber) {
        if (serialNumber != null) {
            this.serialNumber = serialNumber;
        }
    }

    public void setStatus(WebSQLContextStatus status) {
        log.debug("{} -> {} - {}", StackTraceUtils.getCurrentMethodName(), status, StackTraceUtils.getPreviousMethodName());
        switch (status) {
            case CLOSED:
            case INTERRUPTED:
                plusTransactionIndex();
                break;
            default:
        }
        this.status = status;
    }

    public boolean isClosed() {
        return this.status == WebSQLContextStatus.CLOSED;
    }

    public boolean isInterrupted() {
        return this.status == WebSQLContextStatus.INTERRUPTED;
    }

    public int getTransactionIndex() {
        return transactionIndexes.computeIfAbsent(token, k -> 0);
    }

    public void plusTransactionIndex() {
        transactionIndexes.put(token, getTransactionIndex() + 1);
    }

    public static void plusTransactionIndex(String token) {
        transactionIndexes.put(token, transactionIndexes.computeIfAbsent(token, k -> 0) + 1);
    }

    public WebSQLContextInfo(DataSourceConnectionHandler dataSourceContainer,
                             String token,
                             Integer purpose,
                             Boolean autoCommit,
                             Boolean autoConnect,
                             Boolean smartCommit,
                             String prefs,
                             Long expirationTime,
                             String charset,
                             ConnectionConfiguration configuration) {
        this.token = token;
        this.purpose = purpose;
        this.autoCommit = autoCommit;
        this.autoConnect = autoConnect;
        this.smartCommit = smartCommit;
        this.prefs = prefs;
        this.expirationTime = expirationTime;
        this.charset = charset;
        this.dataSourceContainer = dataSourceContainer;
        this.dataSource = this.dataSourceContainer.getDataSource();
        this.processor = new WebSQLProcessor(this, true);
        this.transfer = new WebDataTransfer(this);
        this.configuration = configuration;
    }

    //无需openSession
    protected WebSQLContextInfo(String token) {
        this.token = token;
        this.dataSourceContainer = null;
        this.dataSource = null;
        this.processor = new WebSQLProcessor(this, false);
        this.transfer = null;
        this.configuration = null;
    }

    public static String test(TestConnectionMessage message) {
        TestConnectionConfiguration testConnectionConfiguration = message.getTestConnectionConfiguration();
        try (TestDataSourceConnectionHandler handler = TestDataSourceConnectionHandler.handle(testConnectionConfiguration)) {
            if (handler.isConnected()) {
                return handler.getDriverDescriptor().getId();
            }
            throw new ConnectionException(handler.getExceptionMessages(), "测试连接失败！");
        }
    }

    public static void prepare(ConnectionConfig connectionConfig) {
        try {
            ConnectionConfiguration connectionConfiguration = connectionConfig.getConnectionConfiguration();
            DataSourceConnectionHandler handler = DataSourceConnectionHandler.handle(connectionConfiguration);
            if (handler != null && handler.getAllContexts().isEmpty()) {
                final String token = "Preheating " + UUID.randomUUID();
                try {
                    DBCExecutionContext executionContext = handler.getExecutionContext(
                            token,
                            DBCExecutionPurpose.UTIL.getId(),
                            false,
                            false,
                            null,
                            null,
                            connectionConfiguration);
                    log.debug("Preheating connection [{}].", executionContext.getContextId());
                } finally {
                    handler.closeExecutionContext(token);
                }
            }
        } catch (Exception e) {
            throw new ConnectionException(Collections.singletonList(e.getMessage()), "初始化数据源失败！");
        }
    }

    public static List<String> beating(BatchMessage message) {
        return message.getTokens()
                .stream()
                .map(token -> {
                    WebSQLContextInfo contextInfo = null;
                    try {
                        contextInfo = WebSQLContextInfo.getSimpleContext(token);
                        DBCExecutionContext executionContext = contextInfo.getExecutionContext();
                        @SQL String validationQuery = contextInfo.getDataSourceContainer().getPreferenceStore().getString(ModelPreferences.CONNECT_VALIDATION_QUERY);
                        if (validationQuery != null && !StatementHandler.isRunning(token)) {
                            DBExecUtils.execute(MONITOR, executionContext, "Verify Query", validationQuery);
                        }
                        return null;
                    } catch (Exception e) {
                        if (contextInfo != null && DBExecUtils.discoverErrorType(contextInfo.getDataSource(), e) == DBPErrorAssistant.ErrorType.CONNECTION_LOST) {
                            final WebSQLContextInfo finalContextInfo = contextInfo;
                            ContextSubject.trigger(contextObserver -> {
                                try {
                                    contextObserver.printLogCloseLifeCycle(finalContextInfo.getExecutionContext(true), token, "会话丢失", null);
                                } catch (DBException ignored) {
                                }
                            });
                            contextInfo.close();
                        }
                        log.debug("指定 token ({}) 执行心跳失败：{}", token, e.getMessage());
                        return token;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public static WebSQLContextInfo openExecuteContext(ConnectionTokenMessage message) {
        List<String> exceptions = new ArrayList<>(1);
        try {
            ConnectionConfig connectionConfig = message.getConnectionConfig();
            ConnectionConfiguration connectionConfiguration = connectionConfig.getConnectionConfiguration();
            DataSourceConnectionHandler handler = DataSourceConnectionHandler.handle(connectionConfiguration);
            if (handler != null) {
                WebSQLContextInfo contextInfo = new WebSQLContextInfo(
                        handler,
                        message.getToken(),
                        message.getTokenConfig().getPurpose(),
                        message.getTokenConfig().getAutoCommit(),
                        message.getTokenConfig().getAutoConnect(),
                        message.getTokenConfig().getSmartCommit(),
                        message.getTokenConfig().getPrefs(),
                        message.getTokenConfig().getExpirationTime(),
                        message.getTokenConfig().getCharset(),
                        connectionConfiguration);
                DBCExecutionContext executionContext = contextInfo.getExecutionContext();
                String schemaName = connectionConfig.getSchemaName();
                if (StringUtils.isNotBlank(schemaName)) {
                    setDefaults(MONITOR, connectionConfig.getCatalogName(), schemaName, executionContext);
                    // 手动提交
                    if (contextInfo.getDataSource().getInfo().isExecuteErrorTransactionInterrupted() &&
                            message.getTokenConfig().getAutoCommit() != null && !message.getTokenConfig().getAutoCommit()) {
                        commit(executionContext);
                    }
                }
                contexts.put(message.getToken(), contextInfo);
                return contextInfo;
            }
        } catch (MaximumContextSizeException maximumContextSizeException) {
            throw maximumContextSizeException;
        } catch (Exception e) {
            log.error(message.getConnectionConfig().getDriverId() + " - 打开执行上下文失败！", e);
            exceptions.add(e.getMessage());
        }
        throw new ConnectionException(exceptions, "建立连接失败！");
    }

    @SneakyThrows(DBException.class)
    public static void makeExecuteContext(PreferencesMessage message) {

        String token = message.getToken();
        Boolean autoCommit = Boolean.TRUE.equals(message.getAutoCommit());
        Boolean autoConnect = Boolean.TRUE.equals(message.getAutoConnect());

        DBCExecutionContext executionContext = null;

        WebSQLContextInfo context = contexts.get(token);
        if (context != null) {
            if (context.isClosed()) {
                log.info("等待上下文关闭");
                while ((context = contexts.get(token)) != null) {
                    synchronized (context) {
                        if ((context = contexts.get(token)) != null) {
                            try {
                                context.wait();
                            } catch (InterruptedException e) {
                                Thread.currentThread().interrupt();
                            }
                        }
                    }
                }
            } else {
                log.info("上下文已经存在");
                return;
            }
        }

        MessageService messageService = Resource.getBeanRequireNonNull(MessageService.class);
        LifeCycleMessage lifeCycle = new LifeCycleMessage();
        lifeCycle.setWindowId(token);
        lifeCycle.setOperationType(OperationType.OPEN_CONNECTION.getValue());
        lifeCycle.setAutoCommit(autoCommit);
        lifeCycle.setAutoConnect(autoConnect);
        lifeCycle.setPreferences(message.getPreferences());

        log.info("开始构建上下文");

        try {

            EnvRapper envRapper = Resource.getBean(EnvRapper.class);
            assert envRapper != null;

            Env env = envRapper.get(token);
            EnvConnection connection = env.getConnection();
            EnvUser user = env.getUser();
            if (connection == null || user == null) {
                throw new ConnectionException(Collections.singletonList("没有找到连接参数【connection】【user】！"), "建立连接失败！");
            }

            lifeCycle.setOperationContent(String.format(OperationType.OPEN_CONNECTION.getDesc(), connection.getUserName()));

            EnvParamsConfig paramsConfig = env.getParamsConfig();

            ConnectionTokenMessage connectionTokenMessage = new ConnectionTokenMessage();
            ConnectionConfig connectionConfig = SummerMapper.INSTANCE.toConnectionTokenMessage(connection);
            connectionConfig.setHeartBeatTime(Long.parseLong(paramsConfig.getCheckingConnection()) * 60);
            connectionTokenMessage.setConnectionConfig(connectionConfig);

            TokenConfig tokenConfig = new TokenConfig();
            tokenConfig.setPurpose(DBCExecutionPurpose.USER.getId());
            tokenConfig.setExpirationTime(Long.parseLong(paramsConfig.getResultSetCacheTimeout()) * 60);
            tokenConfig.setPrefs(null);
            tokenConfig.setAutoCommit(autoCommit);
            tokenConfig.setAutoConnect(autoConnect);
            connectionTokenMessage.setTokenConfig(tokenConfig);

            ExecuteEvent executeEvent = new ExecuteEvent();
            executeEvent.setUserId(user.getUserId());
            executeEvent.setOperationUser(user.getOperationUser());
            executeEvent.setConnectionPattern(connection.getConnectionPattern());
            connectionTokenMessage.setExecuteEvent(executeEvent);

            connectionTokenMessage.setToken(token);

            FilterIgnoreFiled filterIgnoreFiled = Resource.getBean(FilterIgnoreFiled.class);
            if (filterIgnoreFiled != null) {
                LoggerUtils slf4jUtils = LoggerUtils.receive(LoggerUtils.REDIS);
                slf4jUtils.append("Env", filterIgnoreFiled.handle(JSON.toJSONString(connectionTokenMessage)));
                slf4jUtils.info();
            }

            WebSQLContextInfo contextInfo = openExecuteContext(connectionTokenMessage);

            EnvSchema schema = env.getSchema();
            EnvReviewConfig envReviewConfig = env.getReviewConfig();

            contextInfo.setConnection(connection);
            contextInfo.setUser(user);
            contextInfo.setSchema(schema);
            contextInfo.setParamsConfig(paramsConfig);
            contextInfo.setReviewConfig(envReviewConfig);
            contextInfo.setPreferences(message.getPreferences());

            executionContext = contextInfo.getExecutionContext();

        } catch (ConnectionException e) {
            lifeCycle.setMessage(e.getMessages().get(0));
            throw e;
        } catch (RuntimeException e) {
            lifeCycle.setMessage(e.getMessage());
            throw e;
        } finally {
            messageService.printLogLifeCycle(executionContext, lifeCycle);
        }
    }

    public static WebSQLContextInfo createSimpleContent() {
        return new WebSQLSimpleContextInfo("Simple Token - " + UUID.randomUUID());
    }

    public static WebSQLContextInfo openIsolatedContext(ConnectionConfig connectionConfig) {
        List<String> exceptions = new ArrayList<>(1);
        try {
            ConnectionConfiguration connectionConfiguration = connectionConfig.getConnectionConfiguration();
            DataSourceConnectionHandler handler = DataSourceConnectionHandler.handle(connectionConfiguration);
            if (handler != null) {
                WebSQLContextInfo contextInfo = new WebSQLContextInfo(
                        handler,
                        ISOLATED_TOKEN + " - " + UUID.randomUUID(),
                        DBCExecutionPurpose.USER.getId(),
                        true,
                        true,
                        false,
                        null,
                        0L,
                        connectionConfig.getCharset(),
                        connectionConfiguration);
                String schemaName = connectionConfig.getSchemaName();
                if (StringUtils.isNotBlank(schemaName)) {
                    setDefaults(MONITOR, connectionConfig.getCatalogName(), schemaName, contextInfo.getExecutionContext());
                }
                return contextInfo;
            }
        } catch (Exception e) {
            log.error(connectionConfig.getDriverId() + " - 打开孤立上下文失败！", e);
            exceptions.add(e.getMessage());
        }
        throw new ConnectionException(exceptions, "建立连接失败！");
    }

    public static WebSQLContextInfo getOrOpenContext(ConnectionTokenMessage message) {
        String token = message.getToken();
        if (StringUtils.isBlank(token)) {
            return openIsolatedContext(message.getConnectionConfig());
        } else if (contexts.containsKey(token)) {
            return contexts.get(token);
        } else {
            return openExecuteContext(message);
        }
    }

    public static WebSQLContextInfo getSimpleContext(String token) {
        if (contexts.containsKey(token)) {
            WebSQLContextInfo webSQLContextInfo = contexts.get(token);
            Exception e = webSQLContextInfo.getLastException();
            if (!webSQLContextInfo.getAutoConnect() && e != null && DBExecUtils.discoverErrorType(webSQLContextInfo.getDataSource(), e) == DBPErrorAssistant.ErrorType.CONNECTION_LOST) {
                throw new ConnectionException(null, ExceptionUtils.getRootCause(e).getMessage());
            }
            return webSQLContextInfo;
        } else {
            throw new NotFindTokenException(token);
        }
    }

    public static void recordClosed(String token) {
        if (contexts.containsKey(token)) {
            WebSQLContextInfo webSQLContextInfo = contexts.get(token);
            webSQLContextInfo.setStatus(WebSQLContextStatus.CLOSED);
        }
    }

    public static void closeContext(String token) {
        if (contexts.containsKey(token)) {
            WebSQLContextInfo contextInfo = contexts.get(token);
            contextInfo.setStatus(WebSQLContextStatus.CLOSED);

            Exception e = contextInfo.getLastException();
            final String scene = e != null && DBExecUtils.discoverErrorType(contextInfo.getDataSource(), e) == DBPErrorAssistant.ErrorType.CONNECTION_LOST ?
                    "会话丢失" : "手动关闭";
            ContextSubject.trigger(contextObserver -> {
                try {
                    contextObserver.printLogCloseLifeCycle(contextInfo.getExecutionContext(true), token, scene, null);
                } catch (DBException ignored) {
                }
            });

            contextInfo.close();
        } else {
            ContextSubject.trigger(contextObserver -> contextObserver.printLogCloseLifeCycle(null, token, "手动关闭", "没有找到相关上下文"));
        }
    }

    public static Set<String> getAllTokens() {
        return contexts.keySet();
    }

    public static WebSQLContextInfo getAndSetContext(String token, TokenConfig tokenConfig) {
        WebSQLContextInfo context = getSimpleContext(token);
        if (tokenConfig != null) {
            context.purpose = tokenConfig.getPurpose();
            context.autoCommit = tokenConfig.getAutoCommit();
            context.autoConnect = tokenConfig.getAutoConnect();
            context.smartCommit = tokenConfig.getSmartCommit();
            context.prefs = tokenConfig.getPrefs();
            if (tokenConfig.getExpirationTime() != null) {
                context.expirationTime = tokenConfig.getExpirationTime();
            }
            context.charset = tokenConfig.getCharset();
        }
        return context;
    }

    public static WebSQLContextInfo getAndSetContextAndMakeIfNotFind(String token, TokenConfig tokenConfig, Map<String, Object> preferences) {
        try {
            return WebSQLContextInfo.getAndSetContext(token, tokenConfig);
        } catch (NotFindTokenException e) {
            try {
                log.warn("Could not find context, trying to make session.");
                WebSQLContextInfo.makeExecuteContext(new PreferencesMessage(token, preferences));
                return WebSQLContextInfo.getAndSetContext(token, tokenConfig);
            } catch (Exception exception) {
                log.warn("Try to create session failed.", e);
                throw e;
            }
        }
    }

    public static void delContext(String token) {
        WebSQLContextInfo context = contexts.remove(token);
        if (context != null) {
            synchronized (context) {
                context.notifyAll();
            }
        }
    }

    public DBCExecutionContext getExecutionContext() throws DBException {
        return this.getExecutionContext(false);
    }

    private DBCExecutionContext getExecutionContext(boolean forceGet) throws DBException {
        if (!forceGet && this.isClosed()) {
            throw new DBException(MessageConstants.CLOSED.getMessage());
        }
        return this.dataSourceContainer.getExecutionContext(token, purpose, autoCommit, autoConnect, prefs, charset, configuration);
    }

    public DBCSession openSession(DBRProgressMonitor monitor, DBCExecutionPurpose purpose, String title) throws DBException {
        DBCExecutionContext context = getExecutionContext();
        if (purpose == null) {
            purpose = DBCExecutionPurpose.getById(this.purpose);
        }
        DBCSession session = context.openSession(monitor, purpose, title);
        if (session.getDataSource().getInfo().supportsSetNetworkTimeout()) {
            setNetworkTimeout(session);
        }
        return session;
    }

    private void setNetworkTimeout(DBCSession session) {
        Integer networkTimeout = getConfiguration().getNetworkTimeout();
        try {
            if (session instanceof Connection) {
                if (session.getPurpose() != DBCExecutionPurpose.USER_SCRIPT && networkTimeout != null && networkTimeout > 0) {
                    networkTimeout *= 1000;
                } else {
                    networkTimeout = 0;
                }
                LoggerUtils.print(HandlerCounter.getMarker(), "\n==> {}:\n{} ms", com.dc.utils.StringUtils.firstUpper("setNetworkTimeout"), networkTimeout);
                ((Connection) session).setNetworkTimeout(BufferThread.getExecutor(), networkTimeout);
            }
        } catch (SQLException e) {
            log.error("设置网络超时失败！");
        }
    }

    public static void setDefaults(DBRProgressMonitor monitor, String catalogName, String schemaName, DBCExecutionContext context) throws DBException {
        DBExecUtils.setExecutionContextDefaults(
                monitor,
                context.getDataSource(),
                context,
                catalogName,
                DBExecUtils.getOldCatalogName(context),
                schemaName);
    }

    @NotNull
    public WebSQLResultsInfo saveResult(@NotNull DBSDataContainer dataContainer,
                                        @NotNull DBDAttributeBinding[] attributes) {
        WebSQLResultsInfo resultInfo = new WebSQLResultsInfo(
                dataContainer,
                String.valueOf(resultId.incrementAndGet()));
        resultInfo.setAttributes(attributes);
        resultIdToInfoMap.put(resultInfo.getId(), resultInfo);
        previousWebSQLResultsInfo = resultInfo;
        return resultInfo;
    }

    @NotNull
    public WebSQLResultsInfo getResults(@NotNull String resultId) {
        WebSQLResultsInfo resultsInfo = resultIdToInfoMap.get(resultId);
        if (resultsInfo == null) {
            throw new ServiceException("结果集失效，请重新查询。");
        }
        return resultsInfo;
    }

    public WebSQLResultsInfo getPreviousResults(@NotNull String query) {
        if (previousWebSQLResultsInfo != null && previousWebSQLResultsInfo.getDataContainer().getName().equals(query)) {
            return previousWebSQLResultsInfo;
        }
        previousWebSQLResultsInfo = resultIdToInfoMap.values()
                .stream()
                .filter(webSQLResultsInfo -> webSQLResultsInfo.getDataContainer().getName().equals(query))
                .findFirst()
                .orElse(null);
        return previousWebSQLResultsInfo;
    }

    public boolean closeResult(@NotNull String resultId) {
        if (previousWebSQLResultsInfo != null && previousWebSQLResultsInfo.getId().equals(resultId)) {
            previousWebSQLResultsInfo = null;
        }
        return resultIdToInfoMap.remove(resultId) != null;
    }

    ///////////////////////////////////////////////////////
    // Async model

    public void dispose() {
        previousWebSQLResultsInfo = null;
        resultIdToInfoMap.clear();
    }

    public static final String REDIS_LIST_ID = "Redis List ID: ";

    public static class TaskProgressMonitor extends ProxyProgressMonitor {

        @Getter
        private final WebAsyncTaskInfo asyncTask;

        public TaskProgressMonitor(DBRProgressMonitor original, WebAsyncTaskInfo asyncTask) {
            super(original);
            this.asyncTask = asyncTask;
        }

        @Override
        public void beginTask(String name, int totalWork) {
            super.beginTask(name, totalWork);
        }

        @Override
        public void subTask(String name) {
            super.subTask(name);
            if (name.startsWith(REDIS_LIST_ID)) {
                String id = name.replace(REDIS_LIST_ID, "");
                if (NumberUtils.isDigits(id)) {
                    asyncTask.setStage(Integer.valueOf(id));
                }
            }
        }

        @Override
        public boolean isCanceled() {
            return asyncTask.isCancel();
        }
    }

    public WebAsyncTaskInfo getAsyncTask(String taskId, String taskName, boolean create) {
        if (!create) {
            // 如果不创建，直接get并返回
            return asyncTasks.get(taskId);
        }

        return asyncTasks.computeIfAbsent(taskId, k -> {
            log.debug("Creating new task for taskId: {}", k);
            return new WebAsyncTaskInfo(k, taskName, token);
        });
    }

    public Collection<WebAsyncTaskInfo> getAsyncTasks() {
        return asyncTasks.values();
    }

    public WebAsyncTaskInfo asyncTaskStatus(String taskId, boolean removeOnFinish) throws DBCException {
        WebAsyncTaskInfo taskInfo = asyncTasks.get(taskId);

        if (taskInfo == null) {
            throw new DBCException("Task '" + taskId + "' not found");
        }
        if (removeOnFinish && taskInfo.getStatus() == WebAsyncTaskType.FINISHED) {
            asyncTasks.remove(taskId);
        }
        return taskInfo;
    }

    public void setTaskStatus(String taskId, WebAsyncTaskType status) throws DBCException {
        WebAsyncTaskInfo taskInfo = asyncTasks.get(taskId);

        if (taskInfo == null) {
            throw new DBCException("Task '" + taskId + "' not found");
        }
        if (taskInfo.getJob() != null && !taskInfo.getJob().isFinished()) {
            taskInfo.setStatus(status);
        }
    }


    private AbstractJob createAbstractJob(WebAsyncTaskInfo asyncTask, BiConsumerFunction<String, TaskProgressMonitor> biConsumer) {
        return new AbstractJob(asyncTask.getName()) {
            @Override
            protected IStatus run(DBRProgressMonitor monitor) {
                TaskProgressMonitor taskMonitor = new TaskProgressMonitor(monitor, asyncTask);
                try {
                    taskMonitor.beginTask(asyncTask.getName(), 1);
                    biConsumer.accept(asyncTask.getTaskId(), taskMonitor);
                    asyncTask.setStatus(WebAsyncTaskType.FINISHED);
                } catch (InterruptedException e) {
                    log.error("Task InterruptedException", e);
                    asyncTask.setJobError(e);
                    asyncTask.setStatus(WebAsyncTaskType.INTERRUPTED);
                } catch (DBException dbe) {
                    log.error("Task DBException", dbe);
                    asyncTask.setJobError(dbe);
                    asyncTask.setStatus(WebAsyncTaskType.SQL_ERROR);
                } catch (Throwable t) {
                    log.error("Task Throwable", t);
                    asyncTask.setJobError(t);
                    asyncTask.setStatus(WebAsyncTaskType.SYS_ERROR);
                    taskMonitor.done();
                } finally {
                    taskCount.decrementAndGet();
                    if (isClosed()) {
                        close();
                    }
                }
                if (asyncTask.getJobError() != null) {
                    asyncTask.getJobError().printStackTrace();
                    log.error("createAbstractJob error : ", asyncTask.getJobError());
                }
                return Status.OK_STATUS;
            }
        };
    }

    public WebAsyncTaskInfo createTempTask(String taskName, BiConsumerFunction<String, TaskProgressMonitor> biConsumer) {
        final int taskId = TASK_ID.incrementAndGet();
        WebAsyncTaskInfo asyncTask = new WebAsyncTaskInfo(String.valueOf(taskId), taskName, token);
        asyncTask.setJob(createAbstractJob(asyncTask, biConsumer));
        log.info("{}(Create Temp Async Task): {}", taskName, asyncTask);
        return asyncTask;
    }

    public WebAsyncTaskInfo createAsyncTask(String taskName, BiConsumerFunction<String, TaskProgressMonitor> biConsumer) {
        final int taskId = TASK_ID.incrementAndGet();
        WebAsyncTaskInfo asyncTask = getAsyncTask(String.valueOf(taskId), taskName, true);
        asyncTask.setJob(createAbstractJob(asyncTask, biConsumer));
        log.info("{}(Create Async Task): {}", taskName, asyncTask);
        return asyncTask;
    }

    public void close() {
        this.getDataSourceContainer().closeExecutionContext(this.getToken());
    }

    private static void commit(DBCExecutionContext executionContext) {
        try {
            DBUtils.getTransactionManager(executionContext).commit();
        } catch (DBCException e) {
            log.error("commit fail!", e);
        }
    }

    public void confirmInterrupted(Exception... exceptions) throws InterruptedException {
        if (ArrayUtils.isNotEmpty(exceptions)) {
            log.error("{} Confirm Interrupted.", StackTraceUtils.getPreviousMethodName(), exceptions[0]);
        }
        if (this.isClosed()) {
            log.debug("This context is closed, token is {}, method is {}", getToken(), StackTraceUtils.getPreviousMethodName());
            throw new InterruptedException(MessageConstants.CLOSED.getMessage());
        } else if (this.isInterrupted()) {
            log.debug("This context is interrupted, token is {} method is {}", getToken(), StackTraceUtils.getPreviousMethodName());
            throw new InterruptedException(MessageConstants.INTERRUPTED.getMessage());
        }
    }

    public Runnable recoverBefore() {
        return () -> {
            LifeCycleMessage message = new LifeCycleMessage();
            message.setWindowId(getToken());
            message.setOperationType(OperationType.CLOSE_CONNECTION.getValue());
            message.setOperationContent(String.format(OperationType.CLOSE_CONNECTION.getDesc(), "会话丢失"));
            try {
                Resource.getBeanRequireNonNull(MessageService.class).printLogLifeCycle(getExecutionContext(true), message);
            } catch (DBException e) {
                log.error("get recoverBefore error.", e);
            }
        };
    }

    public Runnable recoverAfter() {
        return () -> {
            LifeCycleMessage message = new LifeCycleMessage();
            message.setWindowId(getToken());
            message.setOperationType(OperationType.OPEN_CONNECTION.getValue());
            message.setOperationContent(String.format(OperationType.OPEN_CONNECTION.getDesc(), getConfiguration().getUserName()));
            message.setPreferences(getPreferences());
            try {
                Resource.getBeanRequireNonNull(MessageService.class).printLogLifeCycle(getExecutionContext(true), message);
            } catch (DBException e) {
                log.error("get recoverAfter error.", e);
            }
        };
    }

    public Boolean getAutoConnect() {
        return Boolean.TRUE.equals(autoConnect);
    }

}
