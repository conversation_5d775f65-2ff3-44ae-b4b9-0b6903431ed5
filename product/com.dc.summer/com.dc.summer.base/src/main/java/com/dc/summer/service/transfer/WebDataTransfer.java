
package com.dc.summer.service.transfer;

import com.dc.springboot.core.model.database.TokenConfig;
import com.dc.springboot.core.model.type.OriginType;
import com.dc.summer.ModelPreferences;
import com.dc.summer.component.SummerMapper;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.data.DBDAttributeBinding;
import com.dc.summer.model.data.result.ConsoleType;
import com.dc.summer.model.data.result.DataResultNodeInfo;
import com.dc.summer.model.data.result.DataResultVisitor;
import com.dc.config.ApiConfig;
import com.dc.springboot.core.model.data.ResultFormat;
import com.dc.springboot.core.model.message.MessageConstants;
import com.dc.springboot.core.model.sensitive.DataDesensitizeProcessor;
import com.dc.springboot.core.model.sensitive.ExportDesensitizeProcessor;
import com.dc.summer.DBException;
import com.dc.springboot.core.component.Resource;
import com.dc.summer.config.SummerConfig;
import com.dc.summer.model.DBPContextProvider;
import com.dc.springboot.core.model.chain.ChainBuilder;
import com.dc.summer.model.data.DBDDataDesensitizeProcessor;
import com.dc.summer.model.data.message.SqlExportMessage;
import com.dc.springboot.core.model.exception.ServiceException;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.exec.DBCExecutionContextDefaults;
import com.dc.summer.model.sql.SQLQueryContainer;
import com.dc.summer.model.sql.SqlFieldData;
import com.dc.springboot.core.model.type.ColumnDelimiterType;
import com.dc.springboot.core.model.type.ExportType;
import com.dc.summer.model.struct.rdb.DBSSchema;
import com.dc.summer.service.container.WebDataTransferContainer;
import com.dc.summer.service.container.WebSQLQueryDataContainer;
import com.dc.summer.service.result.WorkOrderTransferResult;
import com.dc.summer.service.sql.WebSQLContextInfo;
import com.dc.springboot.core.model.result.WebSQLTransferResult;
import com.dc.summer.data.transfer.DTConstants;
import com.dc.summer.data.transfer.IDataTransferConsumer;
import com.dc.summer.data.transfer.IDataTransferProcessor;
import com.dc.summer.data.transfer.database.DatabaseProducerSettings;
import com.dc.summer.data.transfer.database.DatabaseTransferProducer;
import com.dc.summer.data.transfer.registry.DataTransferProcessorDescriptor;
import com.dc.summer.data.transfer.stream.IStreamDataExporter;
import com.dc.summer.data.transfer.stream.StreamConsumerSettings;
import com.dc.summer.data.transfer.stream.StreamTransferConsumer;
import com.dc.summer.model.preferences.DBPPropertyDescriptor;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSDataContainer;
import com.dc.summer.service.sql.WebSQLContextStatus;
import com.dc.summer.service.sql.WebSQLDataDraw;
import com.dc.springboot.core.model.result.WebSQLQueryResultSet;
import com.dc.springboot.core.model.result.WebSQLResultsInfo;
import com.dc.springboot.core.model.result.WebSQLQueryResult;
import com.dc.springboot.core.model.execution.ValidExecuteModel;
import com.dc.summer.model.exec.DBCExecutionPurpose;
import com.dc.summer.model.type.WebDataFormat;
import com.dc.summer.model.impl.data.PrimaryKeyProcessor;
import com.dc.utils.CommonUtils;
import com.dc.utils.io.ByteOrderMark;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.core.runtime.IAdaptable;
import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.function.Supplier;

@Slf4j
public class WebDataTransfer {

    private final WebSQLContextInfo contextInfo;

    private final WebDataTransferHelper helper;

    private final int exportRows;

    private final long exportSize;

    private final String dcBackendPath;

    private String queryText;

    /**
     * SQL窗口
     */
    public WebDataTransfer(WebSQLContextInfo contextInfo) {
        this.contextInfo = contextInfo;
        this.helper = new WebDataTransferHelper(contextInfo.getToken(), contextInfo::dispose);
        SummerConfig config = Resource.getBean(SummerConfig.class);
        this.exportRows = config.getExport().getRows();
        this.exportSize = config.getExport().getSize();
        this.dcBackendPath = config.getPath().getDcBackend();
    }

    /**
     * 工单
     */
    public WebDataTransfer(String orderId, WebSQLContextInfo contextInfo) {
        this.contextInfo = contextInfo;
        this.helper = new WebDataTransferHelper(String.format("order_%s", orderId), null);
        SummerConfig config = Resource.getBean(SummerConfig.class);
        this.exportRows = config.getExport().getRows();
        this.exportSize = config.getExport().getSize();
        this.dcBackendPath = config.getPath().getDcBackend();
    }

    public WorkOrderTransferResult exportDataBySql(
            DBRProgressMonitor monitor,
            DataTransferProcessorDescriptor processor,
            SqlExportMessage sqlExportMessage,
            WebDataTransferName transferZipName,
            WebDataTransferName transferFileName,
            boolean needToZipAndUpload,
            boolean deleteResultInfo,
            String sql,
            DBDDataDesensitizeProcessor dataDesensitize,
            long total, Integer consoleType, String dbName) throws DBException {

        helper.reloading(needToZipAndUpload, true, deleteResultInfo, transferZipName, transferFileName);

        WorkOrderTransferResult orderTransferResult = new WorkOrderTransferResult();

        try {

            Map<String, Object> properties = getProperties(sqlExportMessage);

            Path exportFile = helper.getExportFile(1);

            WebSQLQueryDataContainer dataContainer = new WebSQLQueryDataContainer(contextInfo.getDataSource(), contextInfo.getExecutionContext(), sql, total, null);

            contextInfo.getDataSource().getContainer().getPreferenceStore().setValue(ModelPreferences.RESULT_SET_MAX_ROWS_USE_SQL, true);

            WebSQLResultsInfo resultsInfo = new WebSQLResultsInfo(dataContainer, null);
            resultsInfo.setDataDesensitize(dataDesensitize);
            resultsInfo.setPrimaryKeyModel(new PrimaryKeyProcessor());

            ResultFormat resultFormat = initUseNativeFormat(sqlExportMessage, resultsInfo, dataContainer);

            AtomicReference<Boolean> sensitiveSelect = new AtomicReference<>(null);
            TokenConfig tokenConfig = new TokenConfig();
            tokenConfig.setConsole(consoleType);
            tokenConfig.setDbName(dbName);
            File file = this.exec(
                    monitor,
                    processor,
                    dataContainer,
                    sqlExportMessage.getFileCharset(),
                    properties,
                    exportFile,
                    resultsInfo,
                    0,
                    0,
                    resultFormat,
                    (DataDesensitizeProcessor) resultsInfo.getDataDesensitize(),
                    resultsInfo.getSqlFieldDataList(),
                    sqlExportMessage.isExcelUseNumberFormat(),
                    sensitiveSelect,
                    helper,
                    1,
                    null,
                    tokenConfig,
                    exportSize,
                    null);
            orderTransferResult.setSensitiveSelect(sensitiveSelect.get());
            orderTransferResult.setMessage(String.format("执行成功，当前返回：[%s]行", resultsInfo.getRowSize() > 0 ? resultsInfo.getRowSize() : 0));
            orderTransferResult.setQueryText(queryText);
            helper.addExportFile(file, true);
        } catch (Exception e) {
            log.error("导出失败！", e);
            throw new DBException(e.getCause().getMessage());
        }
        orderTransferResult.setFile(helper.toSingleExportFile(getProcessorFileExtension(processor)));

        return orderTransferResult;
    }

    public WebSQLTransferResult exportDataByContext(
            DBRProgressMonitor monitor,
            DataTransferProcessorDescriptor processor,
            Supplier<List<WebSQLQueryResultSet>> supplier,
            SqlExportMessage sqlExportMessage,
            String userId,
            Function<List<WebSQLQueryResultSet>, ChainBuilder<WebSQLTransferResult>> streamFunction,
            WebDataTransferName transferZipName,
            WebDataTransferName transferFileName,
            boolean needToZipAndUpload,
            boolean isSingle,
            boolean deleteResultInfo,
            String customFileName,
            TokenConfig tokenConfig,
            OriginType originType) {

        contextInfo.setStatus(WebSQLContextStatus.NORMAL);

        long startTime = System.currentTimeMillis();

        helper.reloading(needToZipAndUpload, isSingle, deleteResultInfo, transferZipName, transferFileName);

        WebSQLTransferResult transferResult = new WebSQLTransferResult();

        List<WebSQLQueryResultSet> resultSets = null;

        try {

            resultSets = supplier.get();

            Map<String, Object> properties = getProperties(sqlExportMessage);

            // 如果有executeModel，并且resultSets为空，则使用executeModel进行处理，否则使用原有的resultSets逻辑
            if (CollectionUtils.isNotEmpty(sqlExportMessage.getBatchExecuteModels()) && resultSets.isEmpty()) {
                int batchExecuteSize = sqlExportMessage.getBatchExecuteModels().size();
                for (ValidExecuteModel executeModel : sqlExportMessage.getBatchExecuteModels()) {
                    log.info("使用executeModel进行导出处理");

                    if (StringUtils.isNotBlank(customFileName)) {
                        helper.setTransferFileName(new WebDataTransferName.CustomResultSetName(customFileName));
                    }

                    contextInfo.confirmInterrupted();

                    int fileSize = contextInfo.getDataSource().getInfo().supportsMergeExportFile() ? 1 : batchExecuteSize;

                    Path exportFile = helper.getExportFile(fileSize);

                    // 使用executeModel直接创建数据容器，避免依赖resultsInfo
                    DBCExecutionContext context = contextInfo.getExecutionContext();
                    String sql = executeModel.getSql();
                    String tableName = executeModel.getTableName(); // 可以从executeModel中获取，如果需要的话

                    DBSDataContainer dataContainer;
                    long offset = 0;
                    long exportLimit = executeModel.getExportLimit();

                    boolean isDesensitize = sqlExportMessage.isExportDesensitize();

                    switch (sqlExportMessage.getPageSelected()) {
                        case CURRENT_PAGE:
                            log.info("当前页导出 : 使用executeModel，pageSize: {}", executeModel.getPageSize());
                            dataContainer = new WebSQLQueryDataContainer(context.getDataSource(), context, sql, executeModel.getPageSize(), tableName);
                            offset = executeModel.getOffset();
                            break;
                        case ALL_PAGES:
                            log.info("全部页导出 : 使用executeModel");
                            dataContainer = new WebSQLQueryDataContainer(context.getDataSource(), context, sql, 0, tableName);
                            break;
                        default:
                            throw new ServiceException("Not find pageSelected");
                    }

                    ResultFormat resultFormat = sqlExportMessage.getResultFormat() != null ?
                            sqlExportMessage.getResultFormat() : new ResultFormat();

                    // 创建默认的脱敏处理器
                    DataDesensitizeProcessor dataDesensitize = new DataDesensitizeProcessor();
                    if (isDesensitize) {
                        dataDesensitize = new ExportDesensitizeProcessor(dataDesensitize);
                    }

                    File file = this.exec(
                            monitor,
                            processor,
                            dataContainer,
                            sqlExportMessage.getFileCharset(),
                            properties,
                            exportFile,
                            null, // 不使用resultsInfo
                            offset,
                            exportLimit,
                            resultFormat,
                            dataDesensitize,
                            executeModel.getSqlFieldDataList(),
                            sqlExportMessage.isExcelUseNumberFormat(),
                            new AtomicReference<>(),
                            helper,
                            fileSize,
                            null, // resultName
                            tokenConfig,
                            ObjectUtils.getIfNull(sqlExportMessage.getSplitFileSize(), () -> exportSize),
                            executeModel.getData());
                    helper.addExportFile(file, false);

                    if (monitor.isCanceled()) {
                        throw new DBException(MessageConstants.INTERRUPTED.getMessage());
                    }
                }
            } else {
                // 原有的resultSets处理逻辑
                for (WebSQLQueryResultSet resultSet : resultSets) {

                    if (StringUtils.isNotBlank(customFileName)) {
                        helper.setTransferFileName(new WebDataTransferName.CustomResultSetName(customFileName));
                    }

                    contextInfo.confirmInterrupted();

                    int fileSize = contextInfo.getDataSource().getInfo().supportsMergeExportFile() ? 1 : resultSets.size();

                    Path exportFile = helper.getExportFile(fileSize);

                    WebSQLResultsInfo resultsInfo = contextInfo.getResults(resultSet.getResultId());

                    DataDesensitizeProcessor dataDesensitize = (DataDesensitizeProcessor) resultsInfo.getDataDesensitize();

                    DBSDataContainer dataContainer = resultsInfo.getDataContainer();
                    String tableName = null;
                    if (dataContainer instanceof IAdaptable) {
                        tableName = ((IAdaptable) dataContainer).getAdapter(SQLQueryContainer.class).getTableName();
                    }

                    boolean isUseNativeDateFormat = sqlExportMessage.getExcelUseOriginalFormat() == 1 && resultsInfo.getResultFormat().getUseNativeFormat();

                    boolean isDateType = ExportType.XLSX.equals(sqlExportMessage.getExportType()) && resultsInfo.isDateType() && !isUseNativeDateFormat;

                    boolean isDesensitize = sqlExportMessage.isExportDesensitize();

                    boolean isSwitchConsoleType = !ConsoleType.equalsByNullable(dataDesensitize.getConsoleType(), ConsoleType.of(tokenConfig == null ? null : tokenConfig.getConsole()));

                    long offset = 0;

                    switch (sqlExportMessage.getPageSelected()) {
                        case CURRENT_PAGE:
                            log.info("当前页导出 : containClob: {}, isDateType: {}, isDesensitize: {}, isSwitchConsoleType: {}", resultsInfo.isContainClob(), isDateType, isDesensitize, isSwitchConsoleType);
                            if (resultsInfo.isContainClob() || isDateType || isDesensitize || isSwitchConsoleType) {
                                log.info("当前页导出 : 重新查询");
                                DBCExecutionContext context = ((DBPContextProvider) dataContainer).getExecutionContext();
                                long pageSize = resultSet.getPageSize();
                                dataContainer = new WebSQLQueryDataContainer(context.getDataSource(), context, resultSet.getSql(), pageSize, tableName);
                                offset = resultsInfo.getOffset();
                            } else {
                                log.info("当前页导出 : 未重新查询");
                                dataContainer = new WebDataTransferContainer(null, resultSet, resultsInfo, tableName);
                                properties.put(DTConstants.CONTEXT_SCHEMA_NAME, getContextSchemaName(contextInfo.getExecutionContext(), dataContainer.getDataSource()));
                            }
                            break;
                        case ALL_PAGES:
                            DBCExecutionContext context = ((DBPContextProvider) dataContainer).getExecutionContext();
                            dataContainer = new WebSQLQueryDataContainer(context.getDataSource(), context, resultSet.getSql(), 0, tableName);
                            break;
                        default:
                            throw new ServiceException("Not find pageSelected");
                    }

                    ResultFormat resultFormat = initUseNativeFormat(sqlExportMessage, resultsInfo, dataContainer);

                    if (isDesensitize) {
                        dataDesensitize = new ExportDesensitizeProcessor(dataDesensitize);
                    }
                    File file = this.exec(
                            monitor,
                            processor,
                            dataContainer,
                            sqlExportMessage.getFileCharset(),
                            properties,
                            exportFile,
                            resultsInfo,
                            offset,
                            resultSet.getExportRowsLimit(),
                            resultFormat,
                            dataDesensitize,
                            resultsInfo.getSqlFieldDataList(),
                            sqlExportMessage.isExcelUseNumberFormat(),
                            new AtomicReference<>(),
                            helper,
                            fileSize,
                            resultSet.getResultName(),
                            tokenConfig,
                            ObjectUtils.getIfNull(sqlExportMessage.getSplitFileSize(), () -> exportSize),
                            resultSet.getData());
                    helper.addExportFile(file, false);

                    resultSet.setExportRowsCount(resultsInfo.getRowSize());
                    if (!resultSet.isExportLimit()) {
                        resultSet.setExportLimit(resultsInfo.isExportLimit());
                    }

                    if (monitor.isCanceled()) {
                        throw new DBException(MessageConstants.INTERRUPTED.getMessage());
                    }
                }
            }

            transferResult.setMessage("导出结果集成功！");
            transferResult.setSuccess(true);
        } catch (Exception e) {
            contextInfo.setLastException(e);
            log.error("导出失败！", e);
            transferResult.setMessage(e.getMessage());
            transferResult.setSuccess(false);
        } finally {
            helper.tryToZipAndUpload(transferResult.isSuccess(), dcBackendPath, userId, sqlExportMessage.getEncryptPassword(), transferResult,
                    getProcessorFileExtension(processor), ApiConfig.UPLOAD.getPath(), originType);
            transferResult.setDuration(System.currentTimeMillis() - startTime);
            streamFunction.apply(resultSets).exec(transferResult);
        }

        return transferResult;
    }

    private String getContextSchemaName(DBCExecutionContext executionContext, DBPDataSource dataSource) {
        DBSSchema defaultSchema = Optional.ofNullable(executionContext).map(DBCExecutionContext::getContextDefaults).map(DBCExecutionContextDefaults::getDefaultSchema).orElse(null);
        if (defaultSchema != null) {
            return DBUtils.getQuotedIdentifier(dataSource, defaultSchema.getName());
        }
        return null;
    }

    private static ResultFormat initUseNativeFormat(SqlExportMessage sqlExportMessage, WebSQLResultsInfo resultsInfo, DBSDataContainer dataContainer) {
        boolean useNativeFormat;
        ResultFormat resultFormat;
        if (ExportType.XLSX.equals(sqlExportMessage.getExportType())) {
            resultFormat = new ResultFormat();
            resultFormat.setExcelDateTimeFormat(sqlExportMessage.getExcelDatetimeFormat());
            resultFormat.setDateTimeFormat(resultsInfo.getResultFormat().getDateTimeFormat());
            resultFormat.setDateFormat(resultsInfo.getResultFormat().getDateFormat());
            resultFormat.setTimeFormat(resultsInfo.getResultFormat().getTimeFormat());
            useNativeFormat = sqlExportMessage.getExcelUseOriginalFormat() == 1;
        } else {
            resultFormat = resultsInfo.getResultFormat();
            useNativeFormat = resultsInfo.getResultFormat().getUseNativeFormat();
        }
        //导出设置默认采用时间日期原格式
        try {
            dataContainer.getDataSource().getContainer().setUseNativeDateTimeFormat(useNativeFormat);
        } catch (Exception e) {
            log.error("设置时间日期原格式错误");
        }
        return resultFormat;
    }


    @NotNull
    private Map<String, Object> getProperties(SqlExportMessage sqlExportMessage) {
        Map<String, Object> properties = new HashMap<>();
        if (sqlExportMessage.getLineDelimiter() != null) {
            properties.put(DTConstants.LINE_DELIMITER, sqlExportMessage.getLineDelimiter().getValue());
        }
        if (sqlExportMessage.getTextIdentifier() != null) {
            properties.put(DTConstants.TEXT_IDENTIFIER, sqlExportMessage.getTextIdentifier().getValue());
        }
        if (sqlExportMessage.getColumnDelimiter() != null) {
            if (sqlExportMessage.getColumnDelimiter() == ColumnDelimiterType.OTHER) {
                properties.put(DTConstants.COLUMN_DELIMITER, sqlExportMessage.getOtherDelimiter());
            } else {
                properties.put(DTConstants.COLUMN_DELIMITER, sqlExportMessage.getColumnDelimiter().getValue());
            }
        }
        properties.put(DTConstants.RESULT_STAGE, sqlExportMessage.getStage());

        properties.put(DTConstants.PROP_WATERMARK_CONTENT, sqlExportMessage.getWatermarkContent());
        properties.put(DTConstants.PROP_WATERMARK_ANGLE, sqlExportMessage.getWatermarkAngle());
        return properties;
    }

    private File exec(
            DBRProgressMonitor monitor,
            DataTransferProcessorDescriptor processor,
            DBSDataContainer dataContainer,
            String fileCharset,
            Map<String, Object> properties,
            Path exportFile,
            WebSQLResultsInfo resultsInfo,
            long offset,
            long exportLimit,
            ResultFormat resultFormat,
            DataDesensitizeProcessor dataDesensitize,
            List<SqlFieldData> sqlFieldDataList,
            boolean excelUseNumberFormat,
            AtomicReference<Boolean> sensitiveSelect,
            WebDataTransferHelper helper,
            int fileSize,
            String resultName,
            TokenConfig tokenConfig,
            long exportSize,
            List<Object> data) throws DBException, InterruptedException {

        IDataTransferProcessor processorInstance = processor.getInstance();
        if (!(processorInstance instanceof IStreamDataExporter)) {
            throw new DBException("Invalid processor. " + IStreamDataExporter.class.getSimpleName() + " expected");
        }
        try {

            DataResultVisitor dataResultVisitor = Resource.getBean(DataResultVisitor.class);

            if (dataResultVisitor != null) {
                DataResultNodeInfo dataResultNodeInfo = new DataResultNodeInfo();
                dataResultNodeInfo.setConnectionConfiguration(dataContainer.getDataSource().getContainer().getActualConnectionConfiguration());
                dataResultNodeInfo.setExport(true);
                dataResultNodeInfo.setConsoleType(ConsoleType.of(tokenConfig == null ? null : tokenConfig.getConsole()));
                SummerMapper.INSTANCE.updateDataResultNodeInfo(dataResultNodeInfo, dataDesensitize.getGradedClassifiedModel());
                if (this.contextInfo != null && this.contextInfo.getConnection() != null && StringUtils.isNotBlank(this.contextInfo.getConnection().getDbName())) {
                    dataResultNodeInfo.setDbName(this.contextInfo.getConnection().getDbName());// 导出取env 里的oracle dbName , 仅用于oracle
                }

                if (tokenConfig != null && StringUtils.isNotBlank(tokenConfig.getDbName())) {
                    dataResultNodeInfo.setDbName(tokenConfig.getDbName());//工单导出 导出取applyContent   dbName , 目前仅用于oracle
                }
                dataResultNodeInfo.accept(dataResultVisitor);
            }

            StreamTransferConsumer consumer = new StreamTransferConsumer(
                    new WebSQLDataDraw(),
                    dataDesensitize,
                    resultsInfo != null ? resultsInfo.getPrimaryKeyModel() : new PrimaryKeyProcessor(),
                    resultFormat.getDateTimeFormat(),
                    resultFormat.getTimeFormat(),
                    resultFormat.getDateFormat(),
                    sqlFieldDataList,
                    dataResultVisitor,
                    excelUseNumberFormat,
                    resultName);

            IStreamDataExporter exporter = (IStreamDataExporter) processorInstance;

            StreamConsumerSettings settings = new StreamConsumerSettings();

            settings.setOutputFolder(exportFile.getParent().toAbsolutePath().toString());
            //判定是否为自定义文件名类型，如果是，就需要重新初始化开始时每个结果集的文件名（多个结果集的情况）
            if (helper.getTransferFileName() instanceof WebDataTransferName.CustomResultSetName) {
                settings.setOutputFilePattern(helper.getCustomExportFile(fileSize).getFileName().toString());
            } else {
                settings.setOutputFilePattern(exportFile.getFileName().toString());
            }
            settings.setSplitOutFiles(true);
            settings.setMaxOutFileSize(exportSize * 1000 * 1000);
            if (processor.getId().equals(ExportType.CSV.getId())) {
                settings.setOutputEncodingBOM(ByteOrderMark.UTF_8.getCharsetName());
            }
            if (StringUtils.isNotBlank(fileCharset)) {
                settings.setOutputEncoding(fileCharset);
            }
            if (contextInfo.getDataSource().getInfo().supportsMergeExportFile()) {
                settings.setAppendToFileEnd(true);
            }

            for (DBPPropertyDescriptor prop : processor.getProperties()) {
                properties.put(prop.getId(), prop.getDefaultValue());
            }

            properties.put("dateFormat", resultFormat.getExcelDateTimeFormat());

            consumer.initTransfer(
                    dataContainer,
                    settings,
                    new IDataTransferConsumer.TransferParameters(processor.isBinaryFormat(), processor.isHTMLFormat(), helper.getExportDate()),
                    exporter,
                    properties);

            DatabaseTransferProducer producer = new DatabaseTransferProducer(
                    dataContainer,
                    null);
            DatabaseProducerSettings producerSettings = new DatabaseProducerSettings();
            producerSettings.setExtractType(DatabaseProducerSettings.ExtractType.SINGLE_QUERY);
            producerSettings.setQueryRowCount(false);
            producerSettings.setOpenNewConnections(false);
            producerSettings.setFetchSize(exportRows);
            producerSettings.setOffset(offset);
            producerSettings.setData(data);
            producerSettings.setExportLimit(exportLimit);
            producerSettings.setStage((int) properties.getOrDefault(DTConstants.RESULT_STAGE, 0));

            producer.transferData(monitor, consumer, null, producerSettings, null, contextInfo.recoverBefore(), contextInfo.recoverAfter());

            queryText = producer.getStatistics().getQueryText();

            if (resultsInfo != null) {
                resultsInfo.setRowSize(producer.getStatistics().getRowsFetched());
                resultsInfo.setExportLimit(producer.getStatistics().isExportLimit());
            }

            consumer.finishTransfer(monitor, false);

            if (dataResultVisitor != null) {
                DBDAttributeBinding[] columnBindings = consumer.getColumnBindings();
                if (columnBindings != null) {
                    sensitiveSelect.set(
                            Arrays.stream(columnBindings)
                                    .filter(Objects::nonNull)
                                    .anyMatch(DBDAttributeBinding::isDesensitized)
                    );
                }
            }
            //如果是自定义文件名类型，就需要在consumer执行完成后，把真实的文件名设置到helper中，
            //以便上传的时候能找到文件，并且根据真实的文件名返回真实的文件路径
            if (helper.getTransferFileName() instanceof WebDataTransferName.CustomResultSetName) {
                helper.setTransferFileName(new WebDataTransferName.CustomResultSetName(consumer.getCustomFileName()));
                exportFile = helper.getDataExportFolder().resolve(consumer.getCustomFileName());
            }

            return exportFile.toFile();
        } catch (Exception e) {
            contextInfo.setLastException(e);

            if (Files.exists(exportFile)) {
                try {
                    Files.delete(exportFile);
                } catch (IOException ex) {
                    log.error("Error deleting export file " + exportFile.toAbsolutePath(), e);
                }
            } else {
                for (Throwable cause = e; cause != null; cause = cause.getCause()) {
                    if (cause instanceof FileNotFoundException && cause.getMessage().contains("File name too long")) {
                        throw new DBException("文件名过长，导出失败", e);
                    }
                }
            }

            contextInfo.confirmInterrupted(e);

            if (e instanceof DBException) {
                throw e;
            }
            throw new DBException("Error exporting data: " + e.getMessage(), e);
        }

    }

    private String getProcessorFileExtension(DataTransferProcessorDescriptor processor) {
        DBPPropertyDescriptor extProperty = processor.getProperty("extension");
        String ext = extProperty == null ? processor.getAppFileExtension() : CommonUtils.toString(extProperty.getDefaultValue(), null);
        return CommonUtils.isEmpty(ext) ? "data" : ext;
    }

}
