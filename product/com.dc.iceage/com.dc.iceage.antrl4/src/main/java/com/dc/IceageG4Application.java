package com.dc;

import com.dc.iceage.config.IceageConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;

@Slf4j
@SpringBootApplication
@ComponentScan({"com.dc", "com.pingan"})
@EnableConfigurationProperties(IceageConfig.class)
public class IceageG4Application {

    public static void main(String[] args) {
        SpringApplication.run(IceageG4Application.class, args);
        log.info("Iceage G4 已启动！");
    }

}
