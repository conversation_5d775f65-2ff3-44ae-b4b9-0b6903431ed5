package com.dc.workorder.service.check;

import com.dc.iceage.config.IceageConfig;
import com.dc.parser.service.SqlCheckService;
import com.dc.parser.service.sql.WebSQLParserInfo;
import com.dc.repository.mysql.column.OrderApplyContent;
import com.dc.repository.mysql.column.SqlScript;
import com.dc.repository.mysql.model.*;
import com.dc.repository.mysql.service.OrderSqlParserService;
import com.dc.repository.mysql.service.WorkOrderSqlCheckService;
import com.dc.springboot.core.component.JSON;
import com.dc.springboot.core.component.Resource;
import com.dc.springboot.core.model.parser.ParserCheckMessage;
import com.dc.springboot.core.model.parser.dto.CheckResultDto;
import com.dc.springboot.core.model.recovery.BackupModel;
import com.dc.springboot.core.model.result.WebSQLParserResult;
import com.dc.springboot.core.model.type.OriginType;
import com.dc.springboot.core.model.type.ParserExecuteType;
import com.dc.springboot.core.model.type.PreCheckStatus;
import com.dc.springboot.core.model.type.SqlVerifyStatus;
import com.dc.springboot.core.model.workorder.SchemaInfo;
import com.dc.type.AuditLevelType;
import com.dc.type.AuditOriginType;
import com.dc.utils.Pair;
import com.dc.utils.PairList;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Slf4j
public abstract class WorkOrderCheckSql {

    protected final OrderSqlParserService orderSqlParserService;

    private final WorkOrderSqlCheckService workOrderSqlCheckService;

    protected final IceageConfig iceageConfig;

    protected final SimpleDateFormat format = new SimpleDateFormat("yyyy/MM/dd");

    private final SqlCheckService sqlCheckService;

    private final OriginType originType;

    private final List<ParserExecuteType> parserExecuteTypes;


    private int reminder = 0; // sql审核(用来计算提示多少条)
    private int warning = 0; // sql审核(用来计算警告多少条)
    private int error = 0; // sql审核(用来计算错误多少条)
    private int pass = 0; // sql审核(用来计算通过多少条)
    private int skip = 0;   // sql审核(用来计算跳过多少条)
    protected int verifySuccessTotal = 0; // 语法校验成功多少条
    protected int verifyFailTotal = 0; // 语法校验失败多少条
    protected int verifySkipTotal = 0; // 跳过语法校验多少条

    /**
     * 数据导入 批量
     */
    public static final int BATCH_OPERATE = 2;

    private WebSQLParserInfo webSQLParserInfo = null;

    protected WorkOrderCheckSql(OriginType originType, List<ParserExecuteType> parserExecuteTypes) {
        this.orderSqlParserService = Resource.getBeanRequireNonNull(OrderSqlParserService.class);
        this.workOrderSqlCheckService = Resource.getBeanRequireNonNull(WorkOrderSqlCheckService.class);
        this.iceageConfig = Resource.getBeanRequireNonNull(IceageConfig.class);
        this.sqlCheckService = Resource.getBeanRequireNonNull(SqlCheckService.class);
        this.originType = originType;
        this.parserExecuteTypes = parserExecuteTypes == null ? Collections.emptyList() : parserExecuteTypes;
    }

    public abstract PreCheckStatus parseContentAndInsert(Order order, SqlScript sqlScript, WorkOrderScript workOrderScript);

    public PreCheckStatus parseContentAndInsert(Order order, SqlScript sqlScript, OrderSchemaTask orderSchemaTask, SchemaInfo schemaInfo) {
        return null;
    }

    public void clear() {
        this.reminder = 0;
        this.warning = 0;
        this.error = 0;
        this.pass = 0;
        this.skip = 0;
        this.verifySuccessTotal = 0;
        this.verifyFailTotal = 0;
        this.verifySkipTotal = 0;
    }

    protected WebSQLParserResult preCheckParser(String sql, Order order, OrderApplyContent applyContent) {
        if (webSQLParserInfo == null) {
            ParserCheckMessage parserCheckMessage = new ParserCheckMessage();
            parserCheckMessage.setSql(sql);
            parserCheckMessage.setDbType(applyContent.getDb_type());
            parserCheckMessage.setConnectId(applyContent.getConnect_id());
            parserCheckMessage.setSchemaId(applyContent.getSchema_id());
            parserCheckMessage.setUserId(order.getApply_user());
            parserCheckMessage.setIsVerify(ParserExecuteType.makeIsVerify(parserExecuteTypes));
            parserCheckMessage.setOrderMaskLevel(applyContent.getEnable_desensitize_type());
            parserCheckMessage.setOrigin(originType != null ? (long) originType.getValue() : null);
            parserCheckMessage.setAuditOrigin(AuditOriginType.DATA_CHANGE.getValue());
            webSQLParserInfo = WebSQLParserInfo.openCheckParser(parserCheckMessage);
        } else {
            webSQLParserInfo.getDto().setSql(sql);
        }

        return webSQLParserInfo.getProcessor().process((parserParamDto, sqlParseModel) ->
                sqlCheckService.prepareCheck(parserParamDto, sqlParseModel, webSQLParserInfo));
    }

    protected WebSQLParserResult preCheckParser(String sql, Order order, SchemaInfo schemaInfo, OrderApplyContent applyContent) {

        if (webSQLParserInfo == null) {
            ParserCheckMessage parserCheckMessage = new ParserCheckMessage();
            parserCheckMessage.setSql(sql);
            parserCheckMessage.setDbType(schemaInfo.getDb_type());
            parserCheckMessage.setConnectId(schemaInfo.getConnect_id());
            parserCheckMessage.setSchemaId(schemaInfo.getSchema_id());
            parserCheckMessage.setUserId(order.getApply_user());
            parserCheckMessage.setIsVerify(ParserExecuteType.makeIsVerify(parserExecuteTypes));
            // todo
            parserCheckMessage.setOrderMaskLevel(applyContent.getEnable_desensitize_type());
            parserCheckMessage.setOrigin(originType != null ? (long) originType.getValue() : null);
            parserCheckMessage.setAuditOrigin(AuditOriginType.DATA_CHANGE.getValue());
            webSQLParserInfo = WebSQLParserInfo.openCheckParser(parserCheckMessage);
        } else {
            webSQLParserInfo.getDto().setSql(sql);
        }
        return webSQLParserInfo.getProcessor().process((parserParamDto, sqlParseModel) ->
                sqlCheckService.prepareCheck(parserParamDto, sqlParseModel, webSQLParserInfo));

    }

    protected void insertBefore(PairList<OrderSqlParse, WebSQLParserResult> pairList, OrderApplyContent applyContent) {

        boolean isSkipPrecheck = null != applyContent.getIs_skip_precheck() && applyContent.getIs_skip_precheck() == 1;
        boolean isImport = null != applyContent.getIs_import() && applyContent.getIs_import() == 1;
        for (Pair<OrderSqlParse, WebSQLParserResult> pair : pairList) {
            OrderSqlParse orderSqlParse = pair.getFirst();
            WebSQLParserResult webSQLParserResult = pair.getSecond();

            if (orderSqlParse.getSql_type().isEmpty()) {
                orderSqlParse.setSql_type(webSQLParserResult.getOperation());
            }

            if (parserExecuteTypes.contains(ParserExecuteType.DATA_MASK_PARSER)) {
                orderSqlParse.setSql_operation(webSQLParserResult.getSqlOperation());
                orderSqlParse.setSusceptible_column(webSQLParserResult.getSusceptibleColumn(applyContent.getDb_type()));
            }

            if (parserExecuteTypes.contains(ParserExecuteType.SQL_BACKUP)) {
                BackupModel backupModel = new BackupModel(applyContent.getConnect_id(), applyContent.getDb_type(), applyContent.getInstance_name(), webSQLParserResult);
                orderSqlParse.setBackup_model(JSON.toJSONString(backupModel));
            }

            if (parserExecuteTypes.contains(ParserExecuteType.PRIVILEGES_MANAGEMENT)) {
                orderSqlParse.setPrivilege_model(JSON.toJSONString(webSQLParserResult.getPrivilegeModel()));
            }

            String message = webSQLParserResult.getMessage();
            if (isSkipPrecheck && !isImport) {
                // 导入工单不走这个逻辑
                verifySkipTotal++;
                orderSqlParse.setVerify_status(SqlVerifyStatus.skip.getValue());
                if (parserExecuteTypes.contains(ParserExecuteType.SQL_AFFECTED_ROWS) && webSQLParserResult.getAffectedRows() != null) {
                    orderSqlParse.setAffected_rows(webSQLParserResult.getAffectedRows().longValue());
                }
                continue;
            }
            if (StringUtils.isBlank(message)) {
                orderSqlParse.setVerify_status(SqlVerifyStatus.success.getValue());
                verifySuccessTotal++;
                if (parserExecuteTypes.contains(ParserExecuteType.SQL_AFFECTED_ROWS) && webSQLParserResult.getAffectedRows() != null) {
                    orderSqlParse.setAffected_rows(webSQLParserResult.getAffectedRows().longValue());
                }
            } else {
                orderSqlParse.setVerify_status(SqlVerifyStatus.fail.getValue());
                orderSqlParse.setVerify_reason(message);
                verifyFailTotal++;
            }
        }

    }

    protected void insertAfter(PairList<OrderSqlParse, WebSQLParserResult> pairList, Order order) {
        boolean isSkipPrecheck = null != order.getApply_content().getIs_skip_precheck() && order.getApply_content().getIs_skip_precheck() == 1;
        List<WorkOrderSqlCheck> sqlCheckList = new ArrayList<>();
        for (Pair<OrderSqlParse, WebSQLParserResult> pair : pairList) {
            OrderSqlParse orderSqlParse = pair.getFirst();
            WebSQLParserResult webSQLParserResult = pair.getSecond();
            boolean reminderFlag = false;
            boolean warningFlag = false;
            boolean errorFlag = false;

            if (parserExecuteTypes.contains(ParserExecuteType.SQL_AUDIT)) {
                if (isSkipPrecheck) {
                    WorkOrderSqlCheck workOrderSqlCheck = new WorkOrderSqlCheck();
                    workOrderSqlCheck.setType(AuditLevelType.SKIP.getValue());
                    workOrderSqlCheck.setContent(AuditLevelType.SKIP.getName());
                    workOrderSqlCheck.setOrderSqlParserId(orderSqlParse.getId());
                    workOrderSqlCheck.setOrderId(order.getId());
                    sqlCheckList.add(workOrderSqlCheck);
                    workOrderSqlCheckService.insertBatchMaxSize(sqlCheckList, null, null);
                    skip++;
                    continue;
                }

                if (CollectionUtils.isEmpty(webSQLParserResult.getCheckRuleList())) {
                    WorkOrderSqlCheck workOrderSqlCheck = new WorkOrderSqlCheck();
                    workOrderSqlCheck.setType(AuditLevelType.SUCCESS.getValue());
                    workOrderSqlCheck.setContent(AuditLevelType.SUCCESS.getName());
                    workOrderSqlCheck.setOrderSqlParserId(orderSqlParse.getId());
                    workOrderSqlCheck.setOrderId(order.getId());
                    sqlCheckList.add(workOrderSqlCheck);
                    workOrderSqlCheckService.insertBatchMaxSize(sqlCheckList, null, null);
                    pass++;
                } else {
                    for (CheckResultDto checkResultDto : webSQLParserResult.getCheckRuleList()) {
                        WorkOrderSqlCheck workOrderSqlCheck = new WorkOrderSqlCheck();
                        workOrderSqlCheck.setType(checkResultDto.getLevel());
                        workOrderSqlCheck.setContent(checkResultDto.getRuleName());
                        workOrderSqlCheck.setOrderSqlParserId(orderSqlParse.getId());
                        workOrderSqlCheck.setOrderId(order.getId());
                        sqlCheckList.add(workOrderSqlCheck);
                        workOrderSqlCheckService.insertBatchMaxSize(sqlCheckList, null, null);

                        if (AuditLevelType.REMINDER.getValue().equals(checkResultDto.getLevel())) {
                            if (!reminderFlag) {
                                reminder++;
                            }
                            reminderFlag = true;
                        } else if (AuditLevelType.WARNING.getValue().equals(checkResultDto.getLevel())) {
                            if (!warningFlag) {
                                warning++;
                            }
                            warningFlag = true;
                        } else if (AuditLevelType.ERROR.getValue().equals(checkResultDto.getLevel())) {
                            if (!errorFlag) {
                                error++;
                            }
                            errorFlag = true;
                        }
                    }
                }
            }

        }
        workOrderSqlCheckService.insertBatchAllEntity(sqlCheckList, null, null);
        pairList.getSecondList().clear();

    }

    public String statisticalVerifyContent() {
        if (verifySuccessTotal > 0 || verifyFailTotal > 0 || verifySkipTotal > 0) {
            // 语法校验
            List<String> verifyContentList = new ArrayList<>();
            if (verifySuccessTotal > 0) {
                verifyContentList.add(String.format("成功%s条", verifySuccessTotal));
            }
            if (verifyFailTotal > 0) {
                verifyContentList.add(String.format("失败%s条", verifyFailTotal));
            }
            if (verifySkipTotal > 0) {
                verifyContentList.add(String.format("跳过%s条", verifySkipTotal));
            }
            return String.join(",", verifyContentList);
        }
        return null;
    }

    public String statisticalAuditContent() {
        if (reminder > 0 || warning > 0 || error > 0 || pass > 0 || skip > 0) {
            // sql审核
            List<String> content = new ArrayList<>();
            if (reminder > 0) {
                content.add(AuditLevelType.REMINDER.getName() + reminder + "条");
            }
            if (warning > 0) {
                content.add(AuditLevelType.WARNING.getName() + warning + "条");
            }
            if (error > 0) {
                content.add(AuditLevelType.ERROR.getName() + error + "条");
            }
            if (pass > 0) {
                content.add(String.format("通过%s条", pass));
            }
            if (skip > 0) {
                content.add(String.format("跳过%s条", skip));
            }
            return String.join(",", content);
        }
        return null;
    }

    public boolean isAuditSuccess() {
        return error <= 0;
    }
}
