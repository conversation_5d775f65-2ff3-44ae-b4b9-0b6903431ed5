package com.dc.parser.sensitive.impl;

import com.dc.sqlparser.nodes.TParseTreeNode;
import com.dc.sqlparser.stmt.TSelectSqlStatement;

import java.util.List;

/**
 * 取结果的并集SQL
 *
 * @description: UNION | UNION All
 */
public class SelectSqlStatementExpression implements SQLExpression {

    @Override
    public void interpret(StatementContext ctx) {

        List<TParseTreeNode> nodes = ctx.getNodes();

        for (TParseTreeNode treeNode : nodes) {

            //用例1： UNION
            //用例2： UNION All
            if (null != treeNode && treeNode instanceof TSelectSqlStatement) {

                TSelectSqlStatement selectSqlStatement = (TSelectSqlStatement) treeNode;

                StatementContext context = ctx.createContext();
                context.addEps(new CustomSqlStatementExpression());
                context.addNode(selectSqlStatement.getLeftStmt());
                context.addNode(selectSqlStatement.getRightStmt());  //冗余
                context.search();

            }
        }
    }
}
