package com.dc.parser.sensitive.test;

import com.dc.parser.util.GetMaskNodeUtil;
import com.dc.springboot.core.model.sensitive.MaskNodeModel;
import com.dc.sqlparser.DCustomSqlStatement;
import com.dc.summer.parser.utils.SqlParserUtil;
import com.dc.summer.parser.utils.model.SqlParseResult;

import java.util.List;

public class UnionTest {

    public static void main(String[] args) {

        int dbType = 2;
//        String sql = "select name as e from tab union  select id from tab2";
//        String sql = "select * from t1 join (select * from t2 join t3)";
//        String sql = "select name as password from (select name from tab union select name from tab2) a";
//        String sql = "select a.name from tab a join tab2 b on a.id = b.pid";
//        String sql = "select name from tab a join tab2 b on a.id = b.pid";
//        String sql = "select d.name from tab1 d join (select a.name, b.id from tab1 a join  tab3 b on a.id = b.id ) c on  d.id = c.id";
//        String sql = "select name as password from (select * from tab union select * from tab2) a";
//        String sql = "select name as password from (select rowid,* from tab union select rowid,* from tab2) a";
//        String sql = "select name as password from (select name from tab union select name from tab2) a";
//        String sql = "select name as password from (select name from tab union select id as name from tab2) a";
//        String sql = "select name as password from (select name,id from tab union select id as name ,name as id from tab2) a";
//
        String sql = "SELECT t.rowid, t.JOB_TITLE  as title   FROM \"HR\".\"JOBS\" t WHERE ROWNUM <= 1\n" +
                "union all \n" +
                "SELECT t.rowid, t.REGION_NAME   FROM \"HR\".\"REGIONS\" t WHERE ROWNUM <= 1\n";

//        String sql = "select t.rowid as  id,t.name,t.phone from WHD_POC_TEST1 t ";
//        String sql = "select (select a.password from tab a join tab2 b on a.id= b.pid) as t from tab3";   //这种案例务必需写表别名
//        String sql = "select name as t from (select name from tab union select name from tab2) a";


        List<SqlParseResult> parser = SqlParserUtil.parser(sql, dbType);
        DCustomSqlStatement customSqlStatement = parser.get(0).gettCustomSqlStatement();

        // 解析SQL结果列脱敏节点, 用于半脱提高匹配度
        GetMaskNodeUtil getMaskNode = new GetMaskNodeUtil(dbType,
                "scott",
                "dbo",
                customSqlStatement);
        List<MaskNodeModel> nodes = getMaskNode.parser();
        System.out.println(nodes);
        System.out.println(1);


    }
}
