package com.dc.parser.type;

public enum UpdateSetValueType {

    unknown(0, "未知类型"),
    constant(1, "常量"),
    object_name(2, "列名"),
    sub_query(3, "子查询"),
    function(4, "函数");

    private Integer value;
    private String name;

    UpdateSetValueType(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
