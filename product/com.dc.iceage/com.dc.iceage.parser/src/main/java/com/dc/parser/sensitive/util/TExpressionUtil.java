package com.dc.parser.sensitive.util;

import com.dc.parser.sensitive.constants.EPSConstants;
import com.dc.parser.sensitive.model.SchemaModel;
import com.dc.sqlparser.DSourceToken;
import com.dc.sqlparser.nodes.*;
import org.apache.commons.lang3.StringUtils;

import java.util.Stack;

public class TExpressionUtil {

    public static String getColumnNameOnly(TExpression tExpr) {
        TObjectName objectOperand = null;
        if (null != tExpr) {
            objectOperand = tExpr.getObjectOperand();
        }
        if (null != objectOperand) {
            //a.`password` => password
            return SensitiveColumnUtil.replace(objectOperand.getColumnNameOnly());
        }
        return null;
    }

    public static String getDatabaseName(TObjectName objectOperand) {
        DSourceToken databaseToken = null;
        if (null != objectOperand) {
            databaseToken = objectOperand.getDatabaseToken();
        }
        if (null != databaseToken) {
            return SensitiveColumnUtil.replace(databaseToken.toString());
        }
        return null;
    }


    public static String getSchemaName(TObjectName objectOperand, String defaultDboSchema) {

        DSourceToken schemaToken = null;
        if (null != objectOperand) {
            schemaToken = objectOperand.getSchemaToken();
        }

        if (null != schemaToken) {
            return SensitiveColumnUtil.replace(schemaToken.toString());
        }

        return defaultDboSchema;
    }

    public static String getTableName(TObjectName objectOperand) {

        DSourceToken tableToken = null;
        if (null != objectOperand) {
            tableToken = objectOperand.getObjectToken();
        }

        if (null != tableToken) {
            return SensitiveColumnUtil.replace(tableToken.toString());
        }

        return null;
    }

    public static TObjectName getTableNameFromObjectOperand(TObjectName objectOperand) {
        TTable sourceTable = null;
        if (null != objectOperand) {
            sourceTable = objectOperand.getSourceTable();
        }
        TObjectName tableName = null;
        if (null != sourceTable) {
            tableName = sourceTable.getTableName();
        }
        return tableName;
    }

    /**
     * @param tableObject      objectOperand.getSourceTable().getTableName()
     * @param defaultDboSchema
     * @return
     */
    public static SchemaModel getSchema(TObjectName tableObject, Integer dbType, String defaultDboSchema) {

        String database = getDatabaseName(tableObject);
        String schemaName = getSchemaName(tableObject, defaultDboSchema);
        String tableName = getTableName(tableObject);
        //
        SchemaModel schema = new SchemaModel();
        schema.setExtDatabase(database);
        schema.setExtDboSchema(schemaName);
        schema.setExtTable(schema.generate3fSchemaTableName(dbType, schemaName, EPSConstants.TOKEN_IN_CHAIN, tableName));
        //TODO 占时用不到内连符号
        //  prevTokenInChain = schemaToken.getPrevTokenInChain()
        //  nextTokenInChain = schemaToken.getNextTokenInChain();

        schema.setPrevTokenInChain(EPSConstants.TOKEN_IN_CHAIN);
        schema.setNextTokenInChain(EPSConstants.TOKEN_IN_CHAIN);
        //
        return schema;
    }


    // 由于 toScript 备份特殊语句还原缺陷， 改为toString , 但是toString 不会对新创建的对象进行重新渲染故作废
    @Deprecated
    public static TAliasClause createAlias(String aliasName) {

        TAliasClause aliasClause = new TAliasClause();

        DSourceToken sourceToken = new DSourceToken();
        sourceToken.setString(aliasName);
        TObjectName objectName = new TObjectName();
        objectName.setObjectToken(sourceToken);
        aliasClause.setAliasName(objectName);
        aliasClause.setAsToken(new DSourceToken("as"));

        return aliasClause;
    }

    public static String createAliasStr(String aliasName) {
        return String.format(" %s %s ", EPSConstants.KEYWORD_AS, aliasName);
    }


    public static void convert(TObjectName objectOperand, String style) {

        //fix1: 移除列的表别名 :  a.name
        objectOperand.setObjectToken(null);
        //fix2：移除列的schema对象 [test1].[db0].[table01].name
        objectOperand.setSchemaToken(null);
        //fix3: 移除列的database 对象
        objectOperand.setDatabaseToken(null);

        DSourceToken partToken = objectOperand.getPartToken();
        partToken.setString(style);
        objectOperand.setString(partToken.toString());

    }

    public static String invokeStr(String symbol) {

        if (StringUtils.isEmpty(symbol)) {
            symbol = "******";
        }
        return String.format("'%s'", symbol);
    }


    public static TAliasClause getAliasClauseFromEndToken(DSourceToken endToken) {
        Stack<TParseTreeNode> nodesStartFromThisToken = null;
        if (null != endToken) {
            nodesStartFromThisToken = endToken.getNodesStartFromThisToken();
        }

        if (nodesStartFromThisToken != null && nodesStartFromThisToken.size() > 0) {
            for (int i = 0; i < nodesStartFromThisToken.size(); i++) {
                TParseTreeNode element = nodesStartFromThisToken.get(i);
                if (element instanceof TResultColumn) {
                    TResultColumn resultColumn = (TResultColumn) element;
                    TAliasClause aliasClause = resultColumn.getAliasClause();
                    return aliasClause;
                }
            }
        }
        return null;
    }

}
