package com.dc.parser.model.chain.impl;

import com.dc.parser.model.chain.SqlCheckChain;
import com.dc.parser.service.MetaDataStoreService;
import com.dc.repository.mysql.model.Schema;
import com.dc.springboot.core.component.Resource;
import com.dc.springboot.core.model.parser.ParserParamDto;
import com.dc.springboot.core.model.result.WebSQLParserResult;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import com.dc.summer.parser.utils.SqlPreparedUtil;
import com.dc.summer.parser.utils.model.SqlParseModel;

import java.util.List;
import java.util.Optional;

public class UseDatabaseChain extends SqlCheckChain {

    private final MetaDataStoreService metaDataStoreService = Resource.getBeanRequireNonNull(MetaDataStoreService.class);

    public UseDatabaseChain(ParserParamDto parserParamDto, SqlParseModel sqlParseModel) {
        super(parserParamDto, sqlParseModel);
    }

    @Override
    public boolean proceed(WebSQLParserResult webSQLParserResult) {
        // 切换数据库
        String switchDatabase = SqlPreparedUtil.getSwitchDatabase(sqlParseModel, parserParamDto.getDbType());
        if (!switchDatabase.isEmpty()) {
            Optional<Schema> schema = this.metaDataStoreService.getRealSchema(parserParamDto.getConnectId(), switchDatabase, parserParamDto.getCatalogName());
            if (schema.isPresent()) {
                switchDatabase = schema.get().getSchema_name();
            }
            webSQLParserResult.setUseDatabase(switchDatabase);
            if (!sqlParseModel.getSqlAuthModelList().isEmpty()) {
                SqlAuthModel sqlAuthModel = sqlParseModel.getSqlAuthModelList().get(0);
                webSQLParserResult.setCatalogName(sqlAuthModel.getCatalogName());
            }
            sqlParseModel.setSwitchDatabase(true);
        }
        return true;
    }

    private static Schema getTrueSchema(List<Schema> schemaBySchemaName, String schemaName) {
        if (schemaBySchemaName.size() == 1) {
            return schemaBySchemaName.get(0);
        } else {
            for (Schema schemaTemp : schemaBySchemaName) {
                if (schemaName.equals(schemaTemp.getSchema_name())) {
                    return schemaTemp;
                }
            }
            return schemaBySchemaName.get(0);
        }
    }
}
