package com.dc.proxy.model.data.message;

import com.dc.proxy.model.type.TableType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@ApiModel("表消息")
public class TableMessage extends SchemaMessage {

    @ApiModelProperty(value = "表名", example = "demo_table")
    private String tableNamePattern;

    @ApiModelProperty(value = "表类型", example = "TABLE,VIEW")
    private List<TableType> types;

    public String[] getTypes() {
        return types.stream().map(TableType::getType).toArray(String[]::new);
    }
}
