package com.dc.proxy.service.impl;

import com.dc.proxy.model.data.message.HiveMetaDataMessage;
import com.dc.proxy.model.result.CatalogSchemaResult;
import com.dc.proxy.model.result.SchemaResult;
import com.dc.springboot.core.model.database.ConnectionMessage;
import com.dc.springboot.core.model.database.TableFieldsMessage;
import com.dc.springboot.core.model.exception.ServiceException;
import com.dc.proxy.model.receiver.HiveDataReceiver;
import com.dc.proxy.model.result.HiveResult;
import com.dc.springboot.core.model.data.RowsData;
import com.dc.proxy.model.type.HiveType;
import com.dc.proxy.service.MetaDataService;
import com.dc.springboot.core.utils.SqlFieldDataUtils;
import com.dc.summer.DBException;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.exec.DBCExecutionPurpose;
import com.dc.summer.model.exec.DBExecUtils;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.runtime.LoggingProgressMonitor;
import com.dc.iceage.model.jdbc.ExecutionContainer;
import com.dc.summer.model.sql.SqlFieldData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class MetaDataServiceImpl implements MetaDataService {

    private final LoggingProgressMonitor monitor = new LoggingProgressMonitor();

    @Override
    public HiveResult hiveMetadata(HiveMetaDataMessage message) {
        try (ExecutionContainer executionContainer = new ExecutionContainer(message.getConnectionConfig().getConnectionConfiguration())) {
            DBCExecutionContext executionContext = executionContainer.getExecutionContext();
            HiveResult hiveResult = new HiveResult();
            RowsData rowsData = new RowsData();
            try {
                int rowIndex = 0;
                Map<String,String> keyMap = new HashMap<>();
                for (String type : message.getTypes()) {

                    boolean paging;
                    if (message.getLimit() != null && message.getOffset() != null) {
                        if (rowIndex < message.getOffset() + message.getLimit()) {
                            paging = true;
                        } else {
                            paging = false;
                        }
                    } else {
                        paging = true;
                    }

                    if ((type.equals(HiveType.TBALE.getCode()) || type.equals(HiveType.VIEW.getCode()))
                            && paging) {
                        keyMap.put("table_schem", "owner");
                        keyMap.put("table_name", "object_name");
                        keyMap.put("table_type", "object_type");
                        keyMap.put("remarks", "comment");
                        HiveDataReceiver dataReceiver = new HiveDataReceiver(executionContext, keyMap, message.getInstances());
                        rowIndex = DBExecUtils.executeMetaData(monitor, executionContext, "MetaData Tables",
                                metaData -> metaData.getTables(message.getCatalog(), message.getSchemaPattern(), message.getPattern(), HiveType.getNameByCode(type)),
                                dataReceiver, message.getLimit(), message.getOffset(), rowIndex);
                        rowsData.addAll(dataReceiver.getRowsData());
                    }

                    if (type.equals(HiveType.COLUMN.getCode())
                            && paging && message.getTableList() != null) {
                        for (String tableName : message.getTableList()) {
                            HiveDataReceiver dataReceiver = new HiveDataReceiver(executionContext);
                            rowIndex = DBExecUtils.executeMetaData(monitor, executionContext, "MetaData Columns",
                                    metaData -> metaData.getColumns(message.getCatalog(), message.getSchemaPattern(), tableName, message.getPattern()),
                                    dataReceiver, message.getLimit(), message.getOffset(), rowIndex);
                            rowsData.addAll(dataReceiver.getRowsData());
                        }
                    }

                    if (type.equals(HiveType.FUNCTION.getCode())
                            && paging) {
                        keyMap.put("table_schem", "owner");
                        keyMap.put("table_name", "object_name");
                        keyMap.put("table_type", "object_type");
                        keyMap.put("remarks", "comment");
                        HiveDataReceiver dataReceiver = new HiveDataReceiver(executionContext, keyMap);
                        rowIndex = DBExecUtils.executeMetaData(monitor, executionContext, "MetaData Functions",
                                metaData -> metaData.getFunctions(message.getCatalog(), message.getSchemaPattern(), message.getPattern()),
                                dataReceiver, message.getLimit(), message.getOffset(), rowIndex);
                        rowsData.addAll(dataReceiver.getRowsData());
                    }

                }
                hiveResult.setTotal(rowIndex);
            }catch (Exception e) {
                e.printStackTrace();
            }
            hiveResult.setRecords(rowsData);
            return hiveResult;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage(), e);
        }
    }

    @Override
    public List<CatalogSchemaResult> getSchemas(ConnectionMessage message) {
        try (ExecutionContainer executionContainer = new ExecutionContainer(message.getConnectionConfig().getConnectionConfiguration())) {
            DBCExecutionContext executionContext = executionContainer.getExecutionContext();
            List<CatalogSchemaResult> catalogSchemaResultList = new ArrayList<>();
            try (HiveDataReceiver dataReceiver = new HiveDataReceiver(executionContext, null, null)) {
                DBExecUtils.executeMetaData(monitor, executionContext, "MetaData catalogs",
                        JDBCDatabaseMetaData::getCatalogs,
                        dataReceiver, null, null, 0);
                RowsData catalogs = dataReceiver.getRowsData();
                for (Map<String, Object> catalog : catalogs) {
                    CatalogSchemaResult catalogSchemaResult = new CatalogSchemaResult();
                    String catalogName = (String) catalog.get("table_cat");
                    catalogSchemaResult.setUsername(catalogName);

                    int allTableCount = 0;
                    try (HiveDataReceiver schemaReceiver = new HiveDataReceiver(executionContext, null, null)) {
                        DBExecUtils.executeMetaData(monitor, executionContext, "MetaData schemas",
                                metaData -> metaData.getSchemas(catalogName, null),
                                schemaReceiver, null, null, 0);


                        RowsData schemas = schemaReceiver.getRowsData();
                        List<SchemaResult> schemaResultList = new ArrayList<>();
                        for (Map<String, Object> schema : schemas) {
                            SchemaResult schemaResult = new SchemaResult();
                            String schemaName = (String) schema.get("table_schem");
                            schemaResult.setUsername(schemaName);
                            schemaResult.setCount(0);
                            schemaResultList.add(schemaResult);
                        }
                        catalogSchemaResult.setCount(allTableCount);
                        catalogSchemaResult.setSchemas(schemaResultList);
                        catalogSchemaResultList.add(catalogSchemaResult);
                    } catch (Exception e) {
                        log.error("获取schema失败！", e);
                    }
                }
            }
            return catalogSchemaResultList;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage(), e);
        }
    }


    @Override
    public String getVersion(ConnectionMessage message) {
        try (ExecutionContainer executionContainer = new ExecutionContainer(message.getConnectionConfig().getConnectionConfiguration())) {
            DBCExecutionContext executionContext = executionContainer.getExecutionContext();
            try (JDBCSession session = (JDBCSession) executionContext.openSession(monitor, DBCExecutionPurpose.UTIL, "MetaData version")) {
                JDBCDatabaseMetaData metaData = session.getMetaData();
                return metaData.getDatabaseProductVersion();
            } catch (Throwable e) {
                throw new DBException(e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage(), e);
        }
    }

    @Override
    public List<SqlFieldData> getTableFields(TableFieldsMessage message) {
        try (ExecutionContainer executionContainer = new ExecutionContainer(message.getConnectionConfig().getConnectionConfiguration())) {
            DBCExecutionContext executionContext = executionContainer.getExecutionContext();
            return SqlFieldDataUtils.getExecuteSqlFieldData(executionContext, monitor,
                    message.getPrimaryKeyColumns(),
                    message.getSchemaName(),
                    message.getTableName());
        } catch (Exception e) {
            throw new ServiceException(e.getMessage(), e);
        }
    }

}
