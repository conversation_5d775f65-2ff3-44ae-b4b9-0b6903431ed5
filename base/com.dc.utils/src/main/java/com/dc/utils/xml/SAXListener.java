

package com.dc.utils.xml;

import org.xml.sax.Attributes;

/**
	SAX document listener
*/
public interface SAXListener {

	void saxStartElement(
        SAXReader reader,
        String namespaceURI,
        String localName,
        org.xml.sax.Attributes atts)
		throws XMLException;

	void saxText(
        SAXReader reader,
        String data)
		throws XMLException;

	void saxEndElement(
        SAXReader reader,
        String namespaceURI,
        String localName)
		throws XMLException;


    /**
     * Empty listener supposed to skip element subtrees
     */
    class BaseListener implements SAXListener {

        @Override
        public void saxStartElement(SAXReader reader, String namespaceURI, String localName, Attributes atts) throws XMLException {
        }

        @Override
        public void saxText(SAXReader reader, String data) throws XMLException {
        }

        @Override
        public void saxEndElement(SAXReader reader, String namespaceURI, String localName) throws XMLException {
        }
    }
    SAXListener EMPTY_LISTENER = new BaseListener();

}
