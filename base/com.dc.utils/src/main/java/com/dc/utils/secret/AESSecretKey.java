package com.dc.utils.secret;

import com.dc.utils.verification.VerificationException;

import javax.crypto.Cipher;
import java.security.Key;

public class AESSecret<PERSON>ey extends SymmetricSecretKey {

    public AESSecretKey(String slat) {
        super(AlgorithmType.AES, FeedbackType.ECB, PaddingType.PKCS5Padding, slat);
    }

    @Override
    protected Cipher getCipher(int opmode, Key key) throws Exception {

        Cipher cipher = Cipher.getInstance(transformation);

        if (feedbackType == FeedbackType.ECB) {
            cipher.init(opmode, key);
        } else {
            throw new VerificationException("AES Not Supported feedbackType: " + feedbackType);
        }

        return cipher;
    }

}
