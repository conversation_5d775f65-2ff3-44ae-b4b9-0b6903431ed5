package com.dc.type;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum SecurityRuleType {

    NONE(null, null, false),

    // parser 需要缓存
    SELECT_LIMIT("select_limit", "单次SELECT SQL最大的行数", true),
    UPDATE_LIMIT("update_limit", "单次UPDATE SQL最大的行数", true),
    DELETE_LIMIT("delete_limit", "单次DELETE SQL最大的行数", true),
    INSERT_LIMIT("insert_limit", "单次INSERT SQL最大的行数", true),
    EXPORT_LIMIT("export_limit", "单次本地导出最大行数", true),
    BACKUP_DATA_ROW_LIMIT("backup_data_row_limit", "操作备份行数限制", true),
    COLUMN_ANNOTATION_SWITCH("column_annotation_switch", "列注释开关", true),
    SENSITIVE_DATA_PROTECTION_DESENSITIZATION("sensitive_data_protection_desensitization", "敏感数据保护-脱敏", true),
    DESENSITIZATION_EXPORT("desensitization_export", "脱敏导出", false),
    EXPORT_ENCRYPTED("export_encrypted", "导出文件加密", false),
    SQL_WINDOWS_SQL_AUDIT("sql_windows_sql_audit", "SQL窗口应用SQL审核规则", true),
    DATA_CHANGE_SQL_AUDIT("data_change_sql_audit", "数据变更应用SQL审核规则", true),
    DATA_CHANGE_EXCEEDING_AUTH("data_change_exceeding_auth", "数据变更应用越权开关及等级(0:关,1:提示,2:警告,3:错误)", true),
    FAST_CHANGE_BACKUP_CONFIG("fast_change_backup_config", "快捷变更应用操作备份配置", true),
    SQL_EXECUTE_BACKUP_CONFIG("sql_execute_backup_config", "任务管理-SQL执行应用操作备份配置", true),
    DATA_CHANGE_BACKUP_CONFIG("data_change_backup_config", "脚本变更应用操作备份配置", true),
    DATA_IMPORT_BACKUP_CONFIG("data_import_backup_config", "数据导入应用操作备份配置", true),

    // 工单不需要缓存
    SCRIPT_EXEC_THREADS("script_exec_threads", "脚本变更并行线程数", false),

    //私有schema
    PRIVATE_SCHEMA("private_schema", "私有schema名称", false),

    PREFER_USING_DBA_VIEWS("prefer_using_dba_views", "优先使用DBA_*视图", true),

    ;

    private final String name;
    private final String describe;
    private final boolean cache;

    private static final Map<String, SecurityRuleType> NAME_SECURITY_RULE_TYPE_MAP = Arrays.stream(SecurityRuleType.values()).collect(Collectors.toMap(SecurityRuleType::getName, Function.identity()));

    private static final Set<String> CACHE_DETAILS_TPL_KEYS = Arrays.stream(SecurityRuleType.values()).filter(SecurityRuleType::isCache).map(SecurityRuleType::getName).collect(Collectors.toSet());

    public static SecurityRuleType of(String name) {
        SecurityRuleType type = NAME_SECURITY_RULE_TYPE_MAP.get(name);
        if (type != null) {
            return type;
        }
        return NONE;
    }

    public static Set<String> getCacheDetailsTplKeys() {
        return CACHE_DETAILS_TPL_KEYS;
    }

}
