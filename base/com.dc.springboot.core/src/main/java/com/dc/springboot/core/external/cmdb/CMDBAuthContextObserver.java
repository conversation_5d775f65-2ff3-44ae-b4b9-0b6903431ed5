package com.dc.springboot.core.external.cmdb;

import com.dc.springboot.core.client.BaseRestClient;
import com.dc.springboot.core.external.ExternalConstants;
import com.dc.summer.exec.model.observer.ContextObserver;
import com.dc.summer.exec.model.observer.ContextSubject;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.utils.GeneralUtils;
import com.dc.type.AuthSourceType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

@Slf4j
@Component
public class CMDBAuthContextObserver extends BaseRestClient implements ContextObserver {

    @Resource
    private CMDBConfig cmdbConfig;

    private static final String URL = "/pam/account";

    @PostConstruct
    public void init() {
        ContextSubject.register(this);
    }

    @Override
    public void fillingConfigurationPassword(DBPConnectionConfiguration connectionConfiguration) {

        if (connectionConfiguration.getAuthSourceType() == AuthSourceType.CMDB) {

            final String key = SM4Utils.generateRandomString(16);

            AuthenticationCMDBDatabaseDto dto = new AuthenticationCMDBDatabaseDto();
            dto.setAppId(cmdbConfig.getAppId());
            dto.setObjectName(connectionConfiguration.getProviderProperty(ExternalConstants.CMDB_OBJECT_NAME));
            dto.setResourceName(connectionConfiguration.getProviderProperty(ExternalConstants.CMDB_RESOURCE_NAME));
//            dto.setQuery(String.format("deviceType=%s;address=%s", "Linux", connectionConfiguration.getHostName()));
            dto.setRequestReason("Used For " + GeneralUtils.getProductTitle());
            dto.setAccessKeyId(cmdbConfig.getAccessKeyId());
            dto.setAccessKeySecret(cmdbConfig.getAccessKeySecret());
            dto.setAlgorithm("SM4");
            dto.setEncryptionKey(key);

            connectionConfiguration.setUserPassword(postCMDB(dto));

        }

    }

    private String postCMDB(AuthenticationCMDBDatabaseDto dto) {

        try {
            ResponseEntity<CMDBResult> cmdbResultResponseEntity = super.exchange(cmdbConfig.getPath() + URL, HttpMethod.POST, dto, CMDBResult.class);
            HttpStatus statusCode = cmdbResultResponseEntity.getStatusCode();
            if (statusCode == HttpStatus.OK) {
                CMDBResult cmdbResult = cmdbResultResponseEntity.getBody();
                if (cmdbResult != null) {
                    if (Boolean.TRUE.equals(cmdbResult.getExtras().getEncodeResult())) {
                        return SM4Utils.decrypt(dto.getEncryptionKey(), cmdbResult.getObjectContent().replace("\u003d", "="));
                    } else {
                        throw new CMDBException("ErrorCode is " + cmdbResult.getExtras().getErrorCode());
                    }
                } else {
                    throw new CMDBException("Body is null");
                }
            } else {
                throw new CMDBException("StatusCode is " + statusCode);
            }
        } catch (CMDBException ce) {
            throw ce;
        } catch (Exception e) {
            throw new CMDBException(e);
        }
    }


    @Override
    public HttpHeaders getHeaders(String requestBody) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        return headers;
    }

}