package com.dc.springboot.core.client;

import com.dc.springboot.core.model.data.BackendResult;
import com.dc.springboot.core.model.data.Client;
import com.dc.springboot.core.model.data.ExecutorData;
import com.dc.springboot.core.model.database.SysSchemaMessage;
import com.dc.springboot.core.model.message.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BackendClient extends BaseRestClient {

    public Map<Integer, List<String>> getSysSchema(Client client, SysSchemaMessage message) {
        return postPhp(client.toBackendClient().getUrl("/config/schemas/get-sys-schema"), message, new ParameterizedTypeReference<BackendResult<Map<Integer, List<String>>>>() {
        });
    }

    public Object removeSchema(Client client, Map<String,List<String>> message) {
        return postPhp(client.toBackendClient().getUrl("/config/schemas/del-schema-auth"), message);
    }

    public Object syncSchemas(Client client, Map<String,String> map) {
        return postPhp(client.toBackendClient().getUrl("/config/schemas/sync"), map);
    }

    public void saveAlertNotify(Client client, AlertNotifyMessage alertNotifyMessage) {
        postPhp(client.toBackendClient().getUrl("/alert/alert-notify/save"), alertNotifyMessage);
    }

    public Object enterOrder(Client client, EnterOrderMessage enterOrderMessage) {
        return postPhp(client.toBackendClientOnlyApi().getUrl("/pe/order/orders/enter-order"), enterOrderMessage);
    }


    public Object sendEmail(Client client, EmailMessage message) {
        return postPhp(client.toBackendClient().getUrl("/task/jobs/send-email"), message, new ParameterizedTypeReference<BackendResult<Object>>() {
        });
    }

    public void syncDcMetadata(Client client, String instanceUniqueKey) {
        SyncMetadataMsg msg = new SyncMetadataMsg(instanceUniqueKey);
        postPhp(client.toBackendClient().getUrl("/config/metadatas/sync"), msg);
    }

    public void notifyPrivilegeExpire(Client client) {
        getPhp(client.toBackendClient().getUrl("/security/resource-users/permissions-expired"), null,new ParameterizedTypeReference<BackendResult<Object>>() {
        });
    }

    public Map<Integer, ExecutorData> getExecutorDataMap(Client client) {
        return getPhp(client.toBackendClient().getUrl("/system/params/executor"), null, new ParameterizedTypeReference<BackendResult<List<ExecutorData>>>() {
        }).stream().collect(Collectors.toMap(ExecutorData::getValue, Function.identity()));
    }

    public void notifySoonExpiredAuth(Client client, AuthSoonExpiredMessage authSoonExpiredMessage) {
        postPhp(client.toBackendClientOnlyApi().getUrl("/pe/permission/expire-alerts/expire-soon-alert"), authSoonExpiredMessage);
    }

    public Map<String, Object> inspectPrivilege(Client client, PrivilegeInspectionMessage privilegeInspectionMessage) {
        return postPhp(client.toBackendClient().getUrl("/security/dam-inspection/create"), privilegeInspectionMessage, new ParameterizedTypeReference<BackendResult<Map<String, Object>>>() {
        });
    }
}
