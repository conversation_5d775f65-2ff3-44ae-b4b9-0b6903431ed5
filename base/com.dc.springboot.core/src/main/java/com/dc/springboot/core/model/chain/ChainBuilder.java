package com.dc.springboot.core.model.chain;


import lombok.extern.slf4j.Slf4j;

import java.util.function.Supplier;

@Slf4j
public class ChainBuilder<T> {

    private final ChainRunner<T> chainRunner;
    private Chain<T> head;
    private Chain<T> tail;

    private static final boolean VOID_PROCEED = true;

    /**
     * 忽略为T时，跳过所有检查器，便于开发调试。
     */
    private static final boolean IGNORE = false;

    private ChainBuilder(ChainRunner<T> chainRunner) {
        this.chainRunner = chainRunner;
    }

    public ChainBuilder<T> addChainIfConditionPass(boolean condition, Supplier<Chain<T>> pass) {
        if (condition) {
            return this.addChain(pass.get());
        } else {
            log.debug("Skip (true) -> Chain of Responsibility ({}), spend [0] ms.", pass.get().getClass().getSimpleName());
            return this;
        }
    }

    public ChainBuilder<T> addChainInHead(Chain<T> chain) {
        if (this.chainRunner == null) {
            return this;
        }
        if (this.head == null) {
            this.head = this.tail = chain;
        } else {
            chain.append(this.head);
            this.head = chain;
        }
        return this;
    }

    public ChainBuilder<T> addChain(Chain<T> chain) {
        if (this.chainRunner == null) {
            return this;
        }
        if (this.head == null) {
            this.head = this.tail = chain;
        } else {
            this.tail.append(chain);
            this.tail = chain;
        }
        return this;
    }

    public ChainBuilder<T> addChains(Chain<T>[] chains) {
        if (chains != null) {
            for (Chain<T> chain : chains) {
                this.addChain(chain);
            }
        }
        return this;
    }

    public static <T> ChainBuilder<T> build(ChainRunner<T> chainRunner) {
        return new ChainBuilder<>(chainRunner);
    }

    public static <T> ChainBuilder<T> none() {
        return new ChainBuilder<T>(null);
    }

    public boolean exec(T t) {
        if (IGNORE || this.head == null) {
            return VOID_PROCEED;
        }
        ChainObject<T> o = new ChainObject<>(t);
        return this.chainRunner.run(o.getInstance(), this.head);
    }

}
