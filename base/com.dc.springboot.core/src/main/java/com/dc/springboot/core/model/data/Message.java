package com.dc.springboot.core.model.data;

import javax.validation.constraints.*;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel("信息")
@NoArgsConstructor
@AllArgsConstructor
public class Message {

    @NotNull
    @ApiModelProperty(value = "TOKEN - 用于同窗口用同一个 SESSION 会话", required = true, example = "mock_token")
    private String token;

}
