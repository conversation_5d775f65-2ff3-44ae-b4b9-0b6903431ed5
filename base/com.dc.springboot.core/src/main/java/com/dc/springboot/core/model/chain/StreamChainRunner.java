package com.dc.springboot.core.model.chain;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class StreamChainRunner<T> implements ChainRunner<T> {

    @Override
    public boolean run(T t, Chain<T> chain) {
        if (t == null) {
            log.error("流执行异常！ - null");
            return true;
        }
        if (chain == null) {
            return true;
        }
        long currentTimeMillis = System.currentTimeMillis();
        try {
            chain.proceed(t);
        } catch (Exception e) {
            log.error("流执行异常！", e);
        } finally {
            log.debug("Stream (true) -> Chain of Responsibility ({}), spend [{}] ms.", chain.getClass().getSimpleName(), System.currentTimeMillis() - currentTimeMillis);
        }
        if (chain.next() != null) run(t, chain.next());
        return true;
    }
}
