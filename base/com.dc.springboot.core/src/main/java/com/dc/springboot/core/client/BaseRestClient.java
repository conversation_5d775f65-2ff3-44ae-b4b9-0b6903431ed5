package com.dc.springboot.core.client;

import com.dc.springboot.core.component.JSON;
import com.dc.springboot.core.component.RestsTemplate;
import com.dc.springboot.core.model.data.BackendResult;
import com.dc.springboot.core.model.data.Result;
import com.dc.springboot.core.model.exception.ClientException;
import com.dc.springboot.core.model.exception.ConnectionException;
import com.dc.springboot.core.model.exception.NotFindTokenException;
import com.dc.utils.LoggerUtils;
import com.dc.utils.aksk.AuthUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Slf4j
public class BaseRestClient {

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private RestsTemplate restsTemplate;

    protected Object post(String url, Object body) {
        return post(url, body, Object.class);
    }

    protected <T> T post(String url, Object body, Class<T> clazz) {
        try {
            ResponseEntity<T> responseEntity = exchange(url, HttpMethod.POST, body, clazz);
            if (responseEntity.getStatusCode() == HttpStatus.OK) {
                return responseEntity.getBody();
            } else {
                throw new ClientException(String.valueOf(responseEntity.getBody()));
            }
        } catch (RestClientException e) {
            throw new ClientException(e);
        }
    }

    protected <T> T post(String url, Object body, ParameterizedTypeReference<T> responseType) {
        try {
            ResponseEntity<T> responseEntity = exchange(url, HttpMethod.POST, body, responseType);
            if (responseEntity.getStatusCode() == HttpStatus.OK) {
                return responseEntity.getBody();
            } else {
                throw new ClientException(String.valueOf(responseEntity.getBody()));
            }
        } catch (RestClientException e) {
            throw new ClientException(e);
        }
    }

    protected <T> T postJava(String url, Object body, ParameterizedTypeReference<Result<T>> responseType) {
        try {
            ResponseEntity<Result<T>> responseEntity = exchange(url, HttpMethod.POST, body, responseType);
            if (responseEntity.getStatusCode() == HttpStatus.OK) {
                Result<T> result = responseEntity.getBody();
                if (result != null) {
                    if (result.isSuccess()) {
                        return result.getBody();
                    } else if (result.getCode() == Result.CODE.NOT_TOKEN.getValue()) {
                        throw new NotFindTokenException("");
                    } else if (result.getCode() == Result.CODE.CONNECTION_FAIL.getValue()) {
                        throw new ConnectionException(Collections.singletonList(result.getMsg()), result.getMsg());
                    } else {
                        throw new ClientException(result.getMsg());
                    }
                }
            }
            throw new ClientException(String.valueOf(responseEntity.getBody()));
        } catch (RestClientException e) {
            throw new ClientException(e);
        }
    }

    protected <T> ResponseEntity<Result<T>> postJavaWithResponseEntity(String url, Object body, ParameterizedTypeReference<Result<T>> responseType, Map<String, String> headers) {
        try {
            ResponseEntity<Result<T>> responseEntity = exchange(url, HttpMethod.POST, body, responseType, headers);
            if (responseEntity.getStatusCode() == HttpStatus.OK) {
                Result<T> result = responseEntity.getBody();
                if (result != null) {
                    if (result.isSuccess()) {
                        return responseEntity;
                    } else if (result.getCode() == Result.CODE.NOT_TOKEN.getValue()) {
                        throw new NotFindTokenException("");
                    } else if (result.getCode() == Result.CODE.CONNECTION_FAIL.getValue()) {
                        throw new ConnectionException(Collections.singletonList(result.getMsg()), result.getMsg());
                    } else {
                        throw new ClientException(result.getMsg());
                    }
                }
            }
            throw new ClientException(String.valueOf(responseEntity.getBody()));
        } catch (RestClientException e) {
            throw new ClientException(e);
        }
    }

    protected <T> List<T> postJavaList(String url, Object body, ParameterizedTypeReference<List<T>> responseType) {
        try {
            ResponseEntity<List<T>> responseEntity = exchange(url, HttpMethod.POST, body, responseType);
            if (responseEntity.getStatusCode() == HttpStatus.OK) {
                return responseEntity.getBody();
            } else {
                throw new ClientException(String.valueOf(responseEntity.getBody()));
            }
        } catch (RestClientException e) {
            throw new ClientException(e);
        }
    }

    protected Object postPhp(String url, Object body) {
        return postPhp(url, body, new ParameterizedTypeReference<BackendResult<Object>>() {
        });
    }

    protected <T> T postPhp(String url, Object body, ParameterizedTypeReference<BackendResult<T>> responseType) {
        return php(url, HttpMethod.POST, body, responseType);
    }

    protected <T> T getPhp(String url, Object body, ParameterizedTypeReference<BackendResult<T>> responseType) {
        return php(url, HttpMethod.GET, body, responseType);
    }

    private  <T> T php(String url, HttpMethod method, Object body, ParameterizedTypeReference<BackendResult<T>> responseType) {
        try {
            ResponseEntity<BackendResult<T>> responseEntity = exchange(url, method, body, responseType);
            if (responseEntity.getStatusCode() == HttpStatus.OK) {
                BackendResult<T> result = responseEntity.getBody();
                if (result != null) {
                    if (result.isSuccess()) {
                        return result.getData();
                    } else {
                        throw new ClientException(result.getMessage());
                    }
                }
            }
        } catch (RestClientException e) {
            throw new ClientException(e);
        }
        return null;
    }

    public  <T> ResponseEntity<T> exchange(String url, HttpMethod method, Object body, Class<T> responseType, Object... uriVariables) {
        long startTime = System.currentTimeMillis();
        String requestBody = body == null ? "" : JSON.toJSONString(body);
        HttpHeaders headers = getHeaders(requestBody);
        tranceRequest(url, method, headers, requestBody);
        ResponseEntity<T> entity = getRestTemplate(url).exchange(url, method, new HttpEntity<>(body, headers), responseType, uriVariables);
        traceResponse(entity, startTime);
        return entity;
    }

    public <T> ResponseEntity<T> exchange(String url, HttpMethod method, Object body, ParameterizedTypeReference<T> responseType, Object... uriVariables) {
        long startTime = System.currentTimeMillis();
        String requestBody = body == null ? "" : JSON.toJSONString(body);
        HttpHeaders headers = getHeaders(requestBody);
        tranceRequest(url, method, headers, requestBody);
        ResponseEntity<T> entity = getRestTemplate(url).exchange(url, method, new HttpEntity<>(body, headers), responseType, uriVariables);
        traceResponse(entity, startTime);
        return entity;
    }

    public <T> ResponseEntity<T> exchange(String url, HttpMethod method, Object body, ParameterizedTypeReference<T> responseType, Map<String, String> headerMap) {
        long startTime = System.currentTimeMillis();
        String requestBody = body == null ? "" : JSON.toJSONString(body);
        HttpHeaders headers = getHeaders(requestBody);
        headerMap.forEach(headers::set);
        tranceRequest(url, method, headers, requestBody);
        ResponseEntity<T> entity = getRestTemplate(url).exchange(url, method, new HttpEntity<>(body, headers), responseType);
        traceResponse(entity, startTime);
        return entity;
    }

    private RestTemplate getRestTemplate(String url) {
        if (url.startsWith("https:")) {
            return restsTemplate;
        }
        return restTemplate;
    }

    public HttpHeaders getHeaders(String requestBody) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        AuthUtils.packing(headers::set, requestBody);
        return headers;
    }

    public void tranceRequest(String url, HttpMethod method, HttpHeaders headers, String requestBody) {
        LoggerUtils slf4jUtils = LoggerUtils.send(LoggerUtils.REST);
        slf4jUtils.append("URL", url);
        slf4jUtils.append("Method", method);

        Map<String, String> headerMap = null;
        if (headers != null) {
            headerMap = headers.toSingleValueMap();
        }
        slf4jUtils.append("Headers", JSON.toJSONString(headerMap));
        if (requestBody != null) {
            slf4jUtils.append("Body", requestBody);
        }
        slf4jUtils.info();
    }

    public <T> void traceResponse(ResponseEntity<T> entity, long startTime) {
        LoggerUtils slf4jUtils = LoggerUtils.receive(LoggerUtils.REST);
        slf4jUtils.append("Status", entity.getStatusCode().value());
        HttpHeaders headers = entity.getHeaders();
        Map<String, String> headerMap = headers.toSingleValueMap();
        slf4jUtils.append("Headers", JSON.toJSONString(headerMap));
        String jsonString = JSON.toJSONString(entity.getBody());
        slf4jUtils.append("Body", jsonString);
        slf4jUtils.append("Size (byte)", jsonString.length());
        slf4jUtils.append("Duration (ms)", System.currentTimeMillis() - startTime);
        slf4jUtils.info();
    }

}
