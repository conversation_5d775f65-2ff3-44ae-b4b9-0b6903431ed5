package com.dc.springboot.core.model.workorder;

import com.dc.springboot.core.model.database.ConnectionMessage;
import com.dc.springboot.core.model.message.ExecuteEvent;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@ApiModel("工单信息")
public class WorkOrderMessage extends ConnectionMessage {

    @Valid
    @NotNull
    @ApiModelProperty(value = "工单模型", required = true)
    @JsonProperty("data")
    private WorkOrderModel workOrderModel;

    @Valid
    @ApiModelProperty(value = "告警消息")
    private ExecuteEvent executeEvent = new ExecuteEvent();

}
