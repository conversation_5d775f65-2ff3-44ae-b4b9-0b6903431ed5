package com.dc.springboot.core.model.parser.dto;

import lombok.Data;

@Data
public class ReviewConfigDto {

    private String userId;

    // 复核内容： 全部操作为 all
    private String operation;

    // 触发时机
    private String trigger;

    // 复核人员类型 1全部用户 2 组织， 3用户
    private Integer reviewUserType;

    // 复核人员，用户或组织uuids
    private String reviewUsers;

    // 操作人员类型  1全部用户 2 组织， 3用户
    private Integer operateUserType;

    // 操作人员 用户或组织uuids
    private String operateUsers;

    // 当 env 复核值 为 false 时，这个值 为 true
    private Boolean skipReview;

}
