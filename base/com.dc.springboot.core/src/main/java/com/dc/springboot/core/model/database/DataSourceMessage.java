package com.dc.springboot.core.model.database;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel("数据源消息")
public class DataSourceMessage {

    @NotBlank
    @ApiModelProperty(value = "连接ID", required = true, example = "mock_connection")
    private String connectionId;

    @ApiModelProperty(value = "用户名", example = "mock_username")
    private String userName;

    @ApiModelProperty(value = "服务名", example = "mock_servername")
    private String serverName;

    @ApiModelProperty(value = "刷新时间", example = "mock_servername")
    private long refreshTime = 0;

}
