package com.dc.springboot.core.model.parser;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Objects;

@Data
@ApiModel("解析缓存模型")
public class ParserCacheMessage {

    // 基础信息
    @ApiModelProperty(value = "token - 从redis获取env缓存的key", example = "mock_token")
    private String token;

    @ApiModelProperty(value = "SQL 列表 - 拆分后的多条sql(批处理接口一次传多条sql)", example = "select 1,select 1")
    private String sqlList;

    @ApiModelProperty(value = "验证编码 - parser需要做的操作(鉴权、访问管控等)", example = "SqlAuthVerify,SqlAccessControl,SqlAffectedRows,SqlExecuteLevel,DataMaskParser,DataRowIsEdit")
    private String isVerify;

    @ApiModelProperty(value = "下一个验证", example = "SqlAuthVerify,SqlAccessControl,SqlAffectedRows,SqlExecuteLevel,DataMaskParser,DataRowIsEdit")
    private String nextVerify;

    @ApiModelProperty(value = "切换数据库 - (use语句对应的database)(账号直连时，表示当前窗口schemaName)", example = "schema1")
    private String useDatabase;

    @ApiModelProperty(value = "3层数据库的databaseName - 如pg系列的最顶层name(pg系列切换schema时)(账号直连时，表示当前窗口catalogName)", example = "catalog1")
    private String catalogName;

    @ApiModelProperty(value = "回收站 SQL - 回收站获取脱敏配置后的真实执行sql", example = "select 1")
    private String recycleBinSql;

    // 影响行数
    @ApiModelProperty(value = "分页限制条数", example = "1")
    private Integer limit;

    @ApiModelProperty(value = "分页偏移量", example = "1")
    private Integer offset;

    // 访问管控
    @ApiModelProperty(value = "来源 - 编译无效对象、会话窗口", example = "1")
    private Long origin;

    @ApiModelProperty(value = "sql请求时间", example = "1")
    private Long requestTime;

    // 权限校验
    @ApiModelProperty(value = "oracle job - 验证CREATE、ALTER权限", example = "ORACLE_JOB")
    private String preOperation;

    @ApiModelProperty(value = "sqlserver的job", example = "WORK")
    private String objectType;

    @ApiModelProperty(value = "需要校验的对象及权限 - hbase由php传入，不通过sql解析", example = "1")
    private String authList;

    // 高危
    @ApiModelProperty(value = "是否有where条件 - hbase由php传过来", example = "1")
    private String whereClause;

    @ApiModelProperty(value = "用户的ip地址", example = "***********")
    private String userIp;

    /**
     * 重写比较，用来判断是否命中缓存
     */
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ParserCacheMessage that = (ParserCacheMessage) o;
        return Objects.equals(token, that.token) && Objects.equals(sqlList, that.sqlList) && Objects.equals(isVerify, that.isVerify) && Objects.equals(useDatabase, that.useDatabase) && Objects.equals(catalogName, that.catalogName) && Objects.equals(recycleBinSql, that.recycleBinSql) && Objects.equals(limit, that.limit) && Objects.equals(offset, that.offset) && Objects.equals(origin, that.origin) && Objects.equals(preOperation, that.preOperation) && Objects.equals(objectType, that.objectType) && Objects.equals(authList, that.authList) && Objects.equals(whereClause, that.whereClause) && Objects.equals(userIp, that.userIp);
    }

    @Override
    public int hashCode() {
        int result = Objects.hashCode(token);
        result = 31 * result + Objects.hashCode(sqlList);
        result = 31 * result + Objects.hashCode(isVerify);
        result = 31 * result + Objects.hashCode(useDatabase);
        result = 31 * result + Objects.hashCode(catalogName);
        result = 31 * result + Objects.hashCode(recycleBinSql);
        result = 31 * result + Objects.hashCode(limit);
        result = 31 * result + Objects.hashCode(offset);
        result = 31 * result + Objects.hashCode(origin);
        result = 31 * result + Objects.hashCode(preOperation);
        result = 31 * result + Objects.hashCode(objectType);
        result = 31 * result + Objects.hashCode(authList);
        result = 31 * result + Objects.hashCode(whereClause);
        result = 31 * result + Objects.hashCode(userIp);
        return result;
    }
}
