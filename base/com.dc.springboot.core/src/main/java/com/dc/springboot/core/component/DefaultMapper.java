package com.dc.springboot.core.component;

import com.dc.springboot.core.model.database.ConnectionConfig;
import com.dc.springboot.core.model.database.TestConnectionMessage;
import com.dc.springboot.core.model.execution.*;
import com.dc.springboot.core.model.parser.ParserCacheMessage;
import com.dc.springboot.core.model.parser.ParserParamDto;
import com.dc.summer.exec.config.SessionConfig;
import com.dc.summer.exec.model.data.ConnectionConfiguration;
import com.dc.summer.exec.model.data.TestConnectionConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface DefaultMapper {

    DefaultMapper INSTANCE = Mappers.getMapper(DefaultMapper.class);

    ParserParamDto copyPreCheckParamDTO(ParserParamDto dto);

    @Mapping(target = "databaseType", ignore = true)
    TestConnectionConfiguration toTestConnectionConfiguration(TestConnectionMessage message);

    @Mapping(target = "databaseName", expression = "java(mapValue(connectionConfig.getDatabaseName(), \"\"))")
    @Mapping(target = "authSourceType", ignore = true)
    @Mapping(target = "connectionType", ignore = true)
    @Mapping(target = "databaseType", ignore = true)
    @Mapping(target = "maximumContextSize", expression = "java(mapValue(connectionConfig.getMaximumContextSize(), sessionConfig.getMaximumContextSize()))")
    @Mapping(target = "keepAliveTime", expression = "java(mapValue(connectionConfig.getKeepAliveTime(), sessionConfig.getKeepAliveTime()))")
    @Mapping(target = "queryTimeout", expression = "java(mapValue(connectionConfig.getQueryTimeout(), sessionConfig.getQueryTimeout()))")
    @Mapping(target = "networkTimeout", expression = "java(mapValue(connectionConfig.getNetworkTimeout(), sessionConfig.getNetworkTimeout()))")
    ConnectionConfiguration toConnectionConfiguration(ConnectionConfig connectionConfig, SessionConfig sessionConfig);

    ValidExecuteModel toValidExecuteModel(BatchExecuteModel model);

    @Mapping(target = "origin", source = "message.sqlHistory.origin")
    @Mapping(target = "execSqlIndex", ignore = true)
    @Mapping(target = "dbType", source = "message.sqlHistory.dbType")
    @Mapping(target = "actionList", ignore = true)
    ValidExecuteModel toValidExecuteModel(SingleAsyncExecuteMessage message,
                                          SqlExecuteModel model);

    BatchExecuteModel toBatchExecuteModel(SingleExecuteModel model);

    default Integer mapValue(Integer a, Integer b) {
        return a != null ? a : b;
    }

    default Long mapValue(Long a, Long b) {
        return a != null ? a : b;
    }

    default String mapValue(String a, String b) {
        return a != null ? a : b;
    }

}
