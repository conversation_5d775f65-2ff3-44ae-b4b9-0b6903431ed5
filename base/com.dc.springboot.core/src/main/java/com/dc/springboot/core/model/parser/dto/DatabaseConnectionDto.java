package com.dc.springboot.core.model.parser.dto;

import com.dc.springboot.core.component.FilterIgnoreFiled;
import com.dc.springboot.core.component.JSON;
import com.dc.springboot.core.component.Resource;
import com.dc.springboot.core.model.database.ConnectionConfig;
import com.dc.summer.model.DBConstants;
import com.dc.type.AuthSourceType;
import com.dc.type.DatabaseType;
import com.dc.utils.CipherUtils;
import com.dc.utils.LoggerUtils;
import com.dc.utils.bean.CloneUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Data
public class DatabaseConnectionDto {

    private Long id;

    private String instance_name;

    private String entity;

    private Integer db_type;

    private Integer environment;

    private Integer is_active;

    private String password;

    private String username;

    private String connection;

    private String connection_desc;

    private String ip;

    private String port;

    private String service_name;

    private Integer connect_type;

    private String domain;

    private String tenant;

    private String cluster;

    private String version;

    private String db_name;

    private String gmt_create;

    private String gmt_modified;

    private String unique_key;

    private Integer is_delete;

    private String driver_id;

    private String kingbase_database_mode;

    private String authentication_parameters;

    private String driver_properties;

    private String db_role;

    private String category_id;

    private Integer connect_mode;

    private String jdbc_url;

    private Integer auth_source;

    private String extended_attributes;

    private int executor;

    private String db_id;

    private String config;

    private int security_rule_set_id;

    private Integer is_dblink;

    private String dblink_name;

    private Integer maximum_context_size;

    private Long keep_alive_time;

    private Integer query_timeout;

    private Integer network_timeout;

    private Integer pattern;

    public ConnectionConfig buildConnectionConfig(String schemaName, String catalogName) {
        // 实例连接配置信息
        ConnectionConfig connectionConfig = new ConnectionConfig();
        connectionConfig.setDriverId(this.getDriver_id());
        connectionConfig.setDatabaseType(this.getDb_type());
        connectionConfig.setHostName(this.getIp());
        connectionConfig.setHostPort(this.getPort());
        connectionConfig.setEnvironment(this.getEnvironment());
        connectionConfig.setInstanceName(this.getInstance_name());
        connectionConfig.setEntityName(this.getEntity());
        connectionConfig.setConnectionDesc(this.getConnection_desc());
        String databaseName = this.getService_name();
        if (Arrays.asList(DatabaseType.G_BASE_8S.getValue(), DatabaseType.INFORMIX.getValue()).contains(this.getDb_type())) {
            if (StringUtils.isNotBlank(schemaName)) {
                databaseName = schemaName;
            }
            if (DatabaseType.G_BASE_8S.getValue().equals(this.getDb_type()) && StringUtils.isNotBlank(this.getDb_name())) {
                databaseName += ":GBASEDBTSERVER=" + this.getDb_name() + ";"; // gbase8s需拼接
            } else if (DatabaseType.INFORMIX.getValue().equals(this.getDb_type()) && StringUtils.isNotBlank(this.getDb_name())) {
                databaseName += ":INFORMIXSERVER=" + this.getDb_name() + ";"; // informix需拼接
            }
        } else if (DatabaseType.getPGSqlIntegerValueList().contains(this.getDb_type()) && StringUtils.isNotBlank(catalogName)) {
            databaseName = catalogName; // pg系列serverName得是catalogName
        } else if (DatabaseType.ADBMYSQL2.getValue().equals(this.getDb_type())) {
            schemaName = this.getService_name();
        }
        connectionConfig.setDatabaseName(databaseName);
        connectionConfig.setSchemaName(schemaName);
        connectionConfig.setCatalogName(catalogName);
        String userName = this.getUsername();
        if (Arrays.asList(DatabaseType.OCEAN_BASE_ORACLE.getValue(), DatabaseType.OCEAN_BASE_MYSQL.getValue()).contains(this.getDb_type())
                && "private".equals(this.getDomain())) {

            if (StringUtils.isNotBlank(this.getCluster())) {
                userName = String.format("%s@%s#%s", userName, this.getTenant(), this.getCluster());
            } else {
                userName = String.format("%s@%s", userName, this.getTenant());
            }

        }
        connectionConfig.setUserName(userName);
        connectionConfig.setUserPassword(CipherUtils.changeEncryptionAlgorithm(this.getPassword()));
        connectionConfig.setConnectionId(this.getUnique_key());
        connectionConfig.setAuthSource(this.getAuth_source());
        connectionConfig.setResources(this.getResources());
        connectionConfig.setMaximumContextSize(this.getMaximum_context_size());
        connectionConfig.setKeepAliveTime(this.getKeep_alive_time());
        connectionConfig.setQueryTimeout(this.getQuery_timeout());
        connectionConfig.setNetworkTimeout(this.getNetwork_timeout());

        if (this.getConnect_mode() != null && 1 == this.getConnect_mode()) {
            connectionConfig.setUrl(this.getJdbc_url());
        }

        if (DatabaseType.KING_BASE.getValue().equals(this.getDb_type())) {
            if ("oracle".equals(kingbase_database_mode)) {
                connectionConfig.setUrlType(1);
            } else {
                connectionConfig.setUrlType(2);
            }
        } else {
            if (this.getConnect_type() == null) {
                connectionConfig.setUrlType(0);
            } else {
                connectionConfig.setUrlType(this.getConnect_type());
            }
        }

        getProviderProperties(connectionConfig);

        getProperties(connectionConfig);

        try {
            FilterIgnoreFiled filterIgnoreFiled = Resource.getBeanRequireNonNull(FilterIgnoreFiled.class);
            LoggerUtils slf4jUtils = LoggerUtils.receive(LoggerUtils.MYSQL);
            slf4jUtils.append("Dto", filterIgnoreFiled.handle(JSON.toJSONString(connectionConfig)));
            slf4jUtils.info();
        } catch (Exception e) {
            log.error("Dto To ConnectionConfig Error.", e);
        }

        return connectionConfig;
    }

    private void getProviderProperties(ConnectionConfig connectionConfig) {
        try {
            if (this.getAuthentication_parameters() != null) {

                AuthenticationParametersDto authenticationParametersDto = JSON.parseObject(this.getAuthentication_parameters(), AuthenticationParametersDto.class);

                if (authenticationParametersDto.getMode() == 1) {

                    if (Arrays.asList(DatabaseType.HIVE.getValue(), DatabaseType.IMPALA.getValue(), DatabaseType.INCEPTOR.getValue(), DatabaseType.SPARK.getValue()).contains(this.getDb_type())) {
                        connectionConfig.setAuthModelId("hive_kerberos");

                    } else if (DatabaseType.H_BASE.getValue().equals(this.getDb_type())) {
                        connectionConfig.setAuthModelId("hbase_kerberos");
                    }

                    Map<String, String> authProperties = new HashMap<>();
                    authProperties.put(DBConstants.AUTH_PROP_KEYTAB, authenticationParametersDto.getKeytab());
                    authProperties.put(DBConstants.AUTH_PROP_PRINCIPAL, authenticationParametersDto.getPrincipal());
                    authProperties.put(DBConstants.AUTH_PROP_HOSTS, authenticationParametersDto.getHosts());
                    authProperties.put(DBConstants.AUTH_PROP_KRB5_CONF, authenticationParametersDto.getKrb5Conf());

                    if (authenticationParametersDto.getIsSsl() == 1) {
                        authProperties.put(DBConstants.AUTH_PROP_SSL, authenticationParametersDto.getSslPath());
                    }

                    connectionConfig.setAuthProperties(authProperties);
                }
            }
        } catch (Exception e) {
            log.error("getHiveProviderProperties error : ", e);
        }
    }

    private void getProperties(ConnectionConfig connectionConfig) {

        try {
            if (StringUtils.isNotBlank(this.getDriver_properties())) {

                List<Map<String, Object>> driverProperties = JSON.parseObject(this.getDriver_properties(), new TypeReference<List<Map<String, Object>>>() {
                });
                if (driverProperties != null && driverProperties.size() > 0) {
                    Map<String, String> properties = new HashMap<>();
                    for (Map<String, Object> map : driverProperties) {
                        properties.put(map.get("key").toString(), map.get("value").toString());
                    }
                    connectionConfig.setProperties(properties);
                }
            }

            if (DatabaseType.ORACLE.getValue().equals(this.getDb_type())) {
                String authModelId = "oracle_native";
                connectionConfig.setAuthModelId(authModelId);

                Map<String, String> providerProperties = new HashMap<>();
                providerProperties.put("oracle.logon-as", StringUtils.isNotBlank(this.getDb_role()) ? this.getDb_role() : "Normal");
                connectionConfig.setAuthProperties(providerProperties);
            }

            AuthSourceType authSourceType = AuthSourceType.of(getAuth_source());
            if (authSourceType != AuthSourceType.SYSTEM) {

                List<ExtendedAttributesDto> extendedAttributesDtos = JSON.parseObject(getExtended_attributes(), new TypeReference<List<ExtendedAttributesDto>>() {
                });

                Map<String, String> providerProperties = CloneUtils.transListToMap(extendedAttributesDtos,
                        extendedAttributesDto -> "@" + extendedAttributesDto.getKey().replace("_", "-") + "@",
                        ExtendedAttributesDto::getValue);

                if (authSourceType == AuthSourceType.CYBERARK) {
                    String value = providerProperties.get("@cyberark-username@");
                    if (StringUtils.isNotBlank(value)) {
                        providerProperties.put("@cyberark-user-name@", value);
                    }
                }

                connectionConfig.setProviderProperties(providerProperties);
            }

        } catch (Exception e) {
            log.error("getProperties error : ", e);
        }

    }

    private List<String> getResources() {
        return StringUtils.isNotBlank(this.getConfig())
                ? JSON.parseObject(this.getConfig(), new TypeReference<List<Map<String, Object>>>() {
                }).stream()
                .map(entry -> entry.get("path").toString())
                .collect(Collectors.toList())
                : Collections.emptyList();
    }

    public Integer getAuth_source() {
        return auth_source == null ? 0 : auth_source;
    }

}
