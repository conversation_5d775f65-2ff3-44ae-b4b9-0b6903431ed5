package com.dc.springboot.core.utils;

import org.springframework.util.DigestUtils;

public class MD5Util {

    public static String encryptWithSalt(String string, String salt) {
        String result = null;
        try {
            String saltPassword = string + salt;
            result = DigestUtils.md5DigestAsHex(saltPassword.getBytes());
        } catch (Exception ignored) {
        }
        return result;
    }


    public static void main(String[] args) {
        System.out.println(MD5Util.encryptWithSalt("123456a", "7SkBaJVX"));
    }


}
