package com.dc.springboot.core.utils;

import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Base64;

@Slf4j
public class AESUtil {
    private final String AES = "AES";

    private final String CIPHER_ALGORITHM = "AES/ECB/PKCS5Padding";

    public String Encrypt(String sSrc, String key) throws Exception {
        byte[] raw = toMD5(key).getBytes(StandardCharsets.UTF_8);
        SecretKeySpec skeySpec = new SecretKeySpec(raw, this.AES);
        Cipher cipher = Cipher.getInstance(this.CIPHER_ALGORITHM);
        cipher.init(1, skeySpec);
        byte[] encrypted = cipher.doFinal(sSrc.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(encrypted);
    }

    public String Decrypt(String sSrc, String key) {
        try {
            byte[] raw = toMD5(key).getBytes(StandardCharsets.UTF_8);
            SecretKeySpec skeySpec = new SecretKeySpec(raw, this.AES);
            Cipher cipher = Cipher.getInstance(this.CIPHER_ALGORITHM);
            cipher.init(2, skeySpec);
            byte[] encrypted1 = Base64.getDecoder().decode(sSrc);
            byte[] original = cipher.doFinal(encrypted1);
            return new String(original, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("Decrypt error : ", e);
        }
        return null;
    }

    private String toMD5(String plainText) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update((plainText + "icop").getBytes());
            byte[] b = md.digest();
            StringBuilder buf = new StringBuilder("");
            for (int j : b) {
                int i = j;
                if (i < 0) {
                    i += 256;
                }
                buf.append(Integer.toHexString(i));
            }
            return buf.substring(8, 24);
        } catch (Exception e) {
            log.error("toMD5 error : ", e);
        }
        return "";
    }

    public static void main(String[] args) throws Exception {
        AESUtil icopCipherUtil = new AESUtil();
        String encrypt = icopCipherUtil.Encrypt("123456", "123456");
        System.out.println(encrypt);
        String decrypt = icopCipherUtil.Decrypt(encrypt, "123456");
        System.out.println(decrypt);
        //数据库名:WIWC4bMcs7yKBm+Qhfbv0A==
        //数据库密码:FPZHwqC9JznDebyD6ujiUw==
    }

}
