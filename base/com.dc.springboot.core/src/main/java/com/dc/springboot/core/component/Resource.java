package com.dc.springboot.core.component;

import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class Resource implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
        Resource.applicationContext = applicationContext;
    }

    public static ApplicationContext getApplicationContext() {
        return applicationContext;
    }

    public static Object getBean(String name) {
        try {
            return getApplicationContext().getBean(name);
        } catch (BeansException e) {
            log.warn("getBean error: {}", e.getMessage());
            return null;
        }
    }

    public static <T> T getBean(Class<T> clazz) {
        try {
            return getApplicationContext().getBean(clazz);
        } catch (Exception e) {
            log.warn("getBean error: {}", e.getMessage());
            return null;
        }
    }

    public static Object getBeanRequireNonNull(String name) {
        return Objects.requireNonNull(Resource.getBean(name));
    }

    public static <T> T getBeanRequireNonNull(Class<T> clazz) {
        return Objects.requireNonNull(Resource.getBean(clazz));
    }

    public static <T> T getBeanRequireNonNull(String name, Class<T> clazz) {
        return Objects.requireNonNull(Resource.getBean(name, clazz));
    }

    public static <T> boolean containsBean(Class<T> clazz) {
        try {
            return getBean(clazz) != null;
        } catch (Exception e) {
            log.warn("containsBean error: {}", e.getMessage());
            return false;
        }
    }

    public static <T> T getBean(String name, Class<T> clazz) {
        try {
            return getApplicationContext().getBean(name, clazz);
        } catch (BeansException e) {
            log.warn("getBean error: {}", e.getMessage());
            return null;
        }
    }

    public static <T> Map<String, T> getBeans(Class<T> clazz) {
        try {
            return getApplicationContext().getBeansOfType(clazz);
        } catch (BeansException e) {
            log.warn("getBeans error: {}", e.getMessage());
            return Collections.emptyMap();
        }
    }

}
