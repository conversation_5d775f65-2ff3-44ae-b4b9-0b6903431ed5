package com.dc.test.exec;

import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.type.DatabaseType;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class OracleEncodeTest extends BaseExecTest{

    private Connection connection=null;
    {
        connectionConfiguration.setDriverId("oracle_thin");
        connectionConfiguration.setDatabaseType(DatabaseType.ORACLE);
        connectionConfiguration.setHostName("*************");
        connectionConfiguration.setHostPort("1521");
        connectionConfiguration.setDatabaseName("helowin");
        connectionConfiguration.setUserName("system");
        connectionConfiguration.setUserPassword("sys123");
        connectionConfiguration.setProviderProperty("@summer-sid-service@", "SERVICE");
//        connectionConfiguration.setProviderProperty("@summer-session-language@", "AMERICAN");
//        connectionConfiguration.setProviderProperty("@summer-session-territory@", "AMERICA");
//        connectionConfiguration.setProviderProperty("@summer-session-nls-character-set@", "US7ASCII");
    }

    /**
     * 测试编解码转换主程序
     */
    @Test
    public void test(){

        String sqlGetLanguage="SELECT Userenv('language') FROM dual";
//        String sqlInsert="insert into \"USER_YJC\".TEST1(id,name) values(24,'测试数据零二四UTF16')";
//        String sqlSelect="select * from \"USER_YJC\".TEST1 where id=24";
//        insert(sqlInsert);
//        select(sqlSelect);
//        insert2(sqlInsert);
//        select2(sqlSelect);
        selectLanguage(sqlGetLanguage);


    }

    /**
     * 插入语句编解码转换（us7乱码问题）
     */
    @SneakyThrows
    private void insert(String sql){
        String regex="'([^']+)'";
        Pattern pattern=Pattern.compile(regex);
        Matcher matcher= pattern.matcher(sql);
        String executeSql=sql.replaceAll(regex,"?");
        ArrayList<String> array=new ArrayList<>();
        while(matcher.find()){
            String group=matcher.group();
            System.out.println("GBK编码字节流:"+Arrays.toString(group.getBytes("GBK")));
            array.add(new String(group.getBytes("GBK"),StandardCharsets.ISO_8859_1));
        }
        String[] args=array.toArray(String[]::new);
//        for (String str:args
//             ) {
//            System.out.println(str);
//        }
        execute(executeSql,args);
    }

    @Test
    public void testExecInsertStatement() throws UnsupportedEncodingException {
        String group = "测试拼接";
        String gbk = new String(group.getBytes("GBK"), StandardCharsets.ISO_8859_1);
        String sqlInsert = "insert into \"USER_YJC\".TEST1(id,name) values(70,'" + gbk + "')";
        execute(sqlInsert);
    }

    /**
     * 查询语句编解码转换（us7乱码问题）
     */
    @SneakyThrows
    private void select(String sql){
        query(sql,dbcResultSet -> {
            while (dbcResultSet.nextRow()) {
                String name=((JDBCResultSet) dbcResultSet).getString("NAME");
                System.out.println("name:"+name);

                System.out.println(Arrays.toString(name.getBytes()));
                System.out.println(new String(((JDBCResultSet) dbcResultSet).getBytes("NAME")));
                System.out.println("new--->");
                System.out.println(new String(((JDBCResultSet) dbcResultSet).getBytes("NAME"), "GBK"));
                System.out.println("ISO_8859_1解码的字节流:"+Arrays.toString(((JDBCResultSet) dbcResultSet).getString("NAME").getBytes(StandardCharsets.ISO_8859_1)));
//                String str=new String(name.getBytes(StandardCharsets.ISO_8859_1),"GBK");
//                System.out.println(Arrays.toString(name.getBytes(StandardCharsets.ISO_8859_1)));
//                System.out.println(Arrays.toString(str.getBytes(StandardCharsets.ISO_8859_1)));
//                System.out.println("转码后ISO_8859_1:"+new String(name.getBytes(StandardCharsets.ISO_8859_1),"GBK"));

            }
        });
    }

    /**
     * 查询语句编解码转换（us7乱码问题）
     */
    @SneakyThrows
    private void selectLanguage(String sql){
        query(sql,dbcResultSet -> {
            while (dbcResultSet.nextRow()) {
                String language=((JDBCResultSet) dbcResultSet).getString("USERENV('LANGUAGE')");
                String str=(String)dbcResultSet.getAttributeValue(0);
                System.out.println(language.contains("US7ASCII"));
            }
        });
    }

    /**
     * 项目织入编解码代码后的插入测试函数
     * 插入语句编解码转换（us7乱码问题）
     */
    @SneakyThrows
    private void insert2(String sql){
        String regex="'([^']+)'";
        Pattern pattern=Pattern.compile(regex);
        Matcher matcher= pattern.matcher(sql);
        String executeSql=sql.replaceAll(regex,"?");
        ArrayList<String> array=new ArrayList<>();
        while(matcher.find()){
            String group=matcher.group();
            System.out.println("GBK编码字节流:"+Arrays.toString(group.getBytes("GBK")));
            array.add(group);
        }
        String[] args=array.toArray(String[]::new);
        execute(executeSql,args);
    }


    /**
     * 项目织入编解码代码后的查询测试函数
     * 查询语句编解码转换（us7乱码问题）
     */
    @SneakyThrows
    private void select2(String sql){
        query(sql,dbcResultSet -> {
            while (dbcResultSet.nextRow()) {
                String name=((JDBCResultSet) dbcResultSet).getString("NAME");
                System.out.println("name:"+name);
                System.out.println(Arrays.toString(name.getBytes()));
                System.out.println(new String(((JDBCResultSet) dbcResultSet).getBytes("NAME")));
                System.out.println("new--->");
                System.out.println(new String(((JDBCResultSet) dbcResultSet).getBytes("NAME"), "GBK"));
                System.out.println("ISO_8859_1解码的字节流:"+Arrays.toString(((JDBCResultSet) dbcResultSet).getString("NAME").getBytes(StandardCharsets.ISO_8859_1)));
            }
        });
    }


    @Test
    @SneakyThrows
    public void test01(){
        String str="";
        System.out.println("GBK编码的字节流:"+Arrays.toString(str.getBytes("GBK")));
        String strEncode=new String(str.getBytes("GBK"),StandardCharsets.ISO_8859_1);
        System.out.println("ISO解码的字节流:"+Arrays.toString(strEncode.getBytes(StandardCharsets.ISO_8859_1)));
        System.out.println(strEncode);
        System.out.println(new String(strEncode.getBytes(StandardCharsets.ISO_8859_1),"GBK"));

        System.out.println("US7ASCII编码的字节流:"+Arrays.toString(str.getBytes("ASCII")));
        System.out.println("GBK编码的字节流:"+Arrays.toString(str.getBytes("GBK")));
        System.out.println("ISO解码的字节流:"+Arrays.toString(str.getBytes(StandardCharsets.ISO_8859_1)));
        System.out.println(new String(str.getBytes(StandardCharsets.ISO_8859_1),"GBK"));
        System.out.println("ISO解码的字节流:"+Arrays.toString(str.getBytes(StandardCharsets.US_ASCII)));
        System.out.println(new String(str.getBytes(StandardCharsets.ISO_8859_1),"UTF-16"));
        System.out.println("UNICODE编码的字节流:"+Arrays.toString(str.getBytes("UNICODE")));
    }

    @Test
    public void fun(){
        String sql="insert into TEST_ENCODE_DECODE_01(id,name) values(003,'测试数据003','测试数据004')";
        String regex="'([^']+)'";
        Pattern pattern=Pattern.compile(regex);
        Matcher matcher= pattern.matcher(sql);
        String executeSql=sql.replaceAll(regex,"?");
        ArrayList<String> array=new ArrayList<>();
        while(matcher.find()){
            String group=matcher.group();
            array.add(group);
        }
        String[] strArr=array.toArray(String[]::new);
        System.out.println(executeSql);
        System.out.println(array.toString());
        for (String obj:strArr
             ) {
            System.out.println(obj);
        }
    }

    public void fun1(int... args){
        System.out.println(Arrays.toString(args));
    }

    @Test
    public void fun2(){
        int[] a=new int[]{1,2,3,4};
        fun1(a);
    }












}
