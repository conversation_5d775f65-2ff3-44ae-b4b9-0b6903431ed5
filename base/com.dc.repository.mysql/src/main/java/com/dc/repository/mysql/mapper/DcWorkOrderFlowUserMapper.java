package com.dc.repository.mysql.mapper;

import com.dc.repository.mysql.batch.EasyBaseMapper;
import com.dc.repository.mysql.model.DcWorkOrderFlowUser;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface DcWorkOrderFlowUserMapper extends EasyBaseMapper<DcWorkOrderFlowUser> {

    void save(DcWorkOrderFlowUser dcWorkOrderFlowUser);

    List<DcWorkOrderFlowUser> getStepUserById(Integer orderId);
}
