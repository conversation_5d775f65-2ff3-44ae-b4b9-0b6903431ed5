package com.dc.repository.mysql.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.dc.repository.mysql.batch.EasyBaseMapper;
import com.dc.repository.mysql.model.OrderSchemaTask;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface OrderSchemaTaskMapper extends EasyBaseMapper<OrderSchemaTask> {

    default void updateStatus(Integer orderId, String schemaId, Integer status, String failReason) {
        LambdaUpdateWrapper<OrderSchemaTask> wrapper = new LambdaUpdateWrapper<OrderSchemaTask>()
                .eq(OrderSchemaTask::getOrder_id, orderId)
                .eq(OrderSchemaTask::getSchema_id, schemaId)
                .set(OrderSchemaTask::getSchema_status, status);
        if (StringUtils.isNotBlank(failReason)) {
            wrapper.set(OrderSchemaTask::getCheck_fail_reason, failReason);
        }
        update(
                null,
                wrapper
        );
    }

    default List<OrderSchemaTask> getRecordsByOrderId(Integer orderId) {
        return selectList(new LambdaQueryWrapper<OrderSchemaTask>()
                .eq(OrderSchemaTask::getOrder_id, orderId));
    }

    default List<OrderSchemaTask> getRecordsByOrderIdAndSchemaId(Integer orderId, List<String> schemaIds) {
        return selectList(new LambdaQueryWrapper<OrderSchemaTask>()
                .eq(OrderSchemaTask::getOrder_id, orderId)
                .in(OrderSchemaTask::getSchema_id, schemaIds));
    }

    default Integer updateStatus(Integer orderId, String schemaId, Integer status, Integer currentStatus) {
        return update(
                null,
                new LambdaUpdateWrapper<OrderSchemaTask>()
                        .eq(OrderSchemaTask::getOrder_id, orderId)
                        .eq(OrderSchemaTask::getSchema_id, schemaId)
                        .eq(OrderSchemaTask::getSchema_status, currentStatus)
                        .set(OrderSchemaTask::getSchema_status, status)
        );
    }

    default OrderSchemaTask getRecord(Integer orderId, String schemaId) {
        return selectOne(new LambdaQueryWrapper<OrderSchemaTask>()
                .eq(OrderSchemaTask::getOrder_id, orderId)
                .eq(OrderSchemaTask::getSchema_id, schemaId));
    }

    default void updateToken(OrderSchemaTask orderSchemaTask) {
        update(
                null,
                new LambdaUpdateWrapper<OrderSchemaTask>()
                        .eq(OrderSchemaTask::getId, orderSchemaTask.getId())
                        .set(OrderSchemaTask::getProducer_token, orderSchemaTask.getProducer_token())
                        .set(OrderSchemaTask::getConsumer_token_list, orderSchemaTask.getConsumer_token_list())
        );
    }

    default void updateStatus(List<Integer> schemaTasks, Integer status) {
        update(
                null,
                new LambdaUpdateWrapper<OrderSchemaTask>()
                        .in(OrderSchemaTask::getId, schemaTasks)
                        .set(OrderSchemaTask::getSchema_status, status)
        );
    }

    default OrderSchemaTask getRecordById(Integer id) {
        return selectOne(new LambdaQueryWrapper<OrderSchemaTask>().eq(OrderSchemaTask::getId, id));
    }

    Integer getUnfinishedCnt(OrderSchemaTask orderSchemaTask);

    Integer getUncheckWithFailCnt(OrderSchemaTask orderSchemaTask);

    List<OrderSchemaTask> selectOrderSchemaTasks(@Param("orderId") Integer orderId, @Param("schemaIdList") List<String> schemaIdList);
}
