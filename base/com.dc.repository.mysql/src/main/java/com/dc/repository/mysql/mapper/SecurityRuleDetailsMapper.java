package com.dc.repository.mysql.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dc.repository.mysql.model.SecurityRuleDetails;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@Mapper
public interface SecurityRuleDetailsMapper extends BaseMapper<SecurityRuleDetails> {

    String getSecurityRuleValue(@Param("key") String key, @Param("connectId") String connectId);

    List<SecurityRuleDetails> findByRuleSetIdAnDetailsTplKeys(@Param("ruleSetId") Integer ruleSetId, @Param("detailsTplKeys") Collection<String> detailsTplKeys);
}
