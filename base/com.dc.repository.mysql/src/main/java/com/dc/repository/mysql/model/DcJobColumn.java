package com.dc.repository.mysql.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 用于测试数据生成
 */
@Data
@TableName("xxl_dc_job_columns")
public class DcJobColumn {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("table_name")
    private String tableName;

    @TableField("column_name")
    private String columnName;

    @TableField("type_name")
    private String typeName;

    @TableField("connect_id")
    private String connectId;

    @TableField("schema_id")
    private String schemaId;

    @TableField("dc_job_id")
    private int dcJobId;

    private String algorithm;

}
