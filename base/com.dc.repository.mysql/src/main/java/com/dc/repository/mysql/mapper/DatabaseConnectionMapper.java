package com.dc.repository.mysql.mapper;

import com.dc.repository.mysql.batch.EasyBaseMapper;
import com.dc.repository.mysql.model.DatabaseConnection;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface DatabaseConnectionMapper extends EasyBaseMapper<DatabaseConnection> {

    List<DatabaseConnection> getAllConnections();

    /**
     * 获取oracle数据库开启 僵尸对象的实例列表
     *
     * @return list of DatabaseConnection
     */
    List<DatabaseConnection> getZombieInstances();

    DatabaseConnection getActiveConnectionByUniqueKey(@Param("unique_key") String unique_key);

    DatabaseConnection getConnectionByUniqueKey(@Param("unique_key") String unique_key);

    List<DatabaseConnection> getByUuids(List<String> list);

    void updateBySyncId(Map<String, String> map);

    List<DatabaseConnection> getConnections();

    DatabaseConnection getInstanceByDblinkName(@Param("dblink_name") String dblink_name);

    List<DatabaseConnection> getDbEntity();

    List<DatabaseConnection> getRaseSqlConnections();
}
