package com.dc.repository.mysql.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dc.repository.mysql.model.Order;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

@Mapper
public interface DcWorkOrderMapper extends BaseMapper<Order> {

    Order getById(Integer id);

    int updateStatus(Order order);

    int updateStatusWithReason(Order order);

    int updateFailStatus(Map<String, Object> map);

    int updateAuditStatus(Map<String, Object> map);

    /**
     * 工单apply_content字段由php维护
     *
     * @param order order
     * @return updated row
     */
    @Deprecated
    int updateApplyContent(Order order);

    int updateByStatus(@Param("order") Order order, @Param("status") Integer status);

    /**
     * 判断当前工单是否有正在运行的任务
     *
     * @param id order id
     * @return updated row
     */
    int existsOrderExecute(@Param("orderId") Integer id);

    /**
     * 更新工单token
     *
     * @param order order
     * @return updated row
     */
    int updateToken(@Param("order") Order order);

    int updateCheckFailReason( Order order);

    int updateExecuteFailReason( Order order);

    int updateOrderStatus(Order order);

    int updateStatusAndCheckFailReason(Order order);
}
