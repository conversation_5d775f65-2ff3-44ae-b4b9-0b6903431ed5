package com.dc.repository.mysql.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.Objects;

@Data
@TableName("dc_db_schema")
public class Schema {

    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    private Date gmt_create;

    private Date gmt_modified;

    private int is_delete;

    private String unique_key;

    @TableField("schema_name")
    private String schema_name;

    private Integer db_type;

    private Integer table_count;

    private String charset;

    private Integer is_sys;

    private String connect_id;

    private String pid;

    private String def_dbo_name;

    private String catalog_name;

    private Integer is_private;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        Schema schema = (Schema) o;
        return db_type.equals(schema.db_type) && connect_id.equals(schema.connect_id) && schema_name.equals(schema.schema_name)
                && ((StringUtils.isBlank(catalog_name) && StringUtils.isBlank(schema.catalog_name))
                || (StringUtils.isNotBlank(catalog_name) && catalog_name.equals(schema.catalog_name)));
    }

    @Override
    public int hashCode() {
        if (StringUtils.isNotBlank(catalog_name)) {
            return Objects.hash(db_type, connect_id, schema_name, catalog_name);
        } else {
            return Objects.hash(db_type, connect_id, schema_name);
        }
    }

    @Override
    public String toString() {
        return "Schema{" +
                "unique_key='" + unique_key + '\'' +
                ", schema_name='" + schema_name + '\'' +
                ", db_type=" + db_type +
                ", connect_id='" + connect_id + '\'' +
                ", pid='" + pid + '\'' +
                ", catalog_name='" + catalog_name + '\'' +
                '}';
    }
}
