package com.dc.repository.mysql.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("pa_db_table_field")
public class PaDbTableField {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("connection_id")
    private String connectionId;

    @TableField("schema_id")
    private String schemaId;

    @TableField("table_name")
    private String tableName;

    @TableField("fields")
    private String fields;

    @TableField("gmt_create")
    private Date gmtCreate;

    @TableField("gmt_modified")
    private Date gmtModified;

    @TableField("is_delete")
    private Integer isDelete;
}
