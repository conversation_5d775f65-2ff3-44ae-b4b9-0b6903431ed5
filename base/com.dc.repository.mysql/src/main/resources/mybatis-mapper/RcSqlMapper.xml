<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.RcSqlMapper">


    <resultMap id="OrderInfo" type="com.dc.repository.mysql.model.RcSql">
        <result column="id" property="id"/>
        <result column="rc_table_id" property="rc_table_id"/>
        <result column="session_id" property="session_id"/>
        <result column="operation" property="operation"/>
        <result column="sql" property="sql"/>
        <result column="operator" property="operator"/>
        <result column="is_delete" property="is_delete"/>
        <result column="update_columns" property="update_columns"/>
        <result column="gmt_create" property="gmt_create"/>
        <result column="gmt_modified" property="gmt_modified"/>
        <result column="rc_batch_id" property="rc_batch_id"/>
        <result column="transaction_index" property="transaction_index"/>
        <result column="state" property="state"/>
        <result column="log" property="log"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id,
		t.rc_table_id,
		t.session_id,
		t.operation,
		t.sql,
		t.operator,
        t.is_delete,
        t.update_columns,
        t.gmt_create,
        t.gmt_modified,
        t.rc_batch_id,
        t.transaction_index,
        t.state,
        t.log
    </sql>

    <insert id="add" parameterType="com.dc.repository.mysql.model.RcSql" keyColumn="id"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO `rc_sql` (`rc_table_id`,`session_id`,`operation`,`sql`,`operator`,`update_columns`,`gmt_create`,`gmt_modified`,`rc_batch_id`,`transaction_index`,`state`,`log`)
        values (#{rc_table_id},#{session_id},#{operation},#{sql},#{operator},#{update_columns},now(6),now(6),#{rc_batch_id},#{transaction_index},#{state},#{log})
    </insert>

    <select id="getRcSqlBySessionId" parameterType="com.dc.repository.mysql.model.RcSql"
            resultType="com.dc.repository.mysql.model.RcSql">
        SELECT <include refid="Base_Column_List" />
        FROM `rc_sql` AS t where `session_id` = #{session_id} and `transaction_index` = #{transaction_index} AND t.is_delete = 0
    </select>

    <update id="updateById" parameterType="com.dc.repository.mysql.model.RcSql">
        update `rc_sql` set `rc_batch_id` = #{rc_batch_id} where `id` = #{id} AND is_delete = 0
    </update>

    <select id="getRcSqlById" parameterType="java.lang.Long"
            resultType="com.dc.repository.mysql.model.RcSql">
        SELECT <include refid="Base_Column_List" />
        FROM `rc_sql` AS t
        WHERE t.id = #{id} AND t.is_delete = 0
    </select>

    <select id="getRcSqlListById" parameterType="map"
            resultType="com.dc.repository.mysql.model.RcSql">
        SELECT <include refid="Base_Column_List" />
        FROM `rc_sql` AS t
        WHERE t.is_delete = 0 AND t.id in
        <foreach item="id" collection="ids" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

    <select id="getRcSqlListByBatchId" parameterType="java.lang.Long"
            resultType="com.dc.repository.mysql.model.RcSql">
        SELECT <include refid="Base_Column_List" />
        FROM `rc_sql` AS t
        WHERE t.rc_batch_id = #{rc_batch_id} AND t.is_delete = 0
    </select>

    <select id="getRcSqlIdByBatchId" parameterType="java.lang.Long"
            resultType="java.lang.Long">
        SELECT t.id
        FROM `rc_sql` AS t
        WHERE t.rc_batch_id = #{rc_batch_id} AND t.is_delete = 0
    </select>

    <update id="deleteRcSqlById" parameterType="java.lang.Long">
        UPDATE `rc_sql`
        SET `is_delete` = 1
        WHERE id = #{id}
    </update>

    <select id="getRcSqlIdBySessionId" parameterType="com.dc.repository.mysql.model.RcSql"
            resultType="java.lang.Long">
        select id from `rc_sql` where `session_id` = #{session_id} and `transaction_index` = #{transaction_index} AND is_delete = 0
    </select>

    <select id="getRcSqlTableId" parameterType="com.dc.repository.mysql.model.RcSql"
            resultType="java.lang.Long">
        SELECT DISTINCT t.rc_table_id
        FROM `rc_sql` AS t where `session_id` = #{session_id} and `transaction_index` = #{transaction_index} AND t.is_delete = 0
    </select>

    <select id="getExpiredRcSqlList" parameterType="java.lang.Integer" resultType="com.dc.repository.mysql.model.RcSql">
        SELECT <include refid="Base_Column_List" />
        FROM `rc_sql` AS t
        WHERE t.`rc_batch_id` IS NULL
        OR t.`rc_batch_id` IN
        (SELECT `id` FROM `rc_batch` WHERE TIMESTAMPDIFF(DAY,`gmt_create`,now()) >= #{expired_day})
    </select>

    <update id="updateExpiredRcSql" parameterType="java.lang.Long" >
        UPDATE `rc_sql`
        SET `is_delete` = 1
        WHERE id = #{id}
    </update>

    <delete id="deleteExpiredRcSql" parameterType="java.lang.Long">
        DELETE FROM `rc_sql`
        WHERE id = #{id}
    </delete>

</mapper>