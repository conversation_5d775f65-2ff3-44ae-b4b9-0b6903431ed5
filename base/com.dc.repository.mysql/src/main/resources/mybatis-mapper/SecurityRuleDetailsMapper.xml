<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.SecurityRuleDetailsMapper">

    <select id="getSecurityRuleValue" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT dc_security_rule_details.value
        FROM dc_security_rule_details
            JOIN dc_db_connection ON dc_security_rule_details.rule_set_id = dc_db_connection.security_rule_set_id
        WHERE dc_security_rule_details.details_tpl_key = #{key}
            AND dc_db_connection.unique_key = #{connectId}
    </select>

    <select id="findByRuleSetIdAnDetailsTplKeys" resultType="com.dc.repository.mysql.model.SecurityRuleDetails">
        SELECT details_tpl_key AS detailsTplKey,
        value
        FROM dc_security_rule_details
        WHERE rule_set_id = #{ruleSetId} AND is_delete = 0 AND details_tpl_key IN
        <foreach collection="detailsTplKeys" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>