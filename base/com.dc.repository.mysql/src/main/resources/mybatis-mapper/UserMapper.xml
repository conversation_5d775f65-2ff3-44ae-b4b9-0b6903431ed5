<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.UserMapper">

    <select id="getConnectManagerUser" parameterType="java.lang.String"  resultType="java.lang.String" >
        SELECT `user`.`unique_key`
        FROM `dc_db_connection_user` `base`
        INNER JOIN `dc_sys_user` `user`
        ON base.user_id = user.unique_key
        AND user.is_delete = 0
        WHERE `base`.`is_delete` = 0
        AND `base`.`connect_id` = #{connect_id}
        AND `base`.`relation_type` = 'connect_manager'
    </select>

    <select id="getUsersByPage" parameterType="java.util.Map"  resultType="com.dc.repository.mysql.model.User" >
        SELECT id, unique_key as uniqueKey, username,org_ids as orgIds,r_org_ids as rOrgIds
        FROM dc_sys_user user
        limit #{offset},#{limit}
    </select>

    <update id="updateUserStatus" parameterType="java.util.List">
        update dc_sys_user set is_active = 0
        where username in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="getAllUser" parameterType="java.lang.String"  resultType="com.dc.repository.mysql.model.User" >
        SELECT unique_key as uniqueKey, username from dc_sys_user where is_delete = 0
    </select>

    <update id="updateBatchById" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item">
            update dc_sys_user t
            <set>
                <if test="null != item.orgIds">
                    t.org_ids = #{item.orgIds},
                </if>
                <if test="null != item.rOrgIds">
                    t.r_org_ids = #{item.rOrgIds},
                </if>
                <if test="null != item.gmtModified">
                    t.gmt_modified = #{item.gmtModified},
                </if>
            </set>
            where t.id = #{item.id}
        </foreach>

    </update>

    <select id="getAllDisableUserByExpireTime" parameterType="java.lang.String"  resultType="com.dc.repository.mysql.model.User" >
        select u.id,u.username,u.real_name,u.unique_key as uniqueKey from dc_sys_user u
        left join dc_sys_user_active a on u.unique_key=a.user_id
        where u.is_active = 1 and ((a.last_action_at &lt;= #{expire_time} and a.last_action_at is not null) or (a.last_action_at is null and u.gmt_create &lt;= #{expire_time}))
    </select>

    <update id="disableUserByExpireTime" parameterType="java.util.List">
        update dc_sys_user u
        left join dc_sys_user_active a on u.unique_key=a.user_id
        set u.is_active = 0
        where u.is_active = 1 and ((a.last_action_at &lt;= #{expire_time} and a.last_action_at is not null) or (a.last_action_at is null and u.gmt_create &lt;= #{expire_time}))
    </update>
    <update id="disableUserById">
        update dc_sys_user u
            set u.is_active = 0
        where u.id = #{id}
    </update>
</mapper>
