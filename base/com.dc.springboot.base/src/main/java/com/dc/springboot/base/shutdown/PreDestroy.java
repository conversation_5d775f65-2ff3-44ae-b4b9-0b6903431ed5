package com.dc.springboot.base.shutdown;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component
@Slf4j
public class PreDestroy {

    @Autowired(required = false)
    private ApplicationTerminate terminate;

    @javax.annotation.PreDestroy
    public void preDestroy() {
        try {
            if (terminate != null) {
                log.debug("Exist terminate bean.");
                terminate.destroy();
            }
        } finally {
            log.info("----------------------------------------------------------------");
            log.info("成功调用 @PreDestroy 销毁方法。");
            log.info("----------------------------------------------------------------");
        }
    }

}
