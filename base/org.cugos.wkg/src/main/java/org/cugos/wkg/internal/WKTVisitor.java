//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package org.cugos.wkg.internal;

import org.antlr.v4.runtime.tree.ParseTreeVisitor;

public interface WKTVisitor<T> extends ParseTreeVisitor<T> {
    T visitWkt(WKTParser.WktContext var1);

    T visitPoint(WKTParser.PointContext var1);

    T visitLineString(WKTParser.LineStringContext var1);

    T visitPolygon(WKTParser.PolygonContext var1);

    T visitTriangle(WKTParser.TriangleContext var1);

    T visitMultiPoint(WKTParser.MultiPointContext var1);

    T visitMultiLineString(WKTParser.MultiLineStringContext var1);

    T visitCircularString(WKTParser.CircularStringContext var1);

    T visitTin(WKTParser.TinContext var1);

    T visitPolyHedralSurface(WKTParser.PolyHedralSurfaceContext var1);

    T visitMultiPolygon(WKTParser.MultiPolygonContext var1);

    T visitCurvePolygon(WKTParser.CurvePolygonContext var1);

    T visitCurvePolygonItems(WKTParser.CurvePolygonItemsContext var1);

    T visitCurvePolygonElements(WKTParser.CurvePolygonElementsContext var1);

    T visitCompoundCurve(WKTParser.CompoundCurveContext var1);

    T visitCompoundCurveItems(WKTParser.CompoundCurveItemsContext var1);

    T visitCompoundCurveElements(WKTParser.CompoundCurveElementsContext var1);

    T visitMultiCurve(WKTParser.MultiCurveContext var1);

    T visitMultiCurveItems(WKTParser.MultiCurveItemsContext var1);

    T visitMultiCurveElements(WKTParser.MultiCurveElementsContext var1);

    T visitMultiSurface(WKTParser.MultiSurfaceContext var1);

    T visitMultiSurfaceItems(WKTParser.MultiSurfaceItemsContext var1);

    T visitMultiSurfaceElements(WKTParser.MultiSurfaceElementsContext var1);

    T visitGeometryCollection(WKTParser.GeometryCollectionContext var1);

    T visitGeometryCollectionItems(WKTParser.GeometryCollectionItemsContext var1);

    T visitGeometryCollectionElements(WKTParser.GeometryCollectionElementsContext var1);

    T visitLineStringCoordinates(WKTParser.LineStringCoordinatesContext var1);

    T visitPolygonCoordinates(WKTParser.PolygonCoordinatesContext var1);

    T visitCoordinate(WKTParser.CoordinateContext var1);

    T visitCoordinates(WKTParser.CoordinatesContext var1);

    T visitCoordinatesets(WKTParser.CoordinatesetsContext var1);

    T visitCoordinatesetsset(WKTParser.CoordinatesetssetContext var1);

    T visitEmpty(WKTParser.EmptyContext var1);

    T visitSrid(WKTParser.SridContext var1);

    T visitDimension(WKTParser.DimensionContext var1);
}
