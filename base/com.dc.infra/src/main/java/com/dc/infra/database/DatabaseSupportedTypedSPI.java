
package com.dc.infra.database;

import com.dc.infra.database.type.DatabaseType;
import com.dc.infra.spi.TypedSPI;

import java.util.Collection;

/**
 * Database supported SPI.
 */
public interface DatabaseSupportedTypedSPI extends TypedSPI {
    
    /**
     * Get supported database types.
     *
     * @return supported database types
     */
    Collection<DatabaseType> getSupportedDatabaseTypes();
}
