
package com.dc.parser.ext.mysql.statement;

import com.dc.infra.database.type.DatabaseType;
import com.dc.infra.spi.TypedSPILoader;
import com.dc.parser.model.statement.SQLStatement;

/**
 * MySQL statement.
 */
public interface MySQLStatement extends SQLStatement {
    
    @Override
    default DatabaseType getDatabaseType() {
        return TypedSPILoader.getService(DatabaseType.class, DatabaseType.Constant.MYSQL);
    }
}
