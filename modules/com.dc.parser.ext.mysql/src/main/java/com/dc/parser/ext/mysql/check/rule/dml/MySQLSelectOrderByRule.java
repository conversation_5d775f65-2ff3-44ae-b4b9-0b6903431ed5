package com.dc.parser.ext.mysql.check.rule.dml;

import com.dc.parser.ext.mysql.check.rule.listener.MySQLWithOrderByListener;
import com.dc.parser.model.enums.CheckRuleDatabasePrefix;
import com.dc.parser.model.enums.CheckRuleUniqueKey;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.SQLRule;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.statement.dml.SelectStatement;
import org.antlr.v4.runtime.tree.ParseTreeWalker;

public class MySQLSelectOrderByRule implements SQLRule {

    private final ParseTreeWalker parseTreeWalker = ParseTreeWalker.DEFAULT;

    @Override
    public String getType() {
        return CheckRuleDatabasePrefix.MYSQL_.getValue() + CheckRuleUniqueKey.DML_CHECK_SELECT_WITH_ORDER_BY.getValue();
    }

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        if ((sqlStatement instanceof SelectStatement) && sqlStatement.getParseTree() != null) {
            MySQLWithOrderByListener withOrderByListener = new MySQLWithOrderByListener();
            parseTreeWalker.walk(withOrderByListener, sqlStatement.getParseTree());
            if (withOrderByListener.isHasOrderBy()) {
                return CheckResult.buildFailResult(parameter.getCheckRuleContent());
            }
        }

        return CheckResult.DEFAULT_SUCCESS_RESULT;
    }
}
