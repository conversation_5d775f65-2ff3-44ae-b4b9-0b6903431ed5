package com.dc.parser.ext.mysql.check.rule.dml;

import com.dc.parser.model.enums.CheckRuleDatabasePrefix;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.dml.AliasRule;
import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.segment.dml.item.ColumnProjectionSegment;
import com.dc.parser.model.segment.generic.table.JoinTableSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.segment.generic.table.TableSegment;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.statement.dml.SelectStatement;
import com.dc.parser.model.util.RowFilterRewriteUtil;

import java.util.List;

public class MySQLAliasRule extends AliasRule {

    @Override
    public String getType() {
        return CheckRuleDatabasePrefix.MYSQL_.getValue() + super.getType();
    }

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        if (!(sqlStatement instanceof SelectStatement)) {
            return CheckResult.DEFAULT_SUCCESS_RESULT;
        }

        SelectStatement checkStatement = (SelectStatement) sqlStatement;
        List<SelectStatement> selectStatements = RowFilterRewriteUtil.extractAllSelectFromSelect(checkStatement);

        for (SelectStatement statement : selectStatements) {

            if (statement.getFrom().isPresent() && sameName(statement.getFrom().get())) {
                return CheckResult.buildFailResult(parameter.getCheckRuleContent());
            }

            List<ColumnProjectionSegment> columnProjectionSegments = RowFilterRewriteUtil.extractColumnProjectionFromSelectProjections(statement);

            for (ColumnProjectionSegment columnProjection : columnProjectionSegments) {
                ColumnSegment columnSegment = columnProjection.getColumn();

                if (columnProjection.getAliasSegment().isPresent()
                        && columnProjection.getAliasSegment().get().getIdentifier() != null
                        && columnSegment.getOwner().isEmpty()) {

                    String columnName = columnSegment.getIdentifier().getValue();

                    String aliasName = columnProjection.getAliasSegment().get().getIdentifier().getValue();

                    if (aliasName.equalsIgnoreCase(columnName)) {
                        return CheckResult.buildFailResult(parameter.getCheckRuleContent());
                    }
                }
            }
        }

        return CheckResult.DEFAULT_SUCCESS_RESULT;
    }

    public boolean sameName(TableSegment tableSegment) {
        if (tableSegment instanceof SimpleTableSegment) {
            SimpleTableSegment segment = (SimpleTableSegment) tableSegment;
            if (segment.getTableName() != null && segment.getTableName().getIdentifier() != null
                    && segment.getAliasSegment().isPresent() && segment.getAliasSegment().get().getIdentifier() != null) {

                String tableName = segment.getTableName().getIdentifier().getValue();

                String aliasName = segment.getAliasSegment().get().getIdentifier().getValue();

                return tableName.equals(aliasName);
            }
        } else if (tableSegment instanceof JoinTableSegment) {
            JoinTableSegment segment = (JoinTableSegment) tableSegment;
            return sameName(segment.getLeft()) || sameName(segment.getRight());
        }
        return false;
    }
}
