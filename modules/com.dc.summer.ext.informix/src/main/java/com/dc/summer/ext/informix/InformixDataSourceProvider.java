package com.dc.summer.ext.informix;

import com.dc.summer.ext.generic.GenericDataSourceProvider;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.connection.DBPDriver;

public class InformixDataSourceProvider extends GenericDataSourceProvider {

    @Override
    public String getConnectionURL(DBPDriver driver, DBPConnectionConfiguration connectionInfo) {
        String connectionURL = super.getConnectionURL(driver, connectionInfo);
        connectionURL += ";CLIENT_LOCALE=en_us.57372;NEWCODESET=GBK,8859-1,819";
        return connectionURL;
    }

}
