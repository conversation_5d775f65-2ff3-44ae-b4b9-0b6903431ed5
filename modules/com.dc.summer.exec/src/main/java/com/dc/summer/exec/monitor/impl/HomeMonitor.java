package com.dc.summer.exec.monitor.impl;

import com.dc.summer.exec.monitor.data.HomeStatResult;
import com.dc.summer.exec.monitor.data.MonitorResult;
import com.dc.summer.registry.center.Global;
import com.dc.summer.exec.handler.RecordHandler;
import com.dc.summer.exec.monitor.data.MonitorParam;
import com.dc.summer.exec.monitor.AbstractStatMonitor;
import com.dc.type.DatabaseType;
import com.dc.summer.exec.model.type.RecordType;
import com.dc.summer.registry.DataSourceProviderRegistry;
import com.dc.summer.utils.GeneralUtils;
import com.dc.utils.bean.CloneUtils;
import com.dc.utils.io.FileUtils;

import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

public class HomeMonitor extends AbstractStatMonitor<HomeStatResult> {

    @Override
    protected void execute() {
        // nothing to do
    }

    @Override
    protected void init() {
        super.init();
        HomeStatResult result = new HomeStatResult();
        result.setVersion(GeneralUtils.getProductVersion().toString());

        Map<String, Integer> databases = CloneUtils.transListToMap(
                Arrays.stream(DatabaseType.values()).collect(Collectors.toList()),
                DatabaseType::name,
                DatabaseType::getValue);

        result.setDatabases(databases);

        Map<String, Map<String, String>> map = CloneUtils.transListToMap(
                Arrays.stream(DatabaseType.values()).collect(Collectors.toList()),
                DatabaseType::name,
                databaseType -> new HashMap<>());

        DataSourceProviderRegistry.getInstance()
                .getDataSourceProviders()
                .stream()
                .filter(dataSourceProviderDescriptor -> {
                    DatabaseType[] types = DatabaseType.of(dataSourceProviderDescriptor.getId());
                    if (types != null) {
                        for (DatabaseType type : types) {
                            if (map.containsKey(type.name())) {
                                return true;
                            }
                        }
                    }
                    return false;
                })
                .forEach(dataSourceProviderDescriptor ->
                        dataSourceProviderDescriptor.getDrivers().forEach(driverDescriptor -> {
                                    DatabaseType[] types = DatabaseType.of(dataSourceProviderDescriptor.getId());
                                    for (DatabaseType type : types) {
                                        map.get(type.name()).put(driverDescriptor.getId(), driverDescriptor.getDriverClassName());
                                    }
                                }
                        ));

        result.setDrivers(map);
        result.setJavaVMName(System.getProperty("java.vm.name"));
        result.setJavaVersion(System.getProperty("java.version"));
        Predicate<String> predicate = name -> name.endsWith(".jar");
        result.setJavaDriverPath(
                FileUtils.getByFile(Global.getDRIVERS(), predicate).toArray(String[]::new)
        );
        try {
            String[] classPaths = Arrays.stream(System.getProperty("java.class.path").split(":")).toArray(String[]::new);
            if (classPaths.length == 1) {
                classPaths = FileUtils.getByJar(Global.getUserDir() + "/" + classPaths[0], predicate).toArray(String[]::new);
            }
            result.setJavaClassPath(classPaths);
        } catch (Exception e) {
            List<String> errors = new ArrayList<>();
            errors.add(e.getMessage());

            errors.addAll(Arrays.stream(e.getStackTrace())
                    .map(StackTraceElement::toString)
                    .collect(Collectors.toList()));

            result.setJavaClassPath(errors.toArray(String[]::new));
        }
        result.setStartTime(new Date());

        super.write(Collections.singleton(result));
    }

    @Override
    protected MonitorResult collect(MonitorParam monitorParam) {
        Object o = read().stream().map(RecordHandler.Record::getRow).findFirst().orElse(null);
        return new MonitorResult().setResult(o);
    }

    @Override
    protected RecordType getRecordType() {
        return RecordType.STAT_HOME;
    }

    @Override
    public String getName() {
        return "stat.home";
    }

}
