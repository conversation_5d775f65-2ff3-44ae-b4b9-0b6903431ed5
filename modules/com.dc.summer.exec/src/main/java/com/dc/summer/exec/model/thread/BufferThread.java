package com.dc.summer.exec.model.thread;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

public class BufferThread extends ThreadPoolExecutor implements ThreadFactory {

    private final AtomicInteger atomicInteger = new AtomicInteger();

    private final static BufferThread INSTANCE = new BufferThread();


    private BufferThread() {
        super(0, 100, 60L, TimeUnit.SECONDS, new SynchronousQueue<>());
        this.setThreadFactory(this);
    }

    @Override
    public Thread newThread(Runnable r) {
        Thread thread = Executors.defaultThreadFactory().newThread(r);
        thread.setName(String.format("buffer-Thread-%d", atomicInteger.incrementAndGet()));
        return thread;
    }

    public static Executor getExecutor() {
        return INSTANCE;
    }

}
