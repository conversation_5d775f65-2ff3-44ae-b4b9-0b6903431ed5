package com.dc.summer.exec.model.type;


import lombok.Getter;

@Getter
public enum RecordType {

    DATASOURCE("datasource.txt"),
    SQL("sql.txt"),
    SESSION("session.txt"),
    STAT_SQL("stat-sql.txt"),
    STAT_SESSION("stat-session.txt"),
    STAT_DATASOURCE("stat-datasource.txt"),
    STAT_HOME("stat-home.txt"),
    STAT_THREADPOOL("stat-threadpool.txt"),

    ;

    private final String fileName;

    RecordType(String fileName) {
        this.fileName = fileName;
    }
}
