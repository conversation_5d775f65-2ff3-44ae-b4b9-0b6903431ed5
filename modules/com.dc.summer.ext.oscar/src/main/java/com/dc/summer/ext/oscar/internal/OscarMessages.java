package com.dc.summer.ext.oscar.internal;

import com.dc.summer.registry.center.Global;
import org.eclipse.osgi.util.NLS;

public class OscarMessages extends NLS {
   static final String BUNDLE_NAME = "com.dc.summer.ext.oscar.internal.OscarMessages";
   public static String IndexAttributeWizardPage_attribute;
   public static String IndexAttributeWizardPage_attribute_message;
   public static String IndexAttributeWizardPage_choose_column;
   public static String IndexAttributeWizardPage_error;
   public static String IndexAttributeWizardPage_global_partition;
   public static String IndexAttributeWizardPage_index_name;
   public static String IndexAttributeWizardPage_index_type;
   public static String IndexAttributeWizardPage_input_name;
   public static String IndexAttributeWizardPage_local_partition;
   public static String IndexAttributeWizardPage_no_partition;
   public static String IndexAttributeWizardPage_partition_type;
   public static String IndexAttributeWizardPage_tablespace;
   public static String IndexAttributeWizardPage_unique;
   public static String IndexAttributeWizardPage_online;
   public static String IndexPartitionWizardPage_column_max_value;
   public static String IndexPartitionWizardPage_hash_partition;
   public static String IndexPartitionWizardPage_init;
   public static String IndexPartitionWizardPage_max;
   public static String IndexPartitionWizardPage_next;
   public static String IndexPartitionWizardPage_partition_message;
   public static String IndexPartitionWizardPage_partition_mode;
   public static String IndexPartitionWizardPage_partition_name;
   public static String IndexPartitionWizardPage_range_partition;
   public static String IndexStorageWizardPage_fill;
   public static String IndexStorageWizardPage_split;
   public static String IndexStorageWizardPage_max_value;
   public static String IndexStorageWizardPage_next_value;
   public static String IndexStorageWizardPage_storage_message;
   public static String IndexStorageWizardPage_unlimited;
   public static String IndexStorageWizardPage_value;
   public static String IndexMergeHandler_msg;
   public static String IndexMergeHandler_msg_faild;
   public static String IndexMergeHandler_msg_sucess;
   public static String IndexMergeHandler_msg_title;
   public static String IndexMergeHandler_windowtitle;
   public static String IndexMergeWizardPage_title;
   public static String IndexMergeWizardPage_msg;
   public static String IndexMergeWizardPage_list;
   public static String IndexMergeWizardPage_name;
   public static String IndexMergeWizardPage_type;
   public static String IndexRebuildHandler_msg;
   public static String IndexRebuildHandler_msg_sucess;
   public static String IndexRebuildHandler_msg_title;
   public static String IndexRebuildHandler_sucess_happend_error;
   public static String IndexRebuildHandler_windowtitle;
   public static String IndexRebuildStorageWizardPage_title;
   public static String IndexRebuildStorageWizardPage_msg;
   public static String IndexRebuildStorageWizardPage_group;
   public static String dialog_struct_columns_select_group_columns;
   public static String dialog_struct_columns_select_column;
   public static String ArchiveLogHandler_0;
   public static String ArchiveLogHandler_19;
   public static String ArchiveLogHandler_2;
   public static String ArchiveLogHandler_22;
   public static String ArchiveLogHandler_23;
   public static String ArchiveLogHandler_26;
   public static String ArchiveLogHandler_27;
   public static String ArchiveLogHandler_3;
   public static String ArchiveLogHandler_5;
   public static String ArchiveLogHandler_7;
   public static String ArchiveLogHandler_9;
   public static String AttributesSelectorDialog_clear_all;
   public static String AttributesSelectorDialog_select_all;
   public static String dialog_struct_columns_select_error_load_columns_title;
   public static String dialog_struct_columns_select_error_load_columns_message;
   public static String OSCARUnlimitValueTansformer_unlimited;
   public static String CreatePartitionDialog_day;
   public static String CreatePartitionDialog_hour;
   public static String CreatePartitionDialog_list_partition;
   public static String CreatePartitionDialog_interval_partition;
   public static String CreatePartitionDialog_interval_unit;
   public static String CreatePartitionDialog_interval_value;
   public static String CreatePartitionDialog_minute;
   public static String CreatePartitionDialog_month;
   public static String CreatePartitionDialog_partition_name;
   public static String CreatePartitionDialog_second;
   public static String CreatePartitionDialog_year;
   public static String MViewModeRefreshHandler_msg_error;
   public static String MViewModeRefreshHandler_info_exec;
   public static String MViewModeRefreshHandler_done;
   public static String MViewModeRefreshHandler_mode_refresh_complete_msg;
   public static String MViewModeRefreshHandler_mode_refresh_fast_msg;
   public static String MViewModeRefreshHandler_mode_refresh_force_msg;
   public static String CallHandler_0;
   public static String CommonObjectPropertyTester_label;
   public static String MViewModeRefreshHandler_table_create_mlog;
   public static String MViewModeRefreshHandler_table_drop_mlog;
   public static String NavigatorHandlerObjectRecycle_error_info;
   public static String NavigatorHandlerObjectRecycle_error_title;
   public static String NavigatorHandlerObjectRecycle_info;
   public static String NavigatorHandlerObjectRecycle_info_title;
   public static String NavigatorHandlerObjectRecycle_item_close_function;
   public static String NavigatorHandlerObjectRecycle_item_open_function;
   public static String IndexAttributeWizardPage_column_type;
   public static String IndexAttributeWizardPage_column_length;
   public static String SketchIndexAttributeWizardPage_Dimension;
   public static String SketchIndexAttributeWizardPage_Length;
   public static String SketchIndexAttributeWizardPage_Mode;
   public static String SketchIndexAttributeWizardPage_Level;
   public static String SketchIndexStorageWizardPage_pctincrease;
   public static String SketchIndexAttributeWizardPage_Dimension_Des;
   public static String SketchIndexAttributeWizardPage_Length_Des;
   public static String SketchIndexAttributeWizardPage_Mode_Des;
   public static String SketchIndexAttributeWizardPage_Level_Des;
   public static String SketchIndexStorageWizardPage_pctincrease_Des;
   public static String ObjectValueHandler_parameter_bind_is_not_implemented;
   public static String ContentXML_io_error_while_reading_xml;
   public static String ContentXML_internal_error_when_creating_xmltype;
   public static String XMLWrapper_function_not_supported;
   public static String XMLWrapper_cannot_close_xmltype;

   static {
      Global.initializeMessages("com.dc.summer.ext.oscar.internal.OscarMessages", OscarMessages.class);
   }

   private OscarMessages() {
   }
}
