#
#Thu May 05 15:38:14 CST 2016
dialog_struct_columns_select_group_columns=Column
dialog_struct_columns_select_column=Column Length
IndexAttributeWizardPage_attribute=Attribute
IndexAttributeWizardPage_attribute_message=Enter the attribute information for the index
IndexAttributeWizardPage_choose_column=Please select the required indexed column names in the 
IndexAttributeWizardPage_error=Error
IndexAttributeWizardPage_global_partition=Global Partition
IndexAttributeWizardPage_index_name=Index Name
IndexAttributeWizardPage_index_type=Index Type
IndexAttributeWizardPage_input_name=Enter the index name
IndexAttributeWizardPage_local_partition=Local Partition
IndexAttributeWizardPage_no_partition=No Partition 
IndexAttributeWizardPage_partition_type=Partition Type
IndexAttributeWizardPage_tablespace=Tablespace
IndexAttributeWizardPage_unique=unique
IndexAttributeWizardPage_online=Create online
ArchiveLogHandler_0=Database ''{0}'' is in {1}
ArchiveLogHandler_19=Archive log setting
ArchiveLogHandler_2=Information
ArchiveLogHandler_22=ARCHIVELOG
ArchiveLogHandler_23=NOARCHIVELOG
ArchiveLogHandler_26=Archive log path
ArchiveLogHandler_27=Log path naming rule
ArchiveLogHandler_3=successfully set
ArchiveLogHandler_5=Setup failed
ArchiveLogHandler_7=Setup failed:
ArchiveLogHandler_9=Failed to set the archive log naming rules, please to the parameter Settings in the configuration tool.\ n error message:
AttributesSelectorDialog_select_all=Select All
AttributesSelectorDialog_clear_all=Clear All
dialog_struct_columns_select_error_load_columns_title=Load columns
dialog_struct_columns_select_error_load_columns_message=Error loading table columns
IndexPartitionWizardPage_column_max_value=Column Max
IndexPartitionWizardPage_hash_partition=Hash Partition
IndexPartitionWizardPage_init=Init Size
IndexPartitionWizardPage_max=Max Size
IndexPartitionWizardPage_next=Next Size
IndexPartitionWizardPage_partition_message=Please enter the partition information for the index. 
IndexPartitionWizardPage_partition_mode=Partition Mode
IndexPartitionWizardPage_partition_name=Partition Name
IndexPartitionWizardPage_range_partition=Range Partition
IndexStorageWizardPage_fill=Fill
IndexStorageWizardPage_split=Split
IndexStorageWizardPage_max_value=Max
IndexStorageWizardPage_next_value=Next
IndexStorageWizardPage_storage_message=Enter the stored information for the index.
IndexStorageWizardPage_unlimited=unlimited
IndexStorageWizardPage_value=Value
OSCARUnlimitValueTansformer_unlimited=unlimited
CreatePartitionDialog_day=DAY
CreatePartitionDialog_hour=HOUR
CreatePartitionDialog_list_partition=List Partition
CreatePartitionDialog_interval_partition=Interval Partition
CreatePartitionDialog_interval_value=Interval Value
CreatePartitionDialog_interval_unit=Interval Unit
CreatePartitionDialog_minute=MINUTE
CreatePartitionDialog_month=MONTH
CreatePartitionDialog_partition_name=Partition Name
CreatePartitionDialog_second=SECOND
CreatePartitionDialog_year=YEAR
#MView mode refresh
MViewModeRefreshHandler_info_exec=execute
MViewModeRefreshHandler_mode_refresh_complete_msg=Complete refresh of materialized view "{0}" 
MViewModeRefreshHandler_mode_refresh_fast_msg=Fast refresh of materialized view "{0}" 
MViewModeRefreshHandler_mode_refresh_force_msg=Force refresh of materialized view "{0}" 
MViewModeRefreshHandler_msg_error=occurred an error
MViewModeRefreshHandler_done=Done
CallHandler_0=Call
CommonObjectPropertyTester_label=Storage
MViewModeRefreshHandler_table_create_mlog=Create materialized view log
MViewModeRefreshHandler_table_drop_mlog=Drop materialized view log
#
MViewModeRefreshHandler_table_create_mlog=Create Materialized Log
MViewModeRefreshHandler_table_drop_mlog=Drop Materialized Log
NavigatorHandlerObjectRecycle_error_info=Setting occurred an error, error message:\n
NavigatorHandlerObjectRecycle_error_title=Error Message
NavigatorHandlerObjectRecycle_info=This Settings are effective on the current SESSION only. if you want to permanently set, please go to the Parameters Configuration Tool set parameter "ENABLE_RECYCLEBIN".
NavigatorHandlerObjectRecycle_info_title=Information
NavigatorHandlerObjectRecycle_item_close_function=Close Recycle Function
NavigatorHandlerObjectRecycle_item_open_function=Open Recycle Function
IndexAttributeWizardPage_column_type=Type
IndexAttributeWizardPage_column_length=Length
##########
IndexMergeHandler_msg=Not support merge index
IndexMergeHandler_msg_faild=Merge index "{0}" happened an error:
IndexMergeHandler_msg_sucess=Merge index "{0}" sucess
IndexMergeHandler_msg_title=Information
IndexMergeHandler_windowtitle=Merge Index
IndexMergeWizardPage_title=Merge Index
IndexMergeWizardPage_msg=Please input information of new index
IndexMergeWizardPage_list=Index List
IndexMergeWizardPage_name=Name
IndexMergeWizardPage_type=Index Type
IndexRebuildHandler_msg=Not support rebuild index
IndexRebuildHandler_msg_sucess=Rebuild index "{0}" sucess
IndexRebuildHandler_msg_title=information
IndexRebuildHandler_sucess_happend_error=Rebuild index "{0}" happened an error:
IndexRebuildHandler_windowtitle=Rebuild Index
IndexRebuildStorageWizardPage_title=Rebuild Index
IndexRebuildStorageWizardPage_msg=Please input rebuild arguments
IndexRebuildStorageWizardPage_group=partition
SketchIndexAttributeWizardPage_Dimension=Dimension
SketchIndexAttributeWizardPage_Length=Length
SketchIndexAttributeWizardPage_Mode=Mode
SketchIndexAttributeWizardPage_Level=Level
SketchIndexStorageWizardPage_pctincrease=PctIncrease
ObjectValueHandler_parameter_bind_is_not_implemented=Parameter bind is not implemented
ContentXML_io_error_while_reading_xml=IO error while reading XML
ContentXML_internal_error_when_creating_xmltype=Internal error when creating XMLType
XMLWrapper_function_not_supported=Function not supported
XMLWrapper_cannot_close_xmltype=Can't close XMLType