
package com.dc.summer.ext.greenplum.model;

import com.dc.summer.Log;
import com.dc.summer.ext.postgresql.model.PostgreMaterializedView;
import com.dc.summer.ext.postgresql.model.PostgreTableColumn;
import com.dc.summer.DBException;
import com.dc.summer.ext.postgresql.model.PostgreSchema;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.utils.CommonUtils;

import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

public class GreenplumMaterializedView extends PostgreMaterializedView {

    private static final Log log = Log.getLog(GreenplumMaterializedView.class);

    private int[] distributionColumns;

    private boolean supportsReplicatedDistribution;

    public GreenplumMaterializedView(PostgreSchema catalog, ResultSet dbResult) {
        super(catalog, dbResult);

        if (catalog.getDataSource().isServerVersionAtLeast(9, 1)) {
            supportsReplicatedDistribution = true;
        }
    }

    public GreenplumMaterializedView(PostgreSchema catalog) {
        super(catalog);
    }

    private List<PostgreTableColumn> getDistributionPolicy(DBRProgressMonitor monitor) throws DBException {
        if (distributionColumns == null) {
            try {
                distributionColumns = GreenplumUtils.readDistributedColumns(monitor, this);
            } catch (Throwable e) {
                log.error("Error reading distribution policy", e);
            }
            if (distributionColumns == null) {
                distributionColumns = new int[0];
            }
        }

        if (distributionColumns.length == 0) {
            return null;
        }
        List<PostgreTableColumn> columns = new ArrayList<>(distributionColumns.length);
        for (int i = 0; i < distributionColumns.length; i++) {
            PostgreTableColumn attr = getAttributeByPos(monitor, distributionColumns[i]);
            if (attr == null) {
                log.debug("Bad policy attribute position: " + distributionColumns[i]);
            } else {
                columns.add(attr);
            }
        }
        return columns;
    }

    @Override
    public void appendTableModifiers(DBRProgressMonitor monitor, StringBuilder ddl) {
        try {
            List<PostgreTableColumn> distributionColumns = getDistributionPolicy(monitor);
            if (CommonUtils.isEmpty(distributionColumns)) {
                distributionColumns = GreenplumUtils.getDistributionTableColumns(monitor, distributionColumns, this);
            }

            GreenplumUtils.addObjectModifiersToDDL(monitor, ddl, this, distributionColumns, supportsReplicatedDistribution);
        } catch (DBException e) {
            log.error("Error reading Greenplum table properties", e);
        }
    }
}
