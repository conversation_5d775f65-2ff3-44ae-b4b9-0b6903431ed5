
package com.dc.summer.model.gis;

public enum DBGeometryDimension {
    XY(2, false, false),
    XYZ(3, true, false),
    XYM(3, false, true),
    XYZM(4, true, true);

    private final int coordinates;
    private final boolean z;
    private final boolean m;

    DBGeometryDimension(int coordinates, boolean z, boolean m) {
        this.coordinates = coordinates;
        this.z = z;
        this.m = m;
    }

    public int getCoordinates() {
        return coordinates;
    }

    public boolean hasZ() {
        return z;
    }

    public boolean hasM() {
        return m;
    }
}
