
package com.dc.summer.ext.firebird.model;

import com.dc.summer.model.struct.rdb.DBSTable;
import com.dc.code.NotNull;
import com.dc.summer.model.data.DBDInsertReplaceMethod;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSAttributeBase;

public class FireBirdInsertReplaceMethod implements DBDInsertReplaceMethod {

    @NotNull
    @Override
    public String getOpeningClause(DBSTable table, DBRProgressMonitor monitor) {
        return "UPDATE OR INSERT INTO";
    }

    @Override
    public String getTrailingClause(DBSTable table, DBRProgressMonitor monitor, DBSAttributeBase[] attributes) {
        return null;
    }
}
