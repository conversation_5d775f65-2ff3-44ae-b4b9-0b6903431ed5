
package com.dc.summer.ext.firebird.model;

import com.dc.summer.ext.generic.model.GenericStructContainer;
import com.dc.summer.model.struct.rdb.DBSTable;

public class FireBirdDatabaseTrigger extends FireBirdTrigger<GenericStructContainer> {

    public FireBirdDatabaseTrigger(GenericStructContainer container, String name, String description, FireBirdTriggerType type, int sequence, boolean isSystem) {
        super(container, name, description, type, sequence, isSystem);
    }

    @Override
    public DBSTable getTable() {
        return null;
    }
}
