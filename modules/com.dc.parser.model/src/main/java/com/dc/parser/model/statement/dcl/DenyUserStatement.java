package com.dc.parser.model.statement.dcl;

import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.Collection;
import java.util.LinkedList;

/**
 * Deny user statement.
 */
@Getter
@Setter
public abstract class DenyUserStatement extends AbstractSQLStatement implements DCLStatement {

    private SimpleTableSegment table;

    private final Collection<ColumnSegment> columns = new LinkedList<>();
}
