package com.dc.parser.model.segment.dml.statistics;

import com.dc.parser.model.enums.StatisticsDimension;
import com.dc.parser.model.segment.SQLSegment;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Update statistics option segment.
 */
@Getter
@Setter
@NoArgsConstructor
public final class StatisticsOptionSegment implements SQLSegment {

    private int startIndex;

    private int stopIndex;

    private StatisticsDimension statisticsDimension;

    private String maxDegreeOfParallelism;

    private boolean noRecompute;

    private boolean incremental;

    private boolean autoDrop;

    public StatisticsOptionSegment(final int startIndex, final int stopIndex) {
        this.startIndex = startIndex;
        this.stopIndex = stopIndex;
    }
}
