
package com.dc.parser.model.segment.ddl.constraint.alter;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import com.dc.parser.model.segment.ddl.AlterDefinitionSegment;
import com.dc.parser.model.segment.ddl.constraint.ConstraintSegment;

/**
 * Validate constraint definition segment.
 */
@RequiredArgsConstructor
@Getter
public final class ValidateConstraintDefinitionSegment implements AlterDefinitionSegment {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    private final ConstraintSegment constraintName;
}
