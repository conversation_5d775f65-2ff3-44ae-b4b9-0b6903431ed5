package com.dc.parser.model.segment.dcl;

import com.dc.parser.model.segment.SQLSegment;
import com.dc.parser.model.value.identifier.IdentifierValue;
import lombok.Data;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Data
public class QuotasSegment implements SQLSegment {
    private final int startIndex;

    private final int stopIndex;

    private String quotasSize;

    private String capacityUnit;

    private boolean isUnlimited;

    private IdentifierValue tablespace;
}
