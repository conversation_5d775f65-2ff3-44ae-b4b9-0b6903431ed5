package com.dc.parser.model.context.statement.dcl;

import com.dc.parser.model.context.segment.table.TablesContext;
import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.context.type.TableAvailable;
import com.dc.parser.model.statement.dcl.RevokeStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Revoke statement context.
 */
@Getter
public final class RevokeStatementContext extends CommonSQLStatementContext implements TableAvailable {

    private final TablesContext tablesContext;

    public RevokeStatementContext(final RevokeStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);
        tablesContext = new TablesContext(sqlStatement.getTables());

        // 构造鉴权模型
        extractSqlAuthModel(currentDatabaseName);
    }

    public void extractSqlAuthModel(final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_REVOKE);
        sqlAuthModel.setSchemaName(currentDatabaseName);
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public RevokeStatement getSqlStatement() {
        return (RevokeStatement) super.getSqlStatement();
    }
}
