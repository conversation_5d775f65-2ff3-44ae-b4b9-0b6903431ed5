
package com.dc.parser.model.value.identifier;

import com.dc.infra.database.enums.QuoteCharacter;
import com.dc.parser.model.util.SQLUtils;
import com.google.common.base.Strings;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.ToString;
import com.dc.parser.model.value.ValueASTNode;

/**
 * Identifier value.
 */
@RequiredArgsConstructor
@Getter
@EqualsAndHashCode
@ToString
public final class IdentifierValue implements ValueASTNode<String> {
    
    private final String value;
    
    private final QuoteCharacter quoteCharacter;
    
    public IdentifierValue(final String text) {
        this(SQLUtils.getExactlyValue(text), QuoteCharacter.getQuoteCharacter(text));
    }
    
    public IdentifierValue(final String text, final String reservedCharacters) {
        this(SQLUtils.getExactlyValue(text, reservedCharacters), QuoteCharacter.getQuoteCharacter(text));
    }
    
    /**
     * Get value with quote characters, i.e. `table1` or `field1`
     *
     * @return value with quote characters
     */
    public String getValueWithQuoteCharacters() {
        return null == value ? "" : quoteCharacter.wrap(value);
    }
    
    /**
     * Get quoted content.
     *
     * @param text text
     * @return quote content
     */
    public static String getQuotedContent(final String text) {
        if (Strings.isNullOrEmpty(text)) {
            return text;
        }
        QuoteCharacter quoteCharacter = QuoteCharacter.getQuoteCharacter(text);
        if (QuoteCharacter.NONE == quoteCharacter) {
            return text.trim();
        }
        return text.substring(1, text.length() - 1);
    }
}
