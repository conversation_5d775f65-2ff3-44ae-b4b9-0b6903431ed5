
package com.dc.parser.model.segment.dal;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.model.segment.SQLSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;

/**
 * From table segment.
 */
@Getter
@Setter
public final class FromTableSegment implements SQLSegment {
    
    private int startIndex;
    
    private int stopIndex;
    
    private SimpleTableSegment table;
}
