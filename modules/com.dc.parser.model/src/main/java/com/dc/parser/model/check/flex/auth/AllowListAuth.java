package com.dc.parser.model.check.flex.auth;

import com.dc.parser.model.context.SQLStatementContext;
import com.dc.parser.model.context.type.SchemaAvailable;
import com.dc.parser.model.statement.IgnoredSQLStatement;
import com.dc.parser.model.statement.dal.SetStatement;
import com.dc.parser.model.statement.ddl.DeclareStatement;
import com.dc.parser.model.statement.tcl.BeginTransactionStatement;
import com.dc.parser.model.statement.tcl.CommitStatement;
import com.dc.parser.model.statement.tcl.RollbackStatement;
import com.dc.parser.model.statement.tcl.SavepointStatement;
import com.dc.utils.bean.ClassUtils;
import org.jetbrains.annotations.Nullable;

/**
 * 检查是否属于白名单
 */
public class AllowListAuth implements SQLAuth {

    @Override
    public CheckAuthResult check(SQLStatementContext context, @Nullable CheckAuthParam parameter) {

        // 切换 schema
        if (context instanceof SchemaAvailable && ((SchemaAvailable) context).getSchemaContext() != null) {
            return null;
        }

        if (ClassUtils.isInstanceOfMultiple(context.getSqlStatement(),
                CommitStatement.class, // 事物提交
                RollbackStatement.class, // 事物回滚
                IgnoredSQLStatement.class, // sql：/
                SetStatement.class, // 变量赋值
                BeginTransactionStatement.class, // 事物开启
                SavepointStatement.class, // 事物保存
                DeclareStatement.class  // 声明变量
        )) {
            return CheckAuthResult.DEFAULT_SUCCESS_RESULT;
        }

        return null;
    }

}
