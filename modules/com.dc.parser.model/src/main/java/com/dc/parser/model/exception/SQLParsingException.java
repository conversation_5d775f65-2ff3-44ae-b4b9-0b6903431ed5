
package com.dc.parser.model.exception;

import com.dc.infra.exception.sqlstate.XOpenSQLState;
import lombok.Getter;

/**
 * SQL parsing exception.
 */
@Getter
public final class SQLParsingException extends ParseSQLException {
    
    private static final long serialVersionUID = -6408790652103666096L;
    
    private final Object symbol;
    
    private final int line;
    
    public SQLParsingException(final String message) {
        this(message, "", 1);
    }
    
    public SQLParsingException(final String message, final Object symbol, final int line) {
        super(XOpenSQLState.SYNTAX_ERROR, 0, "You have an error in your SQL syntax: %s", message);
        this.symbol = symbol;
        this.line = line;
    }
}
