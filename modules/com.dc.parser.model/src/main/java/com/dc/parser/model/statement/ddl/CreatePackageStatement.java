package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.ddl.packages.PackageSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;
import lombok.Setter;


/**
 * Create package statement.
 */
@Getter
@Setter
public abstract class CreatePackageStatement extends AbstractSQLStatement implements DDLStatement {

    private PackageSegment packageSegment;
}
