package com.dc.parser.model.context.statement.dal;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.dal.ChecksumTableStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Checksum table statement context.
 */
@Getter
public class ChecksumTableStatementContext extends CommonSQLStatementContext {

    public ChecksumTableStatementContext(final ChecksumTableStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    public void extractSqlAuthModel(final ChecksumTableStatement sqlStatement, final String currentDatabaseName) {
        sqlStatement.getTables().forEach(simpleTableSegment -> {
            SqlAuthModel sqlAuthModel = new SqlAuthModel();
            sqlAuthModel.setOperation(SqlConstant.KEY_SELECT);
            sqlAuthModel.setType(SqlConstant.KEY_TABLE);
            sqlAuthModel.setName(simpleTableSegment.getTableName().getIdentifier().getValue());
            String schemaName = simpleTableSegment.getOwner().map(ownerSegment -> ownerSegment.getIdentifier().getValue()).orElse(currentDatabaseName);
            sqlAuthModel.setSchemaName(schemaName);
            addSqlAuthModel(sqlAuthModel);
        });
    }

    @Override
    public ChecksumTableStatement getSqlStatement() {
        return (ChecksumTableStatement) super.getSqlStatement();
    }
}
