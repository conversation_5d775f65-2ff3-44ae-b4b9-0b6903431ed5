package com.dc.parser.model.context.statement.dal;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.context.type.RemoveAvailable;
import com.dc.parser.model.segment.SQLSegment;
import com.dc.parser.model.statement.dal.ShowTableStatusStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;

import java.util.Collection;
import java.util.LinkedList;

/**
 * Show table status statement context.
 */
public final class ShowTableStatusStatementContext extends CommonSQLStatementContext implements RemoveAvailable {

    public ShowTableStatusStatementContext(final ShowTableStatusStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    public void extractSqlAuthModel(final ShowTableStatusStatement sqlStatement, final String currentDatabaseName) {
        String databaseName = sqlStatement.getFromDatabase().map(fromDatabaseSegment -> fromDatabaseSegment.getDatabase().getIdentifier().getValue())
                .orElse(currentDatabaseName);
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_SHOW);
        sqlAuthModel.setType(SqlConstant.KEY_TABLE);
        sqlAuthModel.setSchemaName(databaseName);
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public ShowTableStatusStatement getSqlStatement() {
        return (ShowTableStatusStatement) super.getSqlStatement();
    }

    @Override
    public Collection<SQLSegment> getRemoveSegments() {
        Collection<SQLSegment> result = new LinkedList<>();
        getSqlStatement().getFromDatabase().ifPresent(result::add);
        return result;
    }
}
