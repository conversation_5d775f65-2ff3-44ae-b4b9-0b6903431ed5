
package com.dc.parser.model.engine;

import com.dc.parser.model.engine.cache.ParseTreeCacheBuilder;
import com.dc.parser.model.engine.parser.SQLParserExecutor;
import com.dc.infra.database.type.DatabaseType;
import com.dc.infra.spi.TypedSPILoader;
import com.github.benmanes.caffeine.cache.LoadingCache;

/**
 * SQL parser engine.
 */
public final class SQLParserEngine {
    
    private final SQLParserExecutor sqlParserExecutor;
    
    private final LoadingCache<String, ParseASTNode> parseTreeCache;
    
    public SQLParserEngine(final DatabaseType databaseType, final CacheOption cacheOption) {
        sqlParserExecutor = new SQLParserExecutor(databaseType);
        parseTreeCache = ParseTreeCacheBuilder.build(cacheOption, databaseType);
    }
    
    public SQLParserEngine(final String databaseType, final CacheOption cacheOption) {
        this(TypedSPILoader.getService(DatabaseType.class, databaseType), cacheOption);
    }
    
    /**
     * Parse SQL.
     *
     * @param sql SQL to be parsed
     * @param useCache whether to use cache
     * @return parse AST node
     */
    public ParseASTNode parse(final String sql, final boolean useCache) {
        return useCache ? parseTreeCache.get(sql) : sqlParserExecutor.parse(sql);
    }
}
