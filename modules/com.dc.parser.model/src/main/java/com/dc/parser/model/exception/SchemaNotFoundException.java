package com.dc.parser.model.exception;

import com.dc.infra.exception.sqlstate.XOpenSQLState;
import com.dc.infra.exception.type.kernel.category.MetaDataSQLException;

/**
 * Schema not found exception.
 */
public final class SchemaNotFoundException extends MetaDataSQLException {

    private static final long serialVersionUID = 2722045034640737328L;

    public SchemaNotFoundException(final String schemaName) {
        super(XOpenSQLState.NOT_FOUND, 1, "Schema '%s' does not exist.", schemaName);
    }
}
