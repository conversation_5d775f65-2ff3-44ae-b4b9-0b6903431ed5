package com.dc.parser.model.context.statement.ddl;

import com.dc.parser.model.context.segment.table.TablesContext;
import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.context.statement.dml.SelectStatementContext;
import com.dc.parser.model.context.type.TableAvailable;
import com.dc.parser.model.enums.SubqueryType;
import com.dc.parser.model.extractor.TableExtractor;
import com.dc.parser.model.statement.ddl.CreateViewStatement;
import lombok.Getter;

import java.util.Collections;
import java.util.List;

/**
 * Create view statement context.
 */
@Getter
public final class CreateViewStatementContext extends CommonSQLStatementContext implements TableAvailable {

    private final TablesContext tablesContext;

    private final SelectStatementContext selectStatementContext;

    public CreateViewStatementContext(final List<Object> params, final CreateViewStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);
        TableExtractor extractor = new TableExtractor();
        extractor.extractTablesFromCreateViewStatement(sqlStatement);
        tablesContext = new TablesContext(extractor.getRewriteTables());
        selectStatementContext = new SelectStatementContext(params, sqlStatement.getSelect(), currentDatabaseName, Collections.emptyList());
        selectStatementContext.setSubqueryType(SubqueryType.VIEW_DEFINITION);
    }

    @Override
    public CreateViewStatement getSqlStatement() {
        return (CreateViewStatement) super.getSqlStatement();
    }
}
