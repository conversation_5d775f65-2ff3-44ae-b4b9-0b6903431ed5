
package com.dc.parser.model.segment.ddl.table;

import com.dc.parser.model.segment.ddl.charset.CharsetNameSegment;
import com.dc.parser.model.value.literal.impl.NumberLiteralValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import com.dc.parser.model.segment.ddl.CreateDefinitionSegment;
import com.dc.parser.model.segment.ddl.engine.EngineSegment;
import com.dc.parser.model.segment.generic.CommentSegment;

import java.util.Optional;

/**
 * Create table option segment.
 */
@Getter
@Setter
@RequiredArgsConstructor
public class CreateTableOptionSegment implements CreateDefinitionSegment {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    private EngineSegment engine;
    
    private CommentSegment commentSegment;

    private CharsetNameSegment charsetName;

    private NumberLiteralValue autoIncrement;
    
    /**
     * Get engine.
     *
     * @return engine
     */
    public Optional<EngineSegment> getEngine() {
        return Optional.ofNullable(engine);
    }
    
    /**
     * Get comment.
     *
     * @return comment
     */
    public Optional<CommentSegment> getComment() {
        return Optional.ofNullable(commentSegment);
    }

    /**
     * Get charset
     *
     * @return charsetNameSegment
     */
    public Optional<CharsetNameSegment> getCharsetName() {
        return Optional.ofNullable(charsetName);
    }

    public Optional<NumberLiteralValue> getAutoIncrement() {
        return Optional.ofNullable(autoIncrement);
    }
}
