package com.dc.parser.model.context.statement.dml;

import com.dc.parser.model.context.segment.table.TablesContext;
import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.context.type.TableAvailable;
import com.dc.parser.model.context.type.WhereAvailable;
import com.dc.parser.model.context.type.WithAvailable;
import com.dc.parser.model.extractor.ColumnExtractor;
import com.dc.parser.model.extractor.ExpressionExtractor;
import com.dc.parser.model.extractor.TableExtractor;
import com.dc.parser.model.segment.dml.assignment.ColumnAssignmentSegment;
import com.dc.parser.model.segment.dml.assignment.SetAssignmentSegment;
import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.segment.dml.expr.BinaryOperationExpression;
import com.dc.parser.model.segment.dml.expr.subquery.SubqueryExpressionSegment;
import com.dc.parser.model.segment.dml.predicate.WhereSegment;
import com.dc.parser.model.segment.generic.WithSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.dml.UpdateStatement;
import com.dc.summer.parser.sql.model.SqlActionModel;
import lombok.Getter;

import java.util.Collection;
import java.util.LinkedList;
import java.util.Optional;

/**
 * Update SQL statement context.
 */
@Getter
public final class UpdateStatementContext extends CommonSQLStatementContext implements TableAvailable, WhereAvailable, WithAvailable {

    private final TablesContext tablesContext;

    private final Collection<WhereSegment> whereSegments = new LinkedList<>();

    private final Collection<ColumnSegment> columnSegments = new LinkedList<>();

    private final Collection<BinaryOperationExpression> joinConditions = new LinkedList<>();

    public UpdateStatementContext(final UpdateStatement sqlStatement) {
        super(sqlStatement);
        tablesContext = new TablesContext(getAllSimpleTableSegments());
        extractWhereSegments(whereSegments, sqlStatement);
        ColumnExtractor.extractColumnSegments(columnSegments, whereSegments);
        ExpressionExtractor.extractJoinConditions(joinConditions, whereSegments);

        // 构造SQL动作模型
        extractSqlActionModel(sqlStatement);
    }

    private void extractSqlActionModel(final UpdateStatement sqlStatement) {
        SqlActionModel sqlActionModel = getSqlActionModel();

        // 获取SetAssignmentSegment
        Optional<SetAssignmentSegment> setAssignmentOpt = sqlStatement.getAssignmentSegment();

        // 检查是否有子查询
        if (setAssignmentOpt.isPresent()) {
            SetAssignmentSegment setAssignment = setAssignmentOpt.get();
            for (ColumnAssignmentSegment assignment : setAssignment.getAssignments()) {
                if (assignment.getValue() instanceof SubqueryExpressionSegment) {
                    // 如果SET值是子查询，设置isUpdateSelect为true
                    sqlActionModel.setUpdateSelect(true);
                    break;
                }
            }
        }
    }

    private void extractWhereSegments(final Collection<WhereSegment> whereSegments, final UpdateStatement updateStatement) {
        updateStatement.getWhere().ifPresent(whereSegments::add);
    }

    private Collection<SimpleTableSegment> getAllSimpleTableSegments() {
        TableExtractor tableExtractor = new TableExtractor();
        tableExtractor.extractTablesFromUpdate(getSqlStatement());
        return tableExtractor.getRewriteTables();
    }

    @Override
    public UpdateStatement getSqlStatement() {
        return (UpdateStatement) super.getSqlStatement();
    }

    @Override
    public Collection<WhereSegment> getWhereSegments() {
        return whereSegments;
    }

    @Override
    public Collection<ColumnSegment> getColumnSegments() {
        return columnSegments;
    }

    @Override
    public Optional<WithSegment> getWith() {
        return getSqlStatement().getWithSegment();
    }
}
