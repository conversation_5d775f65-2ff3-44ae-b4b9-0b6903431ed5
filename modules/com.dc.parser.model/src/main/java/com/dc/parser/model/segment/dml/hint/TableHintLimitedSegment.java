package com.dc.parser.model.segment.dml.hint;

import com.dc.parser.model.segment.SQLSegment;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

/**
 * With table hint limited segment.
 **/
@RequiredArgsConstructor
@Getter
public final class TableHintLimitedSegment implements SQLSegment {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    @Setter
    private String value;
}
