
package com.dc.parser.model.segment.generic;

import com.dc.parser.model.segment.SQLSegment;
import com.dc.parser.model.segment.generic.bounded.TableSegmentBoundInfo;
import com.dc.parser.model.value.identifier.IdentifierValue;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

import java.util.Optional;

/**
 * Owner segment.
 */
@RequiredArgsConstructor
@Getter
@Setter
@EqualsAndHashCode
public final class OwnerSegment implements SQLSegment {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    private final IdentifierValue identifier;
    
    private OwnerSegment owner;

    private TableSegmentBoundInfo tableBoundInfo;
    
    /**
     * Get owner.
     *
     * @return owner segment
     */
    public Optional<OwnerSegment> getOwner() {
        return Optional.ofNullable(owner);
    }

    /**
     * Get table bound info.
     *
     * @return table bound info
     */
    public Optional<TableSegmentBoundInfo> getTableBoundInfo() {
        return Optional.ofNullable(tableBoundInfo);
    }
}
