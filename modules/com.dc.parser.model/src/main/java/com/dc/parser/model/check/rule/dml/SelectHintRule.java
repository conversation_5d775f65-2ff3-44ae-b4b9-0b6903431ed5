package com.dc.parser.model.check.rule.dml;

import com.dc.parser.model.enums.CheckRuleUniqueKey;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.SQLRule;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.statement.dml.*;

public class SelectHintRule implements SQLRule {

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        if (!(sqlStatement instanceof InsertStatement)
                && !(sqlStatement instanceof UpdateStatement)
                && !(sqlStatement instanceof DeleteStatement)
                && !(sqlStatement instanceof SelectStatement)
                && !(sqlStatement instanceof MergeStatement)) {
            return CheckResult.DEFAULT_SUCCESS_RESULT;
        }

        return CheckResult.buildFailResult(parameter.getCheckRuleContent());
    }

    @Override
    public String getType() {
        return CheckRuleUniqueKey.DML_CHECK_SELECT_HINT.getValue();
    }

}
