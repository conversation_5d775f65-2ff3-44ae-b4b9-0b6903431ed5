package com.dc.parser.model.statement.dcl;

import com.dc.parser.model.segment.dcl.DirectoryNameSegment;
import com.dc.parser.model.segment.dcl.GranteeSegment;
import com.dc.parser.model.segment.dcl.OracleRoleOrPrivilegeSegment;
import com.dc.parser.model.segment.dcl.RoleOrPrivilegeSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Data;

import java.util.Collection;
import java.util.Collections;
import java.util.LinkedList;
import java.util.Optional;

/**
 * Grant statement.
 */
@Data
public abstract class GrantStatement extends AbstractSQLStatement implements DCLStatement {
    
    private final Collection<SimpleTableSegment> tables = new LinkedList<>();

    private GranteeSegment grantees;

    public Optional<SimpleTableSegment> getTableSegment() {
        return Optional.empty();
    }

    public Optional<DirectoryNameSegment> getDirectoryNameSegment() {
        return Optional.empty();
    }

    public Collection<OracleRoleOrPrivilegeSegment> getOracleRoleOrPrivileges() {
        return Collections.emptyList();
    }
}
