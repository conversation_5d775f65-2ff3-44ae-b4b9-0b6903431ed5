
package com.dc.parser.model.segment.dml.table;

import com.dc.parser.model.segment.SQLSegment;
import com.dc.parser.model.statement.dml.InsertStatement;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Collection;

/**
 * Multi table conditional into then segment.
 */
@RequiredArgsConstructor
@Getter
public final class MultiTableConditionalIntoThenSegment implements SQLSegment {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    private final Collection<InsertStatement> insertStatements;
}
