package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.generic.OwnerSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.Optional;

/**
 * Create sequence statement.
 */
@Getter
@Setter
public abstract class CreateSequenceStatement extends AbstractSQLStatement implements DDLStatement {
    
    private String sequenceName;

    private OwnerSegment owner;

    /**
     * Get owner.
     *
     * @return owner
     */
    public Optional<OwnerSegment> getOwner() {
        return Optional.ofNullable(owner);
    }
}
