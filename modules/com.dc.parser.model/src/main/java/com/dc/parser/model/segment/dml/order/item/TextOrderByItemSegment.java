
package com.dc.parser.model.segment.dml.order.item;

import com.dc.infra.database.enums.NullsOrderType;
import com.dc.parser.model.enums.OrderDirection;

/**
 * Order by item segment for text.
 */
public abstract class TextOrderByItemSegment extends OrderByItemSegment {
    
    protected TextOrderByItemSegment(final int startIndex, final int stopIndex, final OrderDirection orderDirection, final NullsOrderType nullsOrderType) {
        super(startIndex, stopIndex, orderDirection, nullsOrderType);
    }
    
    /**
     * Get text.
     * 
     * @return text
     */
    public abstract String getText();
}
