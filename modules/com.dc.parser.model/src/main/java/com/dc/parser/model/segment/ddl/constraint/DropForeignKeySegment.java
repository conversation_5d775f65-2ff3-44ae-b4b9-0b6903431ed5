
package com.dc.parser.model.segment.ddl.constraint;

import com.dc.parser.model.segment.SQLSegment;
import com.dc.parser.model.segment.ddl.AlterDefinitionSegment;
import com.dc.parser.model.value.identifier.IdentifierValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

/**
 * Drop primary key segment.
 */
@RequiredArgsConstructor
@Getter
public final class DropForeignKeySegment implements AlterDefinitionSegment {
    
    private final int startIndex;
    
    private final int stopIndex;

    private final IdentifierValue foreignKeyName;
}
