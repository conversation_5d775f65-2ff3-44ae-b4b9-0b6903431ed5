package com.dc.parser.model.context.segment.select.projection;

import com.dc.parser.model.value.identifier.IdentifierValue;

import java.util.Optional;

/**
 * Projection interface.
 */
public interface Projection {

    /**
     * Get column name.
     *
     * @return column name
     */
    String getColumnName();

    /**
     * Get column label.
     *
     * @return column label
     */
    String getColumnLabel();

    /**
     * Get expression.
     *
     * @return expression
     */
    String getExpression();

    /**
     * Get alias.
     *
     * @return alias
     */
    Optional<IdentifierValue> getAlias();
}
