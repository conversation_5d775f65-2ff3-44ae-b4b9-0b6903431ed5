package com.dc.parser.model.context.statement.dml;

import com.dc.parser.model.context.aware.ParameterAware;
import com.dc.parser.model.context.segment.select.groupby.GroupByContext;
import com.dc.parser.model.context.segment.select.groupby.engine.GroupByContextEngine;
import com.dc.parser.model.context.segment.select.orderby.OrderByContext;
import com.dc.parser.model.context.segment.select.orderby.OrderByItem;
import com.dc.parser.model.context.segment.select.orderby.engine.OrderByContextEngine;
import com.dc.parser.model.context.segment.select.pagination.PaginationContext;
import com.dc.parser.model.context.segment.select.pagination.engine.PaginationContextEngine;
import com.dc.parser.model.context.segment.select.projection.Projection;
import com.dc.parser.model.context.segment.select.projection.ProjectionsContext;
import com.dc.parser.model.context.segment.select.projection.engine.ProjectionsContextEngine;
import com.dc.parser.model.context.segment.select.projection.impl.*;
import com.dc.parser.model.context.segment.table.TablesContext;
import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.context.type.TableAvailable;
import com.dc.parser.model.context.type.WhereAvailable;
import com.dc.parser.model.context.type.WithAvailable;
import com.dc.parser.model.enums.ParameterMarkerType;
import com.dc.parser.model.enums.SubqueryType;
import com.dc.parser.model.extractor.*;
import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.segment.dml.expr.BinaryOperationExpression;
import com.dc.parser.model.segment.dml.expr.ExpressionSegment;
import com.dc.parser.model.segment.dml.expr.simple.ParameterMarkerExpressionSegment;
import com.dc.parser.model.segment.dml.expr.subquery.SubquerySegment;
import com.dc.parser.model.segment.dml.order.item.*;
import com.dc.parser.model.segment.dml.predicate.WhereSegment;
import com.dc.parser.model.segment.generic.WithSegment;
import com.dc.parser.model.segment.generic.table.JoinTableSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.segment.generic.table.SubqueryTableSegment;
import com.dc.parser.model.segment.generic.table.TableSegment;
import com.dc.parser.model.statement.dml.SelectStatement;
import com.dc.parser.model.util.SQLUtils;
import com.dc.parser.model.value.identifier.IdentifierValue;
import com.google.common.base.Preconditions;
import lombok.Getter;
import lombok.Setter;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Select SQL statement context.
 */
@Getter
@Setter
public final class SelectStatementContext extends CommonSQLStatementContext implements TableAvailable, WhereAvailable, ParameterAware, WithAvailable {

    private final TablesContext tablesContext;

    private final ProjectionsContext projectionsContext;

    private final GroupByContext groupByContext;

    private final OrderByContext orderByContext;

    private final Map<Integer, SelectStatementContext> subqueryContexts;

    private final Collection<WhereSegment> whereSegments = new LinkedList<>();

    private final Collection<ColumnSegment> columnSegments = new LinkedList<>();

    private final Collection<BinaryOperationExpression> joinConditions = new LinkedList<>();

    private SubqueryType subqueryType;

    private boolean needAggregateRewrite;

    private PaginationContext paginationContext;

    public SelectStatementContext(final List<Object> params, final SelectStatement sqlStatement,
                                  final String currentDatabaseName, final Collection<TableSegment> inheritedTables) {
        super(sqlStatement);
        extractWhereSegments(whereSegments, sqlStatement);
        ColumnExtractor.extractColumnSegments(columnSegments, whereSegments);
        Collection<TableSegment> tableSegments = getAllTableSegments(inheritedTables);
        ExpressionExtractor.extractJoinConditions(joinConditions, whereSegments);
        subqueryContexts = createSubqueryContexts(params, currentDatabaseName, tableSegments);
        tablesContext = new TablesContext(tableSegments, subqueryContexts);
        groupByContext = new GroupByContextEngine().createGroupByContext(sqlStatement);
        orderByContext = new OrderByContextEngine().createOrderBy(sqlStatement, groupByContext);
        projectionsContext = new ProjectionsContextEngine(getDatabaseType()).createProjectionsContext(getSqlStatement().getProjections(), groupByContext, orderByContext);
        paginationContext = new PaginationContextEngine(getDatabaseType()).createPaginationContext(sqlStatement, projectionsContext, params, whereSegments);
    }

    private void extractWhereSegments(final Collection<WhereSegment> whereSegments, final SelectStatement selectStatement) {
        selectStatement.getWhere().ifPresent(whereSegments::add);
        whereSegments.addAll(WhereExtractor.extractSubqueryWhereSegments(selectStatement));
        whereSegments.addAll(WhereExtractor.extractJoinWhereSegments(selectStatement));
    }

    private Collection<TableSegment> getAllTableSegments(final Collection<TableSegment> inheritedTables) {
        TableExtractor tableExtractor = new TableExtractor();
        appendInheritedSimpleTables(inheritedTables, tableExtractor);
        tableExtractor.extractTablesFromSelect(getSqlStatement());
        Collection<TableSegment> result = new LinkedList<>(tableExtractor.getRewriteTables());
        for (TableSegment each : tableExtractor.getTableContext()) {
            if (each instanceof SubqueryTableSegment) {
                result.add(each);
            }
        }
        return result;
    }

    private void appendInheritedSimpleTables(final Collection<TableSegment> inheritedTables, final TableExtractor tableExtractor) {
        for (TableSegment each : inheritedTables) {
            if (each instanceof SimpleTableSegment) {
                tableExtractor.getTableContext().add(each);
            }
        }
    }

    private Map<Integer, SelectStatementContext> createSubqueryContexts(final List<Object> params, final String currentDatabaseName,
                                                                        final Collection<TableSegment> tableSegments) {
        Collection<SubquerySegment> subquerySegments = SubqueryExtractor.getSubquerySegments(getSqlStatement(), false);
        Map<Integer, SelectStatementContext> result = new HashMap<>(subquerySegments.size(), 1F);
        for (SubquerySegment each : subquerySegments) {
            SelectStatementContext subqueryContext = new SelectStatementContext(params, each.getSelect(), currentDatabaseName, tableSegments);
            subqueryContext.setSubqueryType(each.getSubqueryType());
            result.put(each.getStartIndex(), subqueryContext);
        }
        return result;
    }

    /**
     * Judge whether contains join query or not.
     *
     * @return whether contains join query or not
     */
    public boolean isContainsJoinQuery() {
        return getSqlStatement().getFrom().isPresent() && getSqlStatement().getFrom().get() instanceof JoinTableSegment;
    }

    /**
     * Judge whether contains subquery or not.
     *
     * @return whether contains subquery or not
     */
    public boolean isContainsSubquery() {
        return !subqueryContexts.isEmpty();
    }

    /**
     * Judge whether contains having or not.
     *
     * @return whether contains having or not
     */
    public boolean isContainsHaving() {
        return getSqlStatement().getHaving().isPresent();
    }

    /**
     * Judge whether contains combine or not.
     *
     * @return whether contains combine or not
     */
    public boolean isContainsCombine() {
        return getSqlStatement().getCombine().isPresent();
    }

    /**
     * Judge whether contains dollar parameter marker or not.
     *
     * @return whether contains dollar parameter marker or not
     */
    public boolean isContainsDollarParameterMarker() {
        for (Projection each : projectionsContext.getProjections()) {
            if (each instanceof ParameterMarkerProjection && ParameterMarkerType.DOLLAR == ((ParameterMarkerProjection) each).getParameterMarkerType()) {
                return true;
            }
        }
        for (ParameterMarkerExpressionSegment each : getParameterMarkerExpressions()) {
            if (ParameterMarkerType.DOLLAR == each.getParameterMarkerType()) {
                return true;
            }
        }
        return false;
    }

    private Collection<ParameterMarkerExpressionSegment> getParameterMarkerExpressions() {
        Collection<ExpressionSegment> expressions = new LinkedList<>();
        for (WhereSegment each : whereSegments) {
            expressions.add(each.getExpr());
        }
        return ExpressionExtractor.getParameterMarkerExpressions(expressions);
    }

    /**
     * Judge whether contains partial distinct aggregation.
     *
     * @return whether contains partial distinct aggregation
     */
    public boolean isContainsPartialDistinctAggregation() {
        Collection<Projection> aggregationProjections = projectionsContext.getProjections().stream().filter(AggregationProjection.class::isInstance).collect(Collectors.toList());
        Collection<AggregationDistinctProjection> aggregationDistinctProjections = projectionsContext.getAggregationDistinctProjections();
        return aggregationProjections.size() > 1 && !aggregationDistinctProjections.isEmpty() && aggregationProjections.size() != aggregationDistinctProjections.size();
    }

    /**
     * Set indexes.
     *
     * @param columnLabelIndexMap map for column label and index
     */
    public void setIndexes(final Map<String, Integer> columnLabelIndexMap) {
        setIndexForAggregationProjection(columnLabelIndexMap);
        setIndexForOrderItem(columnLabelIndexMap, orderByContext.getItems());
        setIndexForOrderItem(columnLabelIndexMap, groupByContext.getItems());
    }

    private void setIndexForAggregationProjection(final Map<String, Integer> columnLabelIndexMap) {
        for (AggregationProjection each : projectionsContext.getAggregationProjections()) {
            String columnLabel = SQLUtils.getExactlyValue(each.getAlias().map(IdentifierValue::getValue).orElse(each.getColumnName()));
            Preconditions.checkState(columnLabelIndexMap.containsKey(columnLabel), "Can't find index: %s, please add alias for aggregate selections", each);
            each.setIndex(columnLabelIndexMap.get(columnLabel));
            for (AggregationProjection derived : each.getDerivedAggregationProjections()) {
                String derivedColumnLabel = SQLUtils.getExactlyValue(derived.getAlias().map(IdentifierValue::getValue).orElse(each.getColumnName()));
                Preconditions.checkState(columnLabelIndexMap.containsKey(derivedColumnLabel), "Can't find index: %s", derived);
                derived.setIndex(columnLabelIndexMap.get(derivedColumnLabel));
            }
        }
    }

    private void setIndexForOrderItem(final Map<String, Integer> columnLabelIndexMap, final Collection<OrderByItem> orderByItems) {
        for (OrderByItem each : orderByItems) {
            if (each.getSegment() instanceof IndexOrderByItemSegment) {
                each.setIndex(((IndexOrderByItemSegment) each.getSegment()).getColumnIndex());
                continue;
            }
            if (each.getSegment() instanceof ColumnOrderByItemSegment && ((ColumnOrderByItemSegment) each.getSegment()).getColumn().getOwner().isPresent()) {
                Optional<Integer> itemIndex = projectionsContext.findProjectionIndex(((ColumnOrderByItemSegment) each.getSegment()).getText());
                if (itemIndex.isPresent()) {
                    each.setIndex(itemIndex.get());
                    continue;
                }
            }
            String columnLabel = getAlias(each.getSegment()).orElseGet(() -> getOrderItemText((TextOrderByItemSegment) each.getSegment()));
            Preconditions.checkState(columnLabelIndexMap.containsKey(columnLabel), "Can't find index: %s", each);
            if (columnLabelIndexMap.containsKey(columnLabel)) {
                each.setIndex(columnLabelIndexMap.get(columnLabel));
            }
        }
    }

    private Optional<String> getAlias(final OrderByItemSegment orderByItem) {
        if (projectionsContext.isUnqualifiedShorthandProjection()) {
            return Optional.empty();
        }
        String rawName = SQLUtils.getExactlyValue(((TextOrderByItemSegment) orderByItem).getText());
        for (Projection each : projectionsContext.getProjections()) {
            Optional<String> result = each.getAlias().map(IdentifierValue::getValue);
            if (SQLUtils.getExactlyExpression(rawName).equalsIgnoreCase(SQLUtils.getExactlyExpression(SQLUtils.getExactlyValue(each.getExpression())))) {
                return result;
            }
            if (rawName.equalsIgnoreCase(result.orElse(null))) {
                return Optional.of(rawName);
            }
            if (isSameColumnName(each, rawName)) {
                return result;
            }
        }
        return Optional.empty();
    }

    private boolean isSameColumnName(final Projection projection, final String name) {
        return projection instanceof ColumnProjection && name.equalsIgnoreCase(((ColumnProjection) projection).getName().getValue());
    }

    private String getOrderItemText(final TextOrderByItemSegment orderByItemSegment) {
        if (orderByItemSegment instanceof ColumnOrderByItemSegment) {
            return SQLUtils.getExactlyValue(((ColumnOrderByItemSegment) orderByItemSegment).getColumn().getIdentifier().getValue());
        }
        return SQLUtils.getExactlyValue(((ExpressionOrderByItemSegment) orderByItemSegment).getExpression());
    }

    /**
     * Judge group by and order by sequence is same or not.
     *
     * @return group by and order by sequence is same or not
     */
    public boolean isSameGroupByAndOrderByItems() {
        return !groupByContext.getItems().isEmpty() && groupByContext.getItems().equals(orderByContext.getItems());
    }

    /**
     * Find column projection.
     *
     * @param columnIndex column index
     * @return find column projection
     */
    public Optional<ColumnProjection> findColumnProjection(final int columnIndex) {
        List<Projection> expandProjections = projectionsContext.getExpandProjections();
        if (expandProjections.size() < columnIndex) {
            return Optional.empty();
        }
        Projection projection = expandProjections.get(columnIndex - 1);
        if (projection instanceof ColumnProjection) {
            return Optional.of((ColumnProjection) projection);
        }
        if (projection instanceof SubqueryProjection && ((SubqueryProjection) projection).getProjection() instanceof ColumnProjection) {
            return Optional.of((ColumnProjection) ((SubqueryProjection) projection).getProjection());
        }
        return Optional.empty();
    }

    /**
     * Judge whether sql statement contains table subquery segment or not.
     *
     * @return whether sql statement contains table subquery segment or not
     */
    public boolean containsTableSubquery() {
        return getSqlStatement().getFrom().isPresent() && getSqlStatement().getFrom().get() instanceof SubqueryTableSegment || getSqlStatement().getWithSegment().isPresent();
    }

    @Override
    public SelectStatement getSqlStatement() {
        return (SelectStatement) super.getSqlStatement();
    }

    @Override
    public Collection<WhereSegment> getWhereSegments() {
        return whereSegments;
    }

    @Override
    public Collection<ColumnSegment> getColumnSegments() {
        return columnSegments;
    }

    @Override
    public Collection<BinaryOperationExpression> getJoinConditions() {
        return joinConditions;
    }

    @Override
    public void setUpParameters(final List<Object> params) {
        paginationContext = new PaginationContextEngine(getDatabaseType()).createPaginationContext(getSqlStatement(), projectionsContext, params, whereSegments);
    }

    @Override
    public Optional<WithSegment> getWith() {
        return getSqlStatement().getWithSegment();
    }
}
