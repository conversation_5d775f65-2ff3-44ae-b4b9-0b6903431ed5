package com.dc.parser.model.statement.dal;

import com.dc.parser.model.segment.dml.expr.FunctionSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;
import lombok.Setter;

/**
 * Doris show procedure code statement.
 */
@Getter
@Setter
public abstract class ShowProcedureCodeStatement extends AbstractSQLStatement implements DALStatement {

    private FunctionSegment function;
}
