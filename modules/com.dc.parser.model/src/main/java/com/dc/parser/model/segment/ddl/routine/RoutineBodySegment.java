
package com.dc.parser.model.segment.ddl.routine;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import com.dc.parser.model.segment.SQLSegment;

import java.util.Collection;
import java.util.LinkedList;

/**
 * Routine body segment.
 */
@RequiredArgsConstructor
@Getter
public class RoutineBodySegment implements SQLSegment {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    private final Collection<ValidStatementSegment> validStatements = new LinkedList<>();
}
