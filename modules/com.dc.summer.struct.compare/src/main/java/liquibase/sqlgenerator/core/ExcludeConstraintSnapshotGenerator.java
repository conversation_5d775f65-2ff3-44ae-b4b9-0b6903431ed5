package liquibase.sqlgenerator.core;

import liquibase.Scope;
import liquibase.database.Database;
import liquibase.database.core.*;
import liquibase.exception.DatabaseException;
import liquibase.executor.ExecutorService;
import liquibase.snapshot.*;
import liquibase.snapshot.jvm.JdbcSnapshotGenerator;
import liquibase.statement.core.RawSqlStatement;
import liquibase.structure.DatabaseObject;
import liquibase.structure.core.*;
import liquibase.util.JdbcUtil;
import liquibase.util.StringUtil;

import java.sql.Array;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class ExcludeConstraintSnapshotGenerator extends JdbcSnapshotGenerator {

    public ExcludeConstraintSnapshotGenerator() {
        super(ExcludeConstraint.class, new Class[]{Table.class});
    }

    @Override
    protected DatabaseObject snapshotObject(DatabaseObject example, DatabaseSnapshot snapshot) throws DatabaseException {
        Database database = snapshot.getDatabase();
        Table table = ((ExcludeConstraint)example).getTable();
        String sql = this.getSnapshotObjectSql(example, database, table);
        if (sql == null) {
            return null;
        }
        List<Map<String, ?>> list = (Scope.getCurrentScope().getSingleton(ExecutorService.class)).getExecutor("jdbc", snapshot.getDatabase()).queryForList(new RawSqlStatement(sql));

        if (list.isEmpty()) {
            return null;
        } else {
            try {
                JdbcUtil.requiredSingleResult(list);
            } catch (DatabaseException e) {
                Scope.getCurrentScope().getLog(this.getClass()).severe("Error snapshotting " + example + " with result set of " + sql, e);
                throw e;
            }

            Map<String, ?> map = list.get(0);
            return this.mapBaseResultSetToCheckConstraint(database, map, table);
        }
    }

    protected String getSnapshotObjectSql(DatabaseObject example, Database database, Table table) {
        String sql = "";
        if (database instanceof PostgresDatabase) {
            sql = String.format("SELECT\n" +
                    "    c.conname AS CONSTRAINT_NAME,\n" +
                    "      replace(\n" +
                    "    CAST(conrelid::regclass as text),\n" +
                    "    n.nspname || '.',\n" +
                    "    ''\n" +
                    "  ) AS TABLE_NAME,\n" +
                    "    ARRAY_AGG(a.attname) AS COLUMN_NAMES,\n" +
                    "    replace(pg_get_constraintdef(c.oid), 'EXCLUDE ', '') as SEARCH_CONDITION\n" +
                    "\n" +
                    "FROM\n" +
                    "    pg_constraint c\n" +
                    "JOIN\n" +
                    "    unnest(c.conkey) AS key(colnum) ON TRUE\n" +
                    "JOIN\n" +
                    "    pg_attribute a ON a.attnum = key.colnum\n" +
                    "    AND a.attrelid = c.conrelid\n" +
                    "JOIN pg_namespace n ON n.oid = c.connamespace\n" +
                    "\n" +
                    "WHERE\n" +
                    "  c.contype IN ('x')\n" +
                    "  AND c.conname = '%s'\n" +
                    "  AND c.conrelid = '%s'::regclass\n" +
                    "  AND n.nspname = '%s'\n" +
                    "GROUP BY\n" +
                    "    c.conname, conrelid, c.oid, n.nspname\n" +
                    "ORDER BY\n" +
                    "  c.conrelid::regclass::text", example.getName(), table.getName(), example.getSchema().getName());
        }
        return sql;
    }

    private ExcludeConstraint mapBaseResultSetToCheckConstraint(Database database, Map<String, ?> map, Table table) throws DatabaseException {

        ExcludeConstraint excludeConstraint = new ExcludeConstraint();
        excludeConstraint.setName((String)map.get("CONSTRAINT_NAME"));
        excludeConstraint.setBody(StringUtil.trimToNull((String)map.get("SEARCH_CONDITION")));
        excludeConstraint.setTable(table);

        try {
            String[] columnNames = (String[]) ((Array) map.get("COLUMN_NAMES")).getArray();
            for (int i = 0; i < columnNames.length; i++) {

                String columnName = columnNames[i];
                Column column = new Column(columnName)
                        .setRelation(table);
                excludeConstraint.addColumn(i, column);
            }
        } catch (SQLException e) {
            Scope.getCurrentScope().getLog(this.getClass()).severe("Error get columnNames for pg.", e);
            throw new DatabaseException(e);
        }

        excludeConstraint.setBackingIndex(new Index().setRelation(table).setColumns(excludeConstraint.getColumns()));

        return excludeConstraint;
    }

    @Override
    protected void addTo(DatabaseObject foundObject, DatabaseSnapshot snapshot) throws DatabaseException {
        if (!snapshot.getSnapshotControl().shouldInclude(ExcludeConstraint.class)) {
            return;
        }

        if (foundObject instanceof Table) {
            Table table = (Table) foundObject;
            Database database = snapshot.getDatabase();
            Schema schema = table.getSchema();

            String sql = this.getAddToSql(database, table);
            if (sql == null) {
                return;
            }

            for (Map<String, ?> map : Scope.getCurrentScope()
                    .getSingleton(ExecutorService.class)
                    .getExecutor("jdbc", snapshot.getDatabase())
                    .queryForList(new RawSqlStatement(sql))) {

                ExcludeConstraint excludeConstraint = new ExcludeConstraint()
                        .setName(this.cleanNameFromDatabase((String) map.get("CONSTRAINT_NAME"), database))
                        .setTable(table)
                        .setBody((String) map.get("SEARCH_CONDITION"));
                List<ExcludeConstraint> excludeConstraints = table.getAttribute("excludeConstraints", List.class);
                if (this.isSystemConstraint(excludeConstraint)) {
                    if (excludeConstraints == null) {
                        excludeConstraints = new ArrayList<>();
                        table.setAttribute("excludeConstraints", excludeConstraints);
                    }

                    excludeConstraints.add(excludeConstraint);
                }
            }


            List<ExcludeConstraint> excludeConstraints = table.getAttribute("excludeConstraints", List.class);
            if (excludeConstraints != null) {
                this.sortCheckConstraints(excludeConstraints);
            }

        }
    }

    protected String getAddToSql(Database var1, Table var2) {
        String sql = null;
        if (var1 instanceof PostgresDatabase) {
            sql = String.format("SELECT conname as CONSTRAINT_NAME FROM   pg_constraint c  JOIN   pg_namespace n ON n.oid = c.connamespace   WHERE  contype IN ('x')  AND conrelid = '%s.\"%s\"'::regclass   AND    n.nspname = '%s'   ORDER  BY conrelid::regclass::text, contype DESC", var2.getSchema().getName(), var2.getName(), var2.getSchema().getName());
        }
        return sql;
    }

    private void sortCheckConstraints(List<ExcludeConstraint> var1) {
        var1.sort(ExcludeConstraint::compareTo);
    }

    private boolean isSystemConstraint(ExcludeConstraint excludeConstraint) {
        String body = excludeConstraint.getBody();
        if (body == null) {
            return true;
        } else {
            return !body.matches("\"?\\w+\" IS NOT NULL");
        }
    }

}
