package com.dc.summer.struct.compare.impl.liquibase;

import liquibase.change.Change;
import liquibase.change.core.AddColumnChange;
import liquibase.change.core.CreateTableChange;
import liquibase.change.core.DropTableChange;
import liquibase.change.core.ModifyDataTypeChange;

public class LBChangeProcessor {
   private static LBChangeProcessor instance = new LBChangeProcessor();

   public static LBChangeProcessor getInstance() {
      return instance;
   }

   private LBChangeProcessor() {
   }

   public boolean isChangeAttend(Change change) {
      return change instanceof CreateTableChange || change instanceof AddColumnChange || change instanceof DropTableChange || change instanceof ModifyDataTypeChange;
   }

   public String generateDetail(Change change) {
      StringBuilder sb = new StringBuilder(change.getConfirmationMessage());
      return sb.toString();
   }
}
