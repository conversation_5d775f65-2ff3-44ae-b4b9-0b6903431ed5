package com.dc.summer.parser.utils;

import com.dc.node.ObjectName;
import com.dc.parser.clickhouse.ast.InsertQuery;
import com.dc.parser.clickhouse.ast.SelectStatement;
import com.dc.parser.clickhouse.ast.SelectUnionQuery;
import com.dc.parser.clickhouse.util.getCKTablesUtil;
import com.dc.parser.clickhouse.visitor.BaseSqlBuilder;
import com.dc.sqlparser.types.EDbObjectType;
import com.dc.sqlparser.types.EExpressionType;
import com.dc.sqlparser.types.ETableSource;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.ColumnData;
import com.dc.summer.parser.sql.model.SqlActionModel;
import com.dc.summer.parser.utils.model.SqlParseModel;
import com.dc.type.DatabaseType;
import com.dc.stmt.CustomSqlStatement;
import com.dc.sqlparser.DCustomSqlStatement;
import com.dc.sqlparser.DStatementList;
import com.dc.sqlparser.nodes.*;
import com.dc.sqlparser.stmt.*;
import com.dc.sqlparser.stmt.hive.THiveExportTable;
import com.dc.sqlparser.stmt.hive.THiveFromQuery;
import com.dc.sqlparser.stmt.hive.THiveImportTable;
import com.dc.sqlparser.stmt.hive.THiveLoad;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


public class CrudUtil {

    private static final Logger logger = LoggerFactory.getLogger(CrudUtil.class);

    public static SqlActionModel getObject(SqlParseModel sqlParserModel, Integer dbType) {
        SqlActionModel sqlActionModel = new SqlActionModel();

        Object ast = sqlParserModel.getAst();
        DCustomSqlStatement tCustomSqlStatement = ast != null ? null : sqlParserModel.gettCustomSqlStatement();
        CustomSqlStatement customSqlStatement = sqlParserModel.getCustomSqlStatement();

        GetTablesUtil getTables = new GetTablesUtil();
        getTables.setSqlParseModel(sqlParserModel);
        GetColumnsUtil getColumns = new GetColumnsUtil();
        getColumns.setDbType(dbType);
        getCKTablesUtil getCKTablesUtil = new getCKTablesUtil();

        Set<String> tables = new HashSet<>();
        Set<String> tablesInWhereClause = new HashSet<>();
        Set<String> functions = new HashSet<>();
        Set<String> valuesSelectTables;

        try {
            if (tCustomSqlStatement instanceof TInsertSqlStatement) {
                TInsertSqlStatement tInsertSqlStatement = (TInsertSqlStatement) tCustomSqlStatement;

                if (tInsertSqlStatement.isInsertAll() || tInsertSqlStatement.isInsertFirst()) {
                    if (tInsertSqlStatement.getSubQuery() != null) {
                        Set<String> insertAllFirstTables = getTables.analyzeSelectStatement(tInsertSqlStatement.getSubQuery());
                        sqlActionModel.setInsertAllFirst(true);
                        sqlActionModel.setInsertAllFirstTables(insertAllFirstTables);
                    }
                } else if (tInsertSqlStatement.getSubQuery() != null && tInsertSqlStatement.getInsertSource() != null
                        && "subquery".equalsIgnoreCase(tInsertSqlStatement.getInsertSource().toString())
                        && tInsertSqlStatement.getTargetTable() != null && tInsertSqlStatement.getTargetTable().getEffectType() != null
                        && "tetInsert".equalsIgnoreCase(tInsertSqlStatement.getTargetTable().getEffectType().toString())) {
                    sqlActionModel.setObjectName(tInsertSqlStatement.getTargetTable().getFullName());
                    sqlActionModel.setInsertIntoSelect(true);
                }

                tables = getTables.analyzeInsertStatement(tInsertSqlStatement);
                valuesSelectTables = new HashSet<>(getTables.getValuesSelectTables());
                sqlActionModel.setValuesSelectTables(valuesSelectTables);

                if (sqlActionModel.getObjectName() != null) {
                    tables.removeIf(str -> str.equalsIgnoreCase(sqlActionModel.getObjectName()));
                    tables.addAll(getTables.getInsertSelectTables(tInsertSqlStatement)); // insert into tab1 ... select tab1
                } else if (sqlActionModel.isInsertAllFirst()) {
                    tables.removeAll(sqlActionModel.getInsertAllFirstTables());
                    tables.addAll(getTables.getInsertIntoValuesTables(tInsertSqlStatement)); // INSERT ALL INTO tbl1 VALUES(1,1) INTO tbl2 VALUES(2,2) SELECT * FROM tbl1 WHERE ROWNUM< 2;
                }

                if (tInsertSqlStatement.getOnDuplicateKeyUpdate() != null && tInsertSqlStatement.getOnDuplicateKeyUpdate().size() > 0) {
                    sqlActionModel.setDuplicateKeyUpdate(true);
                    sqlActionModel.setInsertAllFirstTables(tables);
                }

                if (tInsertSqlStatement.getMultiInsertStatements() != null) {
                    Set<String> multiInsertTables = new HashSet<>();
                    ArrayList<TInsertSqlStatement> multiInsertStatements = tInsertSqlStatement.getMultiInsertStatements();
                    for (TInsertSqlStatement insertSqlStatement : multiInsertStatements) {
                        if (insertSqlStatement.getTargetTable() != null && insertSqlStatement.getTargetTable().isBaseTable()) {
                            multiInsertTables.add(insertSqlStatement.getTargetTable().getFullName());
                        }
                    }
                    sqlActionModel.setMultiInsertTables(multiInsertTables);
                }

                if (DatabaseType.CLICKHOUSE.getValue().equals(dbType)) {
                    TTable targetTable = tInsertSqlStatement.getTargetTable();
                    if (targetTable != null && targetTable.getAliasClause() != null) {
                        TAliasClause aliasClause = targetTable.getAliasClause();
                        TObjectName aliasName = aliasClause.getAliasName();
                        String fullName = targetTable.getFullName();
                        if (aliasName != null && "table".equalsIgnoreCase(fullName)) {
                            if (tables.contains(fullName)) {
                                tables.remove(fullName);
                                tables.add(aliasName.toString());
                            }
                            if (sqlActionModel.getObjectName() != null && "table".equalsIgnoreCase(sqlActionModel.getObjectName())) {
                                sqlActionModel.setObjectName(aliasName.toString());
                            }
                        }
                    }
                }

                functions = getTables.getFunctions();

                sqlActionModel.setTables(tables);
                sqlActionModel.setFunctions(functions);
                sqlActionModel.setStatisticsFuncMap(getTables.statisticsFuncMap);
            } else if (tCustomSqlStatement instanceof TUpdateSqlStatement) {
                TUpdateSqlStatement tUpdateSqlStatement = (TUpdateSqlStatement) tCustomSqlStatement;

                //私有表需求需要。
                TResultColumnList tResultColumnList = tUpdateSqlStatement.getResultColumnList();
                if (tResultColumnList != null && tResultColumnList.size() > 0){
                    for (TResultColumn resultColumn : tUpdateSqlStatement.getResultColumnList()) {
                        TExpression expr = resultColumn.getExpr();
                        if (expr.getRightOperand().getExpressionType().equals(EExpressionType.subquery_t)) {
                            sqlActionModel.setUpdateSelect(true);
                        }
                    }
                }

                tables = getTables.analyzeUpdateStatement(tUpdateSqlStatement);
                functions = getTables.getFunctions();
                tablesInWhereClause = getTables.getReturnTablesInWhereClause();

                sqlActionModel.setTables(tables);
                sqlActionModel.setFunctions(functions);
                sqlActionModel.setStatisticsFuncMap(getTables.statisticsFuncMap);
                sqlActionModel.setTablesInWhereClause(tablesInWhereClause);
            } else if (tCustomSqlStatement instanceof TDeleteSqlStatement) {
                TDeleteSqlStatement tDeleteSqlStatement = (TDeleteSqlStatement) tCustomSqlStatement;

                tables = getTables.analyzeDeleteStatement(tDeleteSqlStatement);
                functions = getTables.getFunctions();
                tablesInWhereClause = getTables.getReturnTablesInWhereClause();

                sqlActionModel.setTables(tables);
                sqlActionModel.setFunctions(functions);
                sqlActionModel.setStatisticsFuncMap(getTables.statisticsFuncMap);
                sqlActionModel.setTablesInWhereClause(tablesInWhereClause);
            } else if (tCustomSqlStatement instanceof TMergeSqlStatement) {
                TMergeSqlStatement tMergeSqlStatement = (TMergeSqlStatement) tCustomSqlStatement;

                if (tMergeSqlStatement.getTargetTable() != null) {
                    String targetTable = tMergeSqlStatement.getTargetTable().getFullName();
                    if (StringUtils.isNotEmpty(targetTable)) {
                        sqlActionModel.setObjectName(targetTable);
                        sqlActionModel.setMerge(true);
                    }

                    TSelectSqlStatement subquery = tMergeSqlStatement.getTargetTable().subquery;
                    if (subquery != null) {
                        TTable table = subquery.getTables().getTable(0);
                        targetTable = table.getFullName();
                        if (StringUtils.isNotEmpty(targetTable)) {
                            sqlActionModel.setObjectName(targetTable);
                            sqlActionModel.setMerge(true);
                        }
                    }
                    //构造merge-when子句的动作和对象名的关系。封装一个方法即可，构造 String -> Set<String>即可
                    MergeResult mergeResult = produceMergeWhenClauseOperationMap(tMergeSqlStatement, null);
                    sqlParserModel.setMergeResult(mergeResult);
                }

                tables = getTables.analyzeMergeStatement(tMergeSqlStatement);
                functions = getTables.getFunctions();

                if (tMergeSqlStatement.getCteList() != null && tMergeSqlStatement.getCteList().size() > 0) {
                    for (int i = 0; i < tMergeSqlStatement.getCteList().size(); i++) {
                        TCTE cte = tMergeSqlStatement.getCteList().getCTE(i);
                        if (cte.getSubquery() != null) {
                            Set<String> tablesInCTE = getTables.analyzeSelectStatement(cte.getSubquery());
                            tables.addAll(tablesInCTE);
                            functions.addAll(getTables.getFunctions());
                        }
                    }
                }

                sqlActionModel.setTables(tables);
                sqlActionModel.setFunctions(functions);
                sqlActionModel.setStatisticsFuncMap(getTables.statisticsFuncMap);
            } else if (tCustomSqlStatement instanceof THiveFromQuery) {
                THiveFromQuery tHiveFromQuery = (THiveFromQuery) tCustomSqlStatement;

                DStatementList statements = tHiveFromQuery.getStatements() != null && tHiveFromQuery.getStatements().size() > 0 ? tHiveFromQuery.getStatements()
                        : tHiveFromQuery.getHiveBodyList() != null && tHiveFromQuery.getHiveBodyList().size() > 0 ? tHiveFromQuery.getHiveBodyList()
                        : new DStatementList();

                for (DCustomSqlStatement statement : statements) {
                    if (statement instanceof TInsertSqlStatement) {
                        tables.addAll(getTables.analyzeInsertStatement((TInsertSqlStatement) statement));
                    }
                }
                sqlActionModel.setTables(tables);

                functions = getTables.getFunctions();
                sqlActionModel.setFunctions(functions);
                sqlActionModel.setStatisticsFuncMap(getTables.statisticsFuncMap);

                if (tHiveFromQuery.tables != null) {
                    for (int i = 0; i < tHiveFromQuery.tables.size(); i++) {
                        TTable table = tHiveFromQuery.tables.getTable(i);
                        if (table.isBaseTable()) {
                            tablesInWhereClause.add(table.getFullName());
                        } else if (table.getSubquery() != null) {
                            tablesInWhereClause.addAll(getTables.analyzeSelectStatement(table.getSubquery()));
                        }
                    }
                }
                sqlActionModel.setTablesInWhereClause(tablesInWhereClause);
            } else if (tCustomSqlStatement instanceof TSelectSqlStatement) {
                TSelectSqlStatement tSelectSqlStatement = (TSelectSqlStatement) tCustomSqlStatement;

                // sqlserver: select * into student4 from lwc_test_student; commit; // 执行后不commit会锁库
                if (tSelectSqlStatement.getIntoClause() != null && tSelectSqlStatement.getIntoClause().getExprList() != null && tSelectSqlStatement.getTables() != null) {
                    TExpression expression = tSelectSqlStatement.getIntoClause().getExprList().getExpression(0);
                    if (expression != null && expression.getPlainText() != null) {
                        sqlActionModel.setSelectInto(true);
                        sqlActionModel.setObjectName(expression.getPlainText());
                    }

                    Set<String> selectIntoTables = new HashSet<>();
                    for (TTable table : tSelectSqlStatement.getTables()) {
                        if (table.getSubquery() != null) {
                            selectIntoTables.addAll(getTables.analyzeSelectStatement(table.getSubquery()));
                        } else {
                            if (!"tetSelectInto".equals(table.getEffectType().toString())) {
                                selectIntoTables.add(table.getFullName());
                            }
                        }
                    }

                    sqlActionModel.setTables(selectIntoTables);

                    if (sqlActionModel.isSelectInto()) {
                        Set<String> selectIntoFunctions = getColumns.getFunctions(tCustomSqlStatement);
                        sqlActionModel.setFunctions(selectIntoFunctions);
                        sqlActionModel.setStatisticsFuncMap(getColumns.statisticsFuncMap);
                        return sqlActionModel;
                    }
                } else if (tSelectSqlStatement.getIntoClause() != null && tSelectSqlStatement.getIntoClause().getIntoName() != null) {
                    String intoTable = tSelectSqlStatement.getIntoClause().getIntoName().toString();
                    sqlActionModel.setSelectInto(true);
                    sqlActionModel.setObjectName(intoTable);
                }

                try {
                    tables = getTables.analyzeSelectStatement(tSelectSqlStatement);
                    if (!getTables.getFunctions().isEmpty()) {
                        functions.addAll(getTables.getFunctions());
                    }

                    if (tables.size() == 0 && tSelectSqlStatement.getTables() != null && tSelectSqlStatement.getTables().size() > 0) {
                        TTable table = tSelectSqlStatement.getTables().getTable(0);
                        if (table.getSubquery() != null && table.getSubquery().getResultColumnList() != null && table.getSubquery().getResultColumnList().size() > 0) {
                            TResultColumn resultColumn = table.getSubquery().getResultColumnList().getResultColumn(0);
                            if (resultColumn.getExpr() != null && resultColumn.getExpr().getFunctionCall() != null && resultColumn.getExpr().getFunctionCall().getFunctionName() != null) {
                                String functionName = resultColumn.getExpr().getFunctionCall().getFunctionName().toString();
                                functions.add(functionName);
                                sqlActionModel.setSelectFunction(true);
                            }
                        } else if (table.getFuncCall() != null && table.getFuncCall().getFunctionName() != null) {
                            getFunctionName(table.getFuncCall(), functions, sqlActionModel);
                        } else if (table.getSubquery() != null && table.getSubquery().getValueClause() != null && table.getSubquery().getValueClause().getRows() != null) {
                            ArrayList<TResultColumnList> rows = table.getSubquery().getValueClause().getRows();
                            if (rows.size() > 0) {
                                TResultColumnList tResultColumns = rows.get(0);
                                if (tResultColumns != null && tResultColumns.size() > 0) {
                                    tables.add("(" + tResultColumns + ")");
                                    sqlActionModel.setSelectValue(true);
                                }
                            }
                        } else if (table.getTableExpr() != null && table.getTableExpr().getFunctionCall() != null && table.getTableExpr().getFunctionCall().getFunctionName() != null) {
                            getFunctionName(table.getTableExpr().getFunctionCall(), functions, sqlActionModel);
                        }
                    }

                    if (tables.size() == 0 && tSelectSqlStatement.getResultColumnList() != null) {
                        TResultColumnList resultColumnList = tSelectSqlStatement.getResultColumnList();
                        if (resultColumnList.size() > 0) {
                            TResultColumn resultColumn = resultColumnList.getResultColumn(0);
                            if (resultColumn.getExpr() != null && resultColumn.getExpr().getSequenceName() != null) {
                                String sequenceName = resultColumn.getExpr().getSequenceName().toString();
                                tables.add(sequenceName);
                                sqlActionModel.setSelectSequence(true);
                            } else if (resultColumn.getExpr() != null && resultColumn.getExpr().getFunctionCall() != null
                                    && resultColumn.getExpr().getFunctionCall().getFunctionName() != null) {

                                sqlActionModel.setSelectFunction(true);

                                List<String> funcArgs = new ArrayList<>();
                                sqlActionModel.setFuncArgs(funcArgs);
                                TExpressionList args = resultColumn.getExpr().getFunctionCall().getArgs();
                                if (args != null) {
                                    for (TExpression expression : args) {
                                        if (expression.getConstantOperand() != null) {
                                            String s = expression.getConstantOperand().toString();
                                            funcArgs.add(s);
                                        }
                                    }
                                }
                            } else if (resultColumn.getExpr() != null && resultColumn.getExpr().getOperatorToken() != null
                                    && SqlConstant.KEY_DOUBLE_COLON.equals(resultColumn.getExpr().getOperatorToken().toString())
                                    && resultColumn.getExpr().getTypeName() != null
                                    && Arrays.asList(SqlConstant.POST_GRE_SQL_TEXT_SEARCH_TYPES).contains(resultColumn.getExpr().getTypeName().toString().toUpperCase(Locale.ROOT))) {
                                String typeName = resultColumn.getExpr().getTypeName().toString();
                                tables.add(typeName);
                                sqlActionModel.setSelectValue(true);
                            }
                        }
                    } else if (tables.size() == 0 && tSelectSqlStatement.getValueClause() != null && tSelectSqlStatement.getValueClause().getRows() != null) {
                        ArrayList<TResultColumnList> rows = tSelectSqlStatement.getValueClause().getRows();

                        for (TResultColumnList each : rows) {
                            for (int i = 0; i < each.size(); i++) {
                                TResultColumn resultColumn = each.getResultColumn(i);
                                if (resultColumn.getExpr() != null) {
                                    TExpression expr = resultColumn.getExpr();
                                    switch (expr.getExpressionType()) {
                                        case function_t:
                                            functions.add(expr.getFunctionCall().getFunctionName().toString());
                                            break;
                                        case subquery_t:
                                            tables.addAll(getTables.analyzeSelectStatement(expr.getSubQuery()));
                                            functions.addAll(getTables.getFunctions());
                                            break;
                                    }
                                }
                            }
                        }
                        sqlActionModel.setSelectValue(true);

                        //之前处理sequenceName的逻辑
                        if (rows.size() > 0) {
                            TResultColumnList tResultColumns = rows.get(0);
                            if (tResultColumns != null && tResultColumns.size() > 0) {
                                if (tResultColumns.getResultColumn(0).getExpr() != null && tResultColumns.getResultColumn(0).getExpr().getConstantOperand() != null) {
                                    String sequenceName = getSequenceName(tCustomSqlStatement);
                                    if (StringUtils.isNotBlank(sequenceName)) {
                                        tables.add(sequenceName);
                                        sqlActionModel.setSelectSequence(true);
                                    } else if (tResultColumns.getResultColumn(0).getExpr().getExpressionType() == EExpressionType.subquery_t) {
                                        Set<String> variable = getTables.analyzeSelectStatement(tResultColumns.getResultColumn(0).getExpr().getSubQuery());
                                        tables.addAll(variable);
                                        sqlActionModel.setSelectValue(true);
                                    }
                                }
                            }
                        }
                    }

                    sqlActionModel.setTables(tables);
                    Object[] objects = getColumns.getObject(tCustomSqlStatement);
                    sqlActionModel.setColumns((Set<String>) objects[0]);
                    functions.addAll((Set<String>) objects[1]);
                    sqlActionModel.setFunctions(functions);
                    sqlActionModel.setStatisticsFuncMap(getColumns.statisticsFuncMap);
                    sqlActionModel.setAlias((Map<String, String>) objects[2]);
                    sqlActionModel.setAliasBk((Map<String, List<String>>) objects[3]);
                    sqlActionModel.setColumnDataList((List<ColumnData>) objects[4]);
                } catch (Exception e) {
                    logger.error("getTables error!", e);
                    tables.clear();
                }
            } else if (tCustomSqlStatement instanceof TExplainPlan) {
                TExplainPlan explain = (TExplainPlan) tCustomSqlStatement;

                if (explain.getStatement() instanceof TSelectSqlStatement) {
                    TSelectSqlStatement tSelectSqlStatement = (TSelectSqlStatement) explain.getStatement();
                    sqlActionModel.setExplainOperation(SqlConstant.KEY_SELECT);
                    tables = getTables.analyzeSelectStatement(tSelectSqlStatement);
                    sqlActionModel.setTables(tables);
                    Object[] objects = getColumns.getObject(tCustomSqlStatement);
                    sqlActionModel.setColumns((Set<String>) objects[0]);
                    sqlActionModel.setFunctions((Set<String>) objects[1]);
                    sqlActionModel.setStatisticsFuncMap(getColumns.statisticsFuncMap);
                    sqlActionModel.setAlias((Map<String, String>) objects[2]);
                } else if (explain.getStatement() instanceof TInsertSqlStatement) {
                    TInsertSqlStatement tInsertSqlStatement = (TInsertSqlStatement) explain.getStatement();
                    sqlActionModel.setExplainOperation(SqlConstant.KEY_INSERT);
                    tables = getTables.analyzeInsertStatement(tInsertSqlStatement);
                    functions = getTables.getFunctions();
                    sqlActionModel.setTables(tables);
                    sqlActionModel.setFunctions(functions);
                    sqlActionModel.setStatisticsFuncMap(getTables.statisticsFuncMap);
                } else if (explain.getStatement() instanceof TUpdateSqlStatement) {
                    TUpdateSqlStatement tUpdateSqlStatement = (TUpdateSqlStatement) explain.getStatement();
                    sqlActionModel.setExplainOperation(SqlConstant.KEY_UPDATE);
                    tables = getTables.analyzeUpdateStatement(tUpdateSqlStatement);
                    functions = getTables.getFunctions();
                    sqlActionModel.setTables(tables);
                    sqlActionModel.setFunctions(functions);
                    sqlActionModel.setStatisticsFuncMap(getTables.statisticsFuncMap);
                } else if (explain.getStatement() instanceof TDeleteSqlStatement) {
                    TDeleteSqlStatement tDeleteSqlStatement = (TDeleteSqlStatement) explain.getStatement();
                    sqlActionModel.setExplainOperation(SqlConstant.KEY_DELETE);
                    tables = getTables.analyzeDeleteStatement(tDeleteSqlStatement);
                    functions = getTables.getFunctions();
                    sqlActionModel.setTables(tables);
                    sqlActionModel.setFunctions(functions);
                    sqlActionModel.setStatisticsFuncMap(getTables.statisticsFuncMap);
                } else if (explain.getStatement() instanceof TMergeSqlStatement) {
                    TMergeSqlStatement tMergeSqlStatement = (TMergeSqlStatement) explain.getStatement();
                    sqlActionModel.setExplainOperation(SqlConstant.KEY_INSERT);
                    tables = getTables.analyzeMergeStatement(tMergeSqlStatement);
                    functions = getTables.getFunctions();
                    sqlActionModel.setTables(tables);
                    sqlActionModel.setFunctions(functions);
                    sqlActionModel.setStatisticsFuncMap(getTables.statisticsFuncMap);
                }
            } else if (ast instanceof SelectUnionQuery) {
                SelectUnionQuery selectUnionQuery = (SelectUnionQuery) ast;

                List<SelectStatement> statements = selectUnionQuery.getStatements();
                for (SelectStatement selectStatement : statements) {
                    getCKTablesUtil.analyzeSelectStatement(selectStatement);
                    tables.addAll(getCKTablesUtil.getReturnTables());
                    functions.addAll(getCKTablesUtil.getFunctions());
                }

                sqlActionModel.setTables(tables);
                sqlActionModel.setFunctions(functions);
            } else if (ast instanceof InsertQuery) {
                InsertQuery stmt = (InsertQuery) ast;
                getCKTablesUtil.analyzeInsertStatement(stmt);

                tables.addAll(getCKTablesUtil.getReturnTablesInWhereClause()); // insert into tab1 ... tab1放在了 tablesInWhereClause
                tablesInWhereClause.addAll(getCKTablesUtil.getReturnTables()); // insert into tab1 select tab2 ... tab2放在了 tables
                functions.addAll(getCKTablesUtil.getFunctions());

                if (stmt.getDataClause() != null && stmt.getDataClause().getSelectUnionQuery() != null) {
                    SelectUnionQuery selectUnionQuery = stmt.getDataClause().getSelectUnionQuery();
                    BaseSqlBuilder baseSqlBuilder = new BaseSqlBuilder();
                    String insertSelectSql = baseSqlBuilder.visitSelectUnionQuery(selectUnionQuery);
                    insertSelectSql = "select count() from ( " + insertSelectSql + " )";
                    sqlActionModel.setCkInsertSelectSql(insertSelectSql);
                }

                sqlActionModel.setTables(tables);
                sqlActionModel.setTablesInWhereClause(tablesInWhereClause);
                sqlActionModel.setFunctions(functions);
            } else if (tCustomSqlStatement instanceof THiveLoad) {
                THiveLoad tHiveLoad = (THiveLoad) tCustomSqlStatement;
                if (tHiveLoad.getTables() != null) {
                    for (TTable table : tHiveLoad.getTables()) {
                        tables.add(table.getTableName().toString());
                    }
                    sqlActionModel.setTables(tables);
                }
            } else if (tCustomSqlStatement instanceof THiveImportTable) {
                THiveImportTable tHiveImportTable = (THiveImportTable) tCustomSqlStatement;
                if (tHiveImportTable.getTables() != null) {
                    for (TTable table : tHiveImportTable.getTables()) {
                        tables.add(table.getTableName().toString());
                    }
                    sqlActionModel.setTables(tables);
                }
            } else if (tCustomSqlStatement instanceof THiveExportTable) {
                THiveExportTable tHiveExportTable = (THiveExportTable) tCustomSqlStatement;
                if (tHiveExportTable.getTables() != null) {
                    for (TTable table : tHiveExportTable.getTables()) {
                        tables.add(table.getTableName().toString());
                    }
                    sqlActionModel.setTables(tables);
                }
            } else if (customSqlStatement != null) {
                String name = customSqlStatement.getObjectName().getName();
                tables.add(name != null ? name : "");
                sqlActionModel.setTables(tables);

                if (customSqlStatement.getObjectNameList() != null) {
                    for (ObjectName objectName : customSqlStatement.getObjectNameList()) {
                        tables.add(objectName.getName());
                    }
                }
            }

            getTableByRegex(tables, sqlActionModel, sqlParserModel);


        } catch (Exception e) {
            logger.error("crud parse failed!", e);
        }

        return sqlActionModel;
    }

    public static MergeResult produceMergeWhenClauseOperationMap(TMergeSqlStatement tMergeSqlStatement, MergeResult previousMergeResult) {

        MergeHandler mergeHandler = MergeHandler.of(tMergeSqlStatement, previousMergeResult);

        String targetTableNameStr = getTargetTableNameStr(tMergeSqlStatement.getTargetTable(), mergeHandler); //merge into语句的target表的原始字符串名称

        Map<String, Set<String>> tableMap = mergeHandler.getMergeResult().getTableMap();

        //using的table是subQuery的情况。（目前不用处理，之前老代码有处理过）
        //...

        //onCondition。一般是target.xx = using.xx，但有特殊情况，也可以是其它自由的情况。
        Optional.ofNullable(tMergeSqlStatement.getCondition()).ifPresent(mergeHandler::handleMatchedExtraCondition);

        if (tMergeSqlStatement.getWhenClauses() != null) {
            TPTNodeList<TMergeWhenClause> whenClauses = tMergeSqlStatement.getWhenClauses();
            for (int i = 0; i < whenClauses.size(); i++) {
                TMergeWhenClause clause = whenClauses.getElement(i);
                //外围额外的条件，比如 WHEN MATCHED and s.id = 3
                Optional.ofNullable(clause.getCondition()).ifPresent(mergeHandler::handleMatchedExtraCondition);

                //updateClause
                TMergeUpdateClause updateClause = clause.getUpdateClause();
                if (updateClause != null) {
                    //updateClause -- resultColumnList
                    Optional.ofNullable(updateClause.getUpdateColumnList()).ifPresent(resultColumnList -> {
                        if (resultColumnList.size() != 0) {
                            tableMap.computeIfAbsent(SqlConstant.KEY_UPDATE, k -> new HashSet<>()).add(targetTableNameStr);
                        }
                        for (TResultColumn resultColumn : resultColumnList) {
                            TExpression expr = resultColumn.getExpr();
                            if (expr.getExpressionType() == EExpressionType.assignment_t) {
                                //leftOperand的列和sourceTable的关系
                                //...
                                TExpression rightOperand = expr.getRightOperand();
                                mergeHandler.handleTExpressionIsFuncCase(rightOperand);
                                mergeHandler.handleTExpressionIsSubQueryCase(rightOperand);
                            } else if (expr.getExpressionType() == EExpressionType.group_comparison_t && expr.getRightOperand() != null
                                    && expr.getRightOperand().getExpressionType() == EExpressionType.list_t && expr.getRightOperand().getExprList() != null) { //update set (id,name)=(1,'xx')

                                expr.getRightOperand().getExprList().forEach(item -> {
                                    mergeHandler.handleTExpressionIsFuncCase(item);
                                    mergeHandler.handleTExpressionIsSubQueryCase(item);
                                });
                            } else if (expr.getExpressionType() == EExpressionType.group_comparison_t && expr.getRightOperand() != null
                                    && expr.getRightOperand().getExpressionType() == EExpressionType.subquery_t && expr.getRightOperand().getSubQuery() != null) { //update set (id,name)=(subQuery)
                                mergeHandler.handleTExpressionIsSubQueryCase(expr.getRightOperand());
                            }
                        }
                    });
                    //updateClause -- whereClause
                    Optional.ofNullable(updateClause.getUpdateWhereClause()).ifPresent(whereClauseOfUpdate -> {
                        if (whereClauseOfUpdate.getExpressionType() != null) {
                            mergeHandler.handleMatchedExtraCondition(whereClauseOfUpdate);
                        }
                    });
                    //updateClause -- deleteWhereClause
                    Optional.ofNullable(updateClause.getDeleteWhereClause()).ifPresent(deleteWhereClause -> {
                        if (deleteWhereClause.getExpressionType() != null) { //通过type是否存在，来判断该where条件语句是否合理
                            tableMap.computeIfAbsent(SqlConstant.KEY_DELETE, k -> new HashSet<>()).add(targetTableNameStr);
                            mergeHandler.handleMatchedExtraCondition(deleteWhereClause);
                        }
                    });
                }
                //insertClause
                TMergeInsertClause insertClause = clause.getInsertClause();
                if (insertClause != null && tMergeSqlStatement.getTargetTable() != null) {
                    tableMap.computeIfAbsent(SqlConstant.KEY_INSERT, k -> new HashSet<>()).add(targetTableNameStr);
                    //处理valueList
                    Optional.ofNullable(insertClause.getValuelist()).ifPresent(valueList -> {
                        for (TResultColumn resultColumn : valueList) {
                            TExpression expr = resultColumn.getExpr();
                            mergeHandler.handleTExpressionIsFuncCase(expr);
                            mergeHandler.handleTExpressionIsSubQueryCase(expr);
                        }
                    });
                    //处理 Merge into 语句中 insertClause 中的 insertWhereClause
                    Optional.ofNullable(insertClause.getInsertWhereClause()).ifPresent(mergeHandler::handleMatchedExtraCondition);
                }
                //deleteClause
                TMergeDeleteClause deleteClause = clause.getDeleteClause();
                if (deleteClause != null) {
                    Optional.ofNullable(deleteClause.getCondition()).ifPresentOrElse(condition -> {
                        if (condition.getExpressionType() == EExpressionType.simple_comparison_t) {
                            TExpression leftOperand = condition.getLeftOperand();
                            if (leftOperand != null) {
                                Optional.ofNullable(leftOperand.getObjectOperand().getSourceTable()).ifPresent(sourceTable -> {
                                    tableMap.computeIfAbsent(SqlConstant.KEY_DELETE, k -> new HashSet<>()).add(sourceTable.getTableName().toString());
                                });
                            }
                            TExpression rightOperand = condition.getRightOperand();
                            mergeHandler.handleTExpressionIsFuncCase(rightOperand);
                            mergeHandler.handleTExpressionIsSubQueryCase(rightOperand);
                        }
                    }, () -> tableMap.computeIfAbsent(SqlConstant.KEY_DELETE, k -> new HashSet<>()).add(targetTableNameStr));
                }
            }
        }

        if (tMergeSqlStatement.getErrorLoggingClause() != null) {
            TObjectName tableName = tMergeSqlStatement.getErrorLoggingClause().getTableName();
            if (tableName != null && StringUtils.isNotEmpty(tableName.toString())) {
                tableMap.computeIfAbsent(SqlConstant.KEY_INSERT, k -> new HashSet<>()).add(tableName.toString());
            }
        }

        if (tMergeSqlStatement.getOutputClause() != null) {
            TOutputClause outputClause = tMergeSqlStatement.getOutputClause();
            TObjectName intoTable = outputClause.getIntoTable();
            if (intoTable != null && intoTable.getDbObjectType() != null && intoTable.getDbObjectType() == EDbObjectType.table && StringUtils.isNotEmpty(intoTable.toString())) {
                tableMap.computeIfAbsent(SqlConstant.KEY_INSERT, k -> new HashSet<>()).add(intoTable.toString());
            }
        }
        return mergeHandler.getMergeResult();
    }

    private static String getTargetTableNameStr(TTable targetTable, MergeHandler mergeHandler) {
        ETableSource tableType = targetTable.getTableType();
        if (tableType == ETableSource.subquery && targetTable.getSubquery() != null && targetTable.getSubquery().getTables().size() > 0) {
            String fromTableNameStr = targetTable.getSubquery().getTables().getTable(0).getTableName().toString();
            mergeHandler.getMergeResult().getTableMap().computeIfAbsent(SqlConstant.KEY_SELECT, k -> new HashSet<>()).add(fromTableNameStr);
            return fromTableNameStr;
        } else {
            return targetTable.getTableName().toString();
        }
    }

    private static void handleTExpressionIsFuncCase(TExpression operand, MergeHandler mergeHandler) {
        if (operand == null) {
            return;
        }
        Map<String, Set<String>> staFuncMap = mergeHandler.getMergeResult().getStaFuncMap();
        TMergeSqlStatement tMergeSqlStatement = mergeHandler.getTMergeSqlStatement();
        Map<String, Set<String>> funcMap = mergeHandler.getMergeResult().getFuncMap();

        //TODO，递归解决，加减乘除等复杂计算式。@date 1/21已完成
        Optional.ofNullable(operand.getExpressionType()).ifPresent(expressionType -> {
            if (expressionType.toString().startsWith("arithmetic_") || expressionType == EExpressionType.parenthesis_t) {
                if (operand.getLeftOperand() != null) {
                    mergeHandler.handleTExpressionIsFuncCase(operand.getLeftOperand());
                }
                if (operand.getRightOperand() != null) {
                    mergeHandler.handleTExpressionIsFuncCase(operand.getRightOperand());
                }
            }
        });

        if (operand.getExpressionType() == EExpressionType.function_t && operand.getFunctionCall() != null) {
            String funcName = operand.getFunctionCall().getFunctionName().toString();
            if (CommonUtil.isStaFunc(funcName)) {
                Optional.ofNullable(operand.getFunctionCall().getArgs()).ifPresent(args -> {
                    for (TExpression arg : args) {
                        if (arg.getExpressionType() == EExpressionType.simple_object_name_t) {
                            TTable sourceTable = arg.getObjectOperand().getSourceTable();
                            if (sourceTable != null && sourceTable.getTableType() == ETableSource.objectname) {
                                staFuncMap.computeIfAbsent(funcName, K -> new HashSet<>()).add(sourceTable.getTableName().toString());
                            }
                            ArrayList<TTable> sourceTableList = arg.getObjectOperand().getSourceTableList();
                            if (sourceTableList != null && !sourceTableList.isEmpty()) {
                                for (TTable table : sourceTableList) {
                                    if (table.getTableType() == ETableSource.objectname) {
                                        staFuncMap.computeIfAbsent(funcName, K -> new HashSet<>()).add(table.getTableName().toString());
                                    }
                                }
                            }
                        } else if (arg.getExpressionType() == EExpressionType.simple_constant_t) {
                            if (tMergeSqlStatement.getTargetTable() != null && tMergeSqlStatement.getUsingTable() != null) {
                                List.of(tMergeSqlStatement.getTargetTable(), tMergeSqlStatement.getUsingTable()).forEach(table -> {
                                    staFuncMap.computeIfAbsent(funcName, K -> new HashSet<>()).add(table.getTableName().toString());
                                });
                            }
                        }
                    }
                });
            } else {
                funcMap.computeIfAbsent(SqlConstant.KEY_CALL, k -> new HashSet<>()).add(funcName);
            }
        }
    }

    private static void handleTExpressionIsSubQueryCase(TExpression operand, MergeHandler mergeHandler) {
        if (operand == null) {
            return;
        }

        Map<String, Set<String>> tableMap = mergeHandler.getMergeResult().getTableMap();
        Map<String, Set<String>> funcMap = mergeHandler.getMergeResult().getFuncMap();
        Map<String, Set<String>> staFuncMap = mergeHandler.getMergeResult().getStaFuncMap();
        TMergeSqlStatement tMergeSqlStatement = mergeHandler.getTMergeSqlStatement();

        //TODO，递归解决，加减乘除等复杂计算式。@date 1/21已完成
        Optional.ofNullable(operand.getExpressionType()).ifPresent(expressionType -> {
            if (expressionType.toString().startsWith("arithmetic_") || expressionType == EExpressionType.parenthesis_t) {
                if (operand.getLeftOperand() != null) {
                    mergeHandler.handleTExpressionIsSubQueryCase(operand.getLeftOperand());
                }
                if (operand.getRightOperand() != null) {
                    mergeHandler.handleTExpressionIsSubQueryCase(operand.getRightOperand());
                }
            }
        });

        if (operand.getExpressionType() == EExpressionType.subquery_t && operand.getSubQuery() != null) {
            GetTablesUtil getTablesUtil = new GetTablesUtil();
            Set<String> tableStrs = getTablesUtil.analyzeSelectStatement(operand.getSubQuery());
            Set<String> funcStrs = getTablesUtil.getFunctions();

            GetColumnsUtil getColumnsUtil = new GetColumnsUtil();
            Set<String> otherFuncStrs = getColumnsUtil.getFunctions(operand.getSubQuery());
            funcStrs.addAll(otherFuncStrs);

            tableStrs.forEach(str -> tableMap.computeIfAbsent(SqlConstant.KEY_SELECT, k -> new HashSet<>()).add(str));
            funcStrs.forEach(str -> handleFuncName(str, tableStrs, staFuncMap, funcMap, tMergeSqlStatement));
        }
    }

    private static void handleFuncName(String funcName, Set<String> tableStrs, Map<String, Set<String>> staFuncMap, Map<String, Set<String>> funcMap, TMergeSqlStatement tMergeSqlStatement) {
        if (CommonUtil.isStaFunc(funcName)) {
            if (tableStrs != null && !tableStrs.isEmpty()) {
                tableStrs.forEach(str -> staFuncMap.computeIfAbsent(funcName, K -> new HashSet<>()).add(str));
            }
        } else {
            funcMap.computeIfAbsent(SqlConstant.KEY_CALL, k -> new HashSet<>()).add(funcName);
        }
    }

    private static void handleMatchedExtraCondition(TExpression condition, MergeHandler mergeHandler) {
        if (condition != null && condition.getExpressionType() != null) {
            switch (condition.getExpressionType()) {
                case simple_comparison_t: {
                    List.of(condition.getLeftOperand(), condition.getRightOperand()).forEach(operand -> {
                        switch (operand.getExpressionType()) {
                            case function_t: mergeHandler.handleTExpressionIsFuncCase(operand);break;
                            case subquery_t: mergeHandler.handleTExpressionIsSubQueryCase(operand);break;
                        }
                    });
                    break;
                }
                case logical_and_t:
                case logical_or_t:
                case parenthesis_t: {
                    //利用flattedAndOrExpr
                    //...
                    if (condition.getLeftOperand() != null) {
                        mergeHandler.handleMatchedExtraCondition(condition.getLeftOperand());
                    }
                    if (condition.getRightOperand() != null) {
                        mergeHandler.handleMatchedExtraCondition(condition.getRightOperand());
                    }
                    break;
                }
            }
        }
    }

    @Getter
    public static class MergeHandler {
        private final TMergeSqlStatement tMergeSqlStatement;
        private final MergeResult mergeResult;

        //复用之前的结果
        public MergeHandler(TMergeSqlStatement tMergeSqlStatement, MergeResult mergeResult) {
            this.tMergeSqlStatement = tMergeSqlStatement;
            this.mergeResult = mergeResult;
        }
        //用全新的结果
        public MergeHandler(TMergeSqlStatement tMergeSqlStatement) {
            this.tMergeSqlStatement = tMergeSqlStatement;
            this.mergeResult = new MergeResult();
        }

        public static MergeHandler of(TMergeSqlStatement tMergeSqlStatement, MergeResult mergeResult) {
            if (mergeResult == null) {
                return new MergeHandler(tMergeSqlStatement);
            } else {
                return new MergeHandler(tMergeSqlStatement, mergeResult);
            }
        }

        public void handleTExpressionIsFuncCase(TExpression operand) {
            CrudUtil.handleTExpressionIsFuncCase(operand,this);
        }

        public void handleTExpressionIsSubQueryCase(TExpression operand) {
            CrudUtil.handleTExpressionIsSubQueryCase(operand, this);
        }

        public void handleMatchedExtraCondition(TExpression condition) {
            CrudUtil.handleMatchedExtraCondition(condition, this);
        }
    }

    @Getter
    public static class MergeResult {
        private final Map<String, Set<String>> staFuncMap = new LinkedHashMap<>();
        private final Map<String, Set<String>> funcMap = new LinkedHashMap<>();
        private final Map<String, Set<String>> tableMap = new LinkedHashMap<>();
    }

    public static void getTableByRegex(Set<String> tables, SqlActionModel sqlActionModel, SqlParseModel sqlParserModel) {
        if (tables.size() == 0 && !sqlActionModel.isInsertIntoSelect()) {
            boolean isMatched = false;
            DCustomSqlStatement tCustomSqlStatement = sqlParserModel.gettCustomSqlStatement();

            if (!isMatched) {
                String pattern = "(INSERT\\s+INTO)\\s+([0-9a-zA-Z_\"'\\[\\]`.]+)\\s+(DEFAULT\\s+VALUES)";
                Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                Matcher m = p.matcher(tCustomSqlStatement.toString());
                if (m.find()) {
                    tables.add(m.group(2));
                    sqlActionModel.setTables(tables);
                    sqlParserModel.setErrorMessage(null);
                    isMatched = true;
                }
            }

            if (!isMatched) {
                String pattern = "(SELECT)\\s+([0-9a-zA-Z_\"'\\[\\]`.\\*]+)\\s+(INTO)\\s+([0-9a-zA-Z_\"'\\[\\]`.]+)\\s+(FROM)\\s+([0-9a-zA-Z_\"'\\[\\]`.]+)";
                Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                Matcher m = p.matcher(tCustomSqlStatement.toString());
                if (m.find()) {
                    sqlActionModel.setObjectName(m.group(4));
                    tables.add(m.group(6));
                    sqlActionModel.setTables(tables);
                    sqlActionModel.setSelectInto(true);
                    isMatched = true;
                }
            }

            if (!isMatched) {
                // select functionName
                String pattern = "(SELECT)\\s+([0-9a-zA-Z_\"'\\[\\]\\-:`.@]+)";
                Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                Matcher m = p.matcher(tCustomSqlStatement.toString());
                if (m.find()) {
//                    tables.add(m.group(2));
                    sqlActionModel.setTables(tables);
                    sqlActionModel.setSelectFunction(true);
                    sqlParserModel.setErrorMessage(null);
                    isMatched = true;
                }
            }

            if (!isMatched) {
                // gBase8a、mysql: LOAD DATA INFILE 'sftp://root:1qa@WS3ed@192.168.3.131/home/<USER>' INTO TABLE lwc.t1
                String pattern = "(LOAD\\s+DATA\\s+INFILE)\\s+([0-9a-zA-Z_\"'\\[\\]`.:/@]+)\\s+(INTO\\s+TABLE)\\s+([0-9a-zA-Z_\"'\\[\\]`.]+)";
                Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                Matcher m = p.matcher(tCustomSqlStatement.toString());
                if (m.find()) {
                    tables.add(m.group(4));
                    sqlActionModel.setTables(tables);
                    sqlParserModel.setErrorMessage(null);
                    isMatched = true;
                }
            }

            if (!isMatched) {
                // kingBase: table tableName;
                String pattern = "^(TABLE)\\s+([0-9a-zA-Z_\"'\\[\\]`.]+)";
                Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                Matcher m = p.matcher(tCustomSqlStatement.toString());
                if (m.find()) {
                    tables.add(m.group(2));
                    sqlActionModel.setTables(tables);
                    sqlParserModel.setErrorMessage(null);
                    isMatched = true;
                }
            }

            if (!isMatched) {
                // select functionName
                String pattern = "(SELECT)\\s+(\\*)\\s+(FROM)\\s+([0-9a-zA-Z_\"'\\[\\]`.]+)\\s*(\\()\\s*([0-9a-zA-Z_,/'\\s]*)\\s*(\\))";
                Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                Matcher m = p.matcher(tCustomSqlStatement.toString());
                if (m.find()) {
                    if (sqlActionModel.getFunctions() != null) {
                        sqlActionModel.getFunctions().add(m.group(4));
                    }
                    if (sqlActionModel.getTables() != null) {
                        sqlActionModel.getTables().addAll(tables);
                    }
                    sqlActionModel.setSelectFunction(true);
                    isMatched = true;
                }
            }

            if (!isMatched) {
                // ob-oracle: explain tableName;
                String pattern = "(EXPLAIN)\\s+([0-9a-zA-Z_\"'\\[\\]`.]+)";
                Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                Matcher m = p.matcher(tCustomSqlStatement.toString());
                if (m.find()) {
                    tables.add(m.group(2));
                    sqlActionModel.setTables(tables);
                    sqlActionModel.setExplainOperation(SqlConstant.KEY_SELECT);
                    sqlParserModel.setErrorMessage(null);
                    isMatched = true;
                }
            }

            if (!isMatched) {
                String pattern = "^(?si)\\s*VALUES\\s+([0-9a-zA-Z_`\"']+\\s*\\(.*?\\) | \\([0-9a-zA-Z_`\"']+\\s*\\(.*?\\)(\\s*,\\s*[0-9a-zA-Z_`\"']+\\s*\\(.*?\\))*\\))";
                Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                Matcher m = p.matcher(tCustomSqlStatement.toString());
                if (m.find()) {
                    sqlActionModel.setSelectFunction(true);
                    isMatched = true;
                }
            }
        }
    }

    public static String getSequenceName(DCustomSqlStatement tCustomSqlStatement) {
        try {
            String pattern = "(VALUES)\\s+(PREVIOUS|NEXT)\\s+(VALUE)\\s+(FOR)\\s+([0-9a-zA-Z_\"'\\[\\]`.]+)";
            Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
            Matcher m = p.matcher(tCustomSqlStatement.toString());
            if (m.find()) {
                return m.group(5);
            }
        } catch (Exception e) {
            logger.error("get sequence name error!", e);
        }
        return "";
    }

    public static void getFunctionName(TFunctionCall functionCall, Set<String> functions, SqlActionModel sqlActionModel) {
        String functionName = functionCall.getFunctionName().toString();
        functions.add(functionName);
        sqlActionModel.setSelectFunction(true);

        List<String> funcArgs = new ArrayList<>();
        sqlActionModel.setFuncArgs(funcArgs);
        TExpressionList args = functionCall.getArgs();
        if (args != null) {
            for (TExpression expression : args) {
                if (expression.getConstantOperand() != null) {
                    String s = expression.getConstantOperand().toString();
                    funcArgs.add(s);
                }
            }
        }
    }

}
