package com.dc.summer.parser.sql.model;

import lombok.Data;

@Data
public class SqlVerifyResult {

    private String message;
    private Integer status;
    private String sql;
    private String tableName;
    private String WhereCondition;
    private String operation;

    public SqlVerifyResult() {
    }

    public SqlVerifyResult(Integer status, String sql) {
        this.status = status;
        this.sql = sql;
    }
}
