package com.dc.summer.ext.elasticsearch.data;

import com.dc.summer.DBException;
import com.dc.summer.ext.elasticsearch.ESUtils;
import com.dc.summer.ext.elasticsearch.model.ESDataSource;
import com.dc.summer.model.document.data.DBAbstractDocument;
import com.dc.summer.model.document.data.DBMapValue;
import com.dc.summer.model.runtime.DBRProgressMonitor;

import java.io.*;
import java.nio.charset.Charset;
import java.util.Map;

public class ESDocumentMap extends DBAbstractDocument<ESDataSource, Map<String, Object>> {


    public ESDocumentMap(ESDataSource dataSource, String value) {
        super(dataSource, ESUtils.GSON_BUILDER_SIMPLE.fromJson(value, Map.class));
    }

    public ESDocumentMap(ESDataSource dataSource, Map<String, Object> rawValue) {
        super(dataSource, rawValue);
    }

    @Override
    protected DBMapValue<ESDataSource> makeRawMap() {
        return new DBMapValue(dataSource, null, rawValue);
    }

    @Override
    public Object getDocumentId() {
        return rawValue.get("_scroll_id");
    }

    @Override
    public Object getDocumentProperty(String name) {
        if (name.contains(".")) {

            Object value = rawValue;

            for (String layer : name.split("\\.")) {

                if (value instanceof Map) {
                    value = ((Map<?, ?>) value).get(layer);
                } else {
                    return null;
                }

            }

            return value;

        } else {
            return rawValue.get(name);
        }
    }

    @Override
    public void serializeDocument(DBRProgressMonitor monitor, OutputStream stream, Charset charset, DBDDocumentType type, boolean prettyJson) throws IOException, DBException {

        try (Writer out = new OutputStreamWriter(stream, charset)) {
            if (prettyJson) {
                ESUtils.GSON_BUILDER_PRETTY.toJson(rawValue, out);
            } else {
                ESUtils.GSON_BUILDER_SIMPLE.toJson(rawValue, out);
            }
        }

    }

    @Override
    public void updateDocument(DBRProgressMonitor monitor, InputStream stream, Charset charset) throws IOException, DBException {
        // nothing to do here
    }
}
