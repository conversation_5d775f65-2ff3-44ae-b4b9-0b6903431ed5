
package com.dc.summer.ext.elasticsearch;

import com.dc.summer.DBException;
import com.dc.summer.ext.elasticsearch.model.ESDataSource;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.DBPDataSourceProvider;
import com.dc.summer.model.app.DBPPlatform;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.connection.DBPDriver;
import com.dc.summer.model.impl.jdbc.JDBCURL;
import com.dc.summer.model.preferences.DBPPropertyDescriptor;
import com.dc.summer.model.runtime.DBRProgressMonitor;

public class ESDataSourceProvider implements DBPDataSourceProvider {

    @Override
    public void init(DBPPlatform platform) {
    }

    @Override
    public long getFeatures() {
        return FEATURE_NONE;
    }

    @Override
    public DBPPropertyDescriptor[] getConnectionProperties(DBRProgressMonitor monitor, DBPDriver driver, DBPConnectionConfiguration connectionInfo) throws DBException {
        return new DBPPropertyDescriptor[0];
    }

    @Override
    public DBPDataSource openDataSource(DBRProgressMonitor monitor, DBPDataSourceContainer container) throws DBException {
        return new ESDataSource(monitor, container);
    }

    @Override
    public String getConnectionURL(DBPDriver driver, DBPConnectionConfiguration connectionInfo) {
        return JDBCURL.generateUrlByTemplate(driver, connectionInfo);
    }
}
