package com.dc.summer.ext.elasticsearch.exec;

import com.dc.summer.DBException;
import com.dc.summer.ext.elasticsearch.model.ESDataSource;
import com.dc.summer.model.exec.*;
import com.dc.summer.model.impl.AbstractSession;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import java.util.regex.Pattern;

public class ESSessionImpl extends AbstractSession implements ESSession {

    public static final Pattern REST_PATTERN = Pattern.compile("\\s*(?:get|post|put|patch|delete|head|options|trace).+", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);

    private final ESExecuteContext context;

    public ESSessionImpl(DBRProgressMonitor monitor, DBCExecutionPurpose purpose, String taskTitle, ESExecuteContext context) {
        super(monitor, purpose, taskTitle);
        this.context = context;
    }

    @Override
    public ESExecuteContext getExecutionContext() {
        return this.context;
    }

    @Override
    public ESDataSource getDataSource() {
        return this.context.getDataSource();
    }

    @Override
    public DBCStatement prepareStatement(DBCStatementType type, String query, boolean scrollable, boolean updatable, boolean returnGeneratedKeys) throws DBCException {

        if (REST_PATTERN.matcher(query).matches()) {
            return new ESRestStatement(this, query);

        } else if (query.startsWith("PAGE")) {
            return new ESPageStatement(this, query);

        }

        throw new DBCException("Invalid query statement.");
    }

    @Override
    public void cancelBlock(DBRProgressMonitor monitor, Thread blockThread) throws DBException {
        if (blockThread != null) {
            blockThread.interrupt();
        }
    }

}
