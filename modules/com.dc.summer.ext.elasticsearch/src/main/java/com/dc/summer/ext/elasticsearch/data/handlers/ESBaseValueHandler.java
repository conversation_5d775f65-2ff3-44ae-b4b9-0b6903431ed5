package com.dc.summer.ext.elasticsearch.data.handlers;

import com.dc.code.NotNull;
import com.dc.summer.model.DBPDataKind;
import com.dc.summer.model.exec.*;
import com.dc.summer.model.impl.data.BaseValueHandler;
import com.dc.summer.model.struct.DBSAttributeBase;
import com.dc.summer.model.struct.DBSTypedObject;


public abstract class ESBaseValueHandler extends BaseValueHandler {

   public Object fetchValueObject(@NotNull DBCSession session, @NotNull DBCResultSet resultSet, @NotNull DBSTypedObject type, int index) throws DBCException {
      return type instanceof DBSAttributeBase && type.getDataKind() != DBPDataKind.DOCUMENT ? resultSet.getAttributeValue(((DBSAttributeBase)type).getName()) : resultSet.getAttributeValue(index);
   }

   public final void bindValueObject(@NotNull DBCSession session, @NotNull DBCStatement statement, @NotNull DBSTypedObject columnMetaData, int index, Object value) throws DBCException {
      // nothing to do here
   }

}
