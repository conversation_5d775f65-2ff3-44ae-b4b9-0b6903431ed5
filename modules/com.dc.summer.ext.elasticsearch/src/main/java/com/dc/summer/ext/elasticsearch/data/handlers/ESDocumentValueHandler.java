package com.dc.summer.ext.elasticsearch.data.handlers;

import com.dc.code.NotNull;
import com.dc.summer.ext.elasticsearch.data.ESDocumentList;
import com.dc.summer.ext.elasticsearch.data.ESDocumentMap;
import com.dc.summer.ext.elasticsearch.model.ESDataSource;
import com.dc.summer.model.Types;
import com.dc.summer.model.data.DBDComplexValue;
import com.dc.summer.model.data.DBDDisplayFormat;
import com.dc.summer.model.data.DBDValueHandlerComposite;
import com.dc.summer.model.document.exec.DocumentResultSet;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCResultSet;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.struct.DBSTypedObject;

import java.util.List;
import java.util.Map;


public class ESDocumentValueHandler extends ESBaseValueHandler implements DBDValueHandlerComposite {
   public static final ESDocumentValueHandler INSTANCE = new ESDocumentValueHandler();

   private ESDocumentValueHandler() {
   }

   @Override
   public @NotNull Class<String> getValueObjectType(@NotNull DBSTypedObject attribute) {
      return String.class;
   }

   @Override
   public @NotNull String getValueContentType(@NotNull DBSTypedObject attribute) {
      return "text/json";
   }

   @Override
   public @NotNull String getValueDisplayString(@NotNull DBSTypedObject column, Object value, @NotNull DBDDisplayFormat format) {
      return !(value instanceof Object) && !(value instanceof DBDComplexValue) ? "#document" : value.toString();
   }

   @Override
   public Object getValueFromObject(@NotNull DBCSession session, @NotNull DBSTypedObject type, Object object, boolean copy, boolean validateValue) throws DBCException {
      return object;
   }

   @Override
   public final Object fetchValueObject(@NotNull DBCSession session, @NotNull DBCResultSet resultSet, @NotNull DBSTypedObject type, int index) throws DBCException {
      Object value = resultSet.getAttributeValue(index);
      if (value instanceof String && session.getDataSource() instanceof ESDataSource && resultSet instanceof DocumentResultSet) {
         int typeId = ((DocumentResultSet) resultSet).getTypeId();
         if (typeId == Types.JSON && ((String) value).indexOf("[") == 0) {
            return new ESDocumentList((ESDataSource) session.getDataSource(), (String) value);
         } else if (typeId == Types.JSON && ((String) value).indexOf("{") == 0) {
            return new ESDocumentMap((ESDataSource) session.getDataSource(), (String) value);
         } else {
            return value;
         }
      } else if (value instanceof Map) {
         return new ESDocumentMap((ESDataSource) session.getDataSource(), (Map<String, Object>) value);
      } else if (value instanceof List) {
         return new ESDocumentList((ESDataSource) session.getDataSource(), (List<Object>) value);
      }
      return resultSet;
   }

}
