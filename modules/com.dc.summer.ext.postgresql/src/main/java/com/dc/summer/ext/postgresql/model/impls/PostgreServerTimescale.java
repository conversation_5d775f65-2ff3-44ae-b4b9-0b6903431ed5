
package com.dc.summer.ext.postgresql.model.impls;

import com.dc.summer.ext.postgresql.model.PostgreDataSource;

/**
 * PostgreServerTimescale
 */
public class PostgreServerTimescale extends PostgreServerExtensionBase {

    public PostgreServerTimescale(PostgreDataSource dataSource) {
        super(dataSource);
    }

    @Override
    public boolean supportsEntityMetadataInResults() {
        return true;
    }

    @Override
    public boolean supportsCopyFromStdIn() {
        return true;
    }

    @Override
    public String getServerTypeName() {
        return "Timescale";
    }
}

