
package com.dc.summer.ext.postgresql.model.session;

import com.dc.annotation.SQL;
import com.dc.summer.DBException;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.admin.sessions.DBAServerSessionManager;
import com.dc.summer.model.admin.sessions.DBAServerSessionManagerSQL;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.ext.postgresql.model.PostgreDataSource;
import com.dc.summer.model.exec.DBCSession;

import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * Postgre session manager
 */
public class PostgreSessionManager implements DBAServerSessionManager<PostgreSession>, DBAServerSessionManagerSQL {

    public static final String PROP_KILL_QUERY = "killQuery";

    private final PostgreDataSource dataSource;

    public PostgreSessionManager(PostgreDataSource dataSource)
    {
        this.dataSource = dataSource;
    }

    @Override
    public PostgreDataSource getDataSource()
    {
        return dataSource;
    }

    @Override
    public Collection<PostgreSession> getSessions(DBCSession session, Map<String, Object> options) throws DBException
    {
        try {
            try (JDBCPreparedStatement dbStat = ((JDBCSession) session).prepareStatement(generateSessionReadQuery(options))) {
                try (JDBCResultSet dbResult = dbStat.executeQuery()) {
                    List<PostgreSession> sessions = new ArrayList<>();
                    while (dbResult.next()) {
                        sessions.add(new PostgreSession(dbResult));
                    }
                    return sessions;
                }
            }
        } catch (SQLException e) {
            throw new DBException(e, session.getDataSource());
        }
    }

    @Override
    public void alterSession(DBCSession session, PostgreSession sessionType, Map<String, Object> options) throws DBException
    {
        try {
            try (Statement dbStat = ((JDBCSession) session).createStatement()) {
                @SQL String sql = "SELECT @_catalog.@_terminate_backend(" + sessionType.getPid() + ")";
                dbStat.execute(sql.replace("@", getDataSource().getInstancePrefix()));
            }
        }
        catch (SQLException e) {
            throw new DBException(e, session.getDataSource());
        }
    }

    @Override
    public boolean canGenerateSessionReadQuery() {
        return true;
    }

    @Override
    public String generateSessionReadQuery(Map<String, Object> options) {
        return "SELECT sa.* FROM @_catalog.@_stat_activity sa".replace("@", getDataSource().getInstancePrefix());
    }
}
