
package com.dc.summer.ext.postgresql.model.plan;


import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.plan.*;
import com.dc.summer.model.impl.plan.ExecutionPlanDeserializer;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.JsonPrimitive;
import com.dc.code.NotNull;
import com.dc.summer.ext.postgresql.model.PostgreDataSource;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.plan.*;
import com.dc.summer.model.impl.plan.AbstractExecutionPlanSerializer;
import com.dc.utils.CommonUtils;

import java.io.IOException;
import java.io.Reader;
import java.io.Writer;
import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.Map;

/**
 * PostgreQueryPlaner
 */
public class PostgreQueryPlaner extends AbstractExecutionPlanSerializer implements DBCQueryPlanner
{
    public static final String PARAM_ANALYSE = "ANALYZE";
    public static final String PARAM_VERBOSE = "VERBOSE";
    public static final String PARAM_COSTS = "COSTS";
    public static final String PARAM_SETTINGS = "SETTINGS";
    public static final String PARAM_BUFFERS = "BUFFERS";
    public static final String PARAM_WAL = "WAL";
    public static final String PARAM_TIMING = "TIMING";
    public static final String PARAM_SUMMARY = "SUMMARY";

    private final PostgreDataSource dataSource;

    public final static String FORMAT_VERSION = "1";

    public PostgreQueryPlaner(PostgreDataSource dataSource) {
        this.dataSource = dataSource;
    }

    @Override
    public PostgreDataSource getDataSource() {
        return dataSource;
    }

    @NotNull
    @Override
    public DBCPlan planQueryExecution(@NotNull DBCSession session, @NotNull String query, @NotNull DBCQueryPlannerConfiguration configuration) throws DBCException {
        PostgreExecutionPlan plan = new PostgreExecutionPlan(
            !dataSource.getServerType().supportsExplainPlanXML(),
            dataSource.getServerType().supportsExplainPlanVerbose(),
            query,
            configuration);
        plan.explain(session);
        return plan;
    }

    @NotNull
    @Override
    public DBCPlanStyle getPlanStyle() {
        return dataSource.getServerType().supportsExplainPlan() ? DBCPlanStyle.PLAN : DBCPlanStyle.QUERY;
    }

    @Override
    public void serialize(@NotNull Writer writer, @NotNull DBCPlan plan) throws IOException {
        serializeJson(writer, plan, dataSource.getInfo().getDriverName(), new DBCQueryPlannerSerialInfo() {

            @Override
            public String version() {
                return FORMAT_VERSION;
            }

            @Override
            public void addNodeProperties(DBCPlanNode node, JsonObject nodeJson) {

                JsonArray attributes = new JsonArray();
                if (node instanceof PostgrePlanNodeBase) {
                    PostgrePlanNodeBase<?> pgNode = (PostgrePlanNodeBase<?>) node;
                    for(Map.Entry<String, String>  e : pgNode.attributes.entrySet()) {
                        JsonObject attr = new JsonObject();
                        attr.add(e.getKey(), new JsonPrimitive(CommonUtils.notEmpty(e.getValue())));
                        attributes.add(attr);
                    }
                }
                nodeJson.add(PROP_ATTRIBUTES, attributes);

            }
        });
    }

    @Override
    public DBCPlan deserialize(@NotNull Reader planData) throws IOException, InvocationTargetException {
        try {
            JsonObject jo = new JsonParser().parse(planData).getAsJsonObject();
            
            String query = getQuery(jo);

            ExecutionPlanDeserializer<PostgrePlanNodeExternal> loader = new ExecutionPlanDeserializer<>();

            List<PostgrePlanNodeExternal> planNodes = loader.loadRoot(dataSource, jo,
                (datasource, node, parent) -> new PostgrePlanNodeExternal((PostgreDataSource) datasource, node, parent));

            return new PostgreExecutionPlan(query, planNodes);

        } catch (Throwable e) {
            throw new InvocationTargetException(e);
        }
    }


}
