
package com.dc.summer.ext.postgresql.model.data;

import com.dc.summer.Log;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.impl.jdbc.data.handlers.JDBCStructValueHandler;
import com.dc.summer.model.struct.DBSTypedObject;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.exec.jdbc.JDBCSession;

import java.sql.SQLException;
import java.sql.Types;

/**
 * PostgreArrayValueHandler
 */
public class PostgreRefCursorValueHandler extends JDBCStructValueHandler {
    private static final Log log = Log.getLog(PostgreRefCursorValueHandler.class);
    public static final PostgreRefCursorValueHandler INSTANCE = new PostgreRefCursorValueHandler();

    @Override
    protected Object fetchColumnValue(DBCSession session, JDBCResultSet resultSet, DBSTypedObject type, int index) throws DBCException, SQLException {
        String cursorName = resultSet.getString(index);
        return new PostgreRefCursor((JDBCSession) session, cursorName);
/*
        // Fetch as string (#1735)
        // Fetching cursor as object will close it so it won;'t be possible to use cursor in consequent queries
        Object object = resultSet.getObject(index);
        if (object instanceof ResultSet) {
            JDBCCursor cursor = new JDBCCursor(
                (JDBCSession) session,
                (ResultSet) object,
                type.getTypeName());
            // Set cursor name
            cursor.setCursorName(cursorName);
            // Disable resulset close on cursor release. Otherwise cusor can't be referred by other queries (#6074)
            cursor.setCloseResultsOnRelease(false);
            return cursor;
        }
        return object;
*/
    }

    @Override
    protected void bindParameter(
        JDBCSession session,
        JDBCPreparedStatement statement,
        DBSTypedObject paramType,
        int paramIndex,
        Object value)
        throws DBCException, SQLException
    {
        if (value == null) {
            statement.setNull(paramIndex, paramType.getTypeID());
        } else {
            statement.setObject(paramIndex, value.toString(), Types.OTHER);
        }
    }

    @Override
    public Object getValueFromObject(DBCSession session, DBSTypedObject type, Object object, boolean copy, boolean validateValue) throws DBCException {
        return object == null ? null : object.toString();
    }

}
