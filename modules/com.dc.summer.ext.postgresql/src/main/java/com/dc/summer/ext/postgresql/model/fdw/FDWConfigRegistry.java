

package com.dc.summer.ext.postgresql.model.fdw;

import com.dc.summer.Log;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.registry.center.Global;
import org.eclipse.core.runtime.IConfigurationElement;
import org.eclipse.core.runtime.IExtensionRegistry;

import java.util.ArrayList;
import java.util.List;

/**
 * DataTransferRegistry
 */
public class FDWConfigRegistry {

    public static final String EXTENSION_ID = "com.dc.summer.postgresql.fdw.config"; //$NON-NLS-1$

    private static FDWConfigRegistry instance = null;

    private static final Log log = Log.getLog(FDWConfigRegistry.class);

    public synchronized static FDWConfigRegistry getInstance()
    {
        if (instance == null) {
            instance = new FDWConfigRegistry(Global.getExtensionRegistry());
        }
        return instance;
    }

    private List<FDWConfigDescriptor> configDescriptors = new ArrayList<>();

    private FDWConfigRegistry(IExtensionRegistry registry)
    {
        // Load datasource providers from external plugins
        IConfigurationElement[] extElements = registry.getConfigurationElementsFor(EXTENSION_ID);
        for (IConfigurationElement ext : extElements) {
            // Load main configDescriptors
            if ("fdw".equals(ext.getName())) {
                configDescriptors.add(new FDWConfigDescriptor(ext));
            }
        }
    }

    public List<FDWConfigDescriptor> getConfigDescriptors() {
        return configDescriptors;
    }

    public FDWConfigDescriptor findFirstMatch(DBPDataSourceContainer dataSource) {
        for (FDWConfigDescriptor desc : configDescriptors) {
            if (desc.matches(dataSource)) {
                return desc;
            }
        }
        return null;
    }

}
