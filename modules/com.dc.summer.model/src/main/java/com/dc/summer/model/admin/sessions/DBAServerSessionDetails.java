

package com.dc.summer.model.admin.sessions;

import com.dc.summer.DBException;
import com.dc.summer.model.DBPObject;
import com.dc.summer.model.exec.DBCSession;

import java.util.List;

/**
 * Server session additiona details provider
 */
public interface DBAServerSessionDetails {

    String getDetailsTitle();

    String getDetailsTooltip();

    List<? extends DBPObject> getSessionDetails(DBCSession session, DBAServerSession serverSession) throws DBException;

    Class<?> getDetailsType();
}
