

package com.dc.summer.model.struct;


/**
 * Object state
 */
public class DBSObjectState
{
    public static final DBSObjectState NORMAL = new DBSObjectState("Normal");
    public static final DBSObjectState INVALID = new DBSObjectState("Invalid");
    public static final DBSObjectState ACTIVE = new DBSObjectState("Active");
    public static final DBSObjectState UNKNOWN = new DBSObjectState("Unknown");

    private final String title;

    public DBSObjectState(String title)
    {
        this.title = title;
    }

    public String getTitle()
    {
        return title;
    }

}