

package com.dc.summer.model.qm;

import com.dc.code.NotNull;
import com.dc.summer.model.runtime.DBRProgressMonitor;

import java.util.List;

/**
 * DBC meta events listener
 */
public interface QMMetaListener {

    /**
     * Notifies listeners about new events.
     * Implementation must process all events in sync mode.
     * QM collector will clean all closed objects after listeners notification.
     */
    void metaInfoChanged(@NotNull DBRProgressMonitor monitor, @NotNull List<QMMetaEvent> events);

}
