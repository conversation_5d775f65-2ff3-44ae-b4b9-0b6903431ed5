
package com.dc.summer.model.impl.edit;

import com.dc.summer.model.edit.DBECommand;
import com.dc.summer.model.edit.DBECommandListener;

/**
 * Command adapter
 */
public abstract class DBECommandAdapter implements DBECommandListener {

    @Override
    public void onCommandChange(DBECommand<?> command)
    {
    }

    @Override
    public void onSave()
    {
    }

    @Override
    public void onReset()
    {
    }

    @Override
    public void onCommandDo(DBECommand<?> command)
    {
    }

    @Override
    public void onCommandUndo(DBECommand<?> command)
    {
    }
}
