

package com.dc.summer.model.impl.auth;

import com.dc.summer.Log;
import com.dc.summer.model.auth.*;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.model.auth.*;
import com.dc.summer.runtime.DBWorkbench;
import com.dc.utils.CommonUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Session context implementation
 */
public class SessionContextImpl implements SMSessionContext {
    private static final Log log = Log.getLog(SessionContextImpl.class);

    private final SMSessionContext parentContext;
    private final List<SMSession> sessions = new ArrayList<>();

    public SessionContextImpl(SMSessionContext parentContext) {
        this.parentContext = parentContext;
    }

    @Nullable
    @Override
    public SMSession getSpaceSession(@NotNull DBRProgressMonitor monitor, @NotNull SMAuthSpace space, boolean open) throws DBException {
        SMSession session = findSpaceSession(space);
        if (session != null) {
            return session;
        }

        //log.debug(">> Session not found in context " + this + " for space " + space);
        session = parentContext == null ? null : parentContext.getSpaceSession(monitor, space, false);
        if (session == null && open) {
            SMSessionProviderService sessionProviderService = DBWorkbench.getService(SMSessionProviderService.class);
            if (sessionProviderService != null) {
                try {
                    // Session will be added in this context by itself (if needed)
                    session = sessionProviderService.acquireSession(monitor, this, space);
                    if (session != null) {
                        addSession(session);
                    }
                } catch (Exception e) {
                    throw new DBException("Error acquiring session", e);
                }
            }
        }
        return session;
    }

    @Nullable
    @Override
    public SMSession findSpaceSession(@NotNull SMAuthSpace space) {
        for (SMSession session : sessions) {
            if (CommonUtils.equalObjects(session.getSessionSpace(), space)) {
                return session;
            }
        }
        return null;
    }

    @Override
    public SMAuthToken[] getSavedTokens() {
        return new SMAuthToken[0];
    }

    public void addSession(@NotNull SMSession session) {
        if (!sessions.contains(session)) {
            sessions.add(session);
            //log.debug(">> Session added to context " + this + ", space=" + session.getSessionSpace() + ": " + session, new Exception());
        } else {
            log.debug("Session '" + session + "' was added twice");
        }
    }

    @Override
    public boolean removeSession(@NotNull SMSession session) {
        if (sessions.remove(session)) {
            //log.debug(">> Session removed from context " + this + ", space=" + session.getSessionSpace()  + ": " + session, new Exception());
            return true;
        } else {
            log.debug("Session '" + session + "' was removed twice");
            return false;
        }
    }

    public void close() {
        this.sessions.clear();
    }

}
