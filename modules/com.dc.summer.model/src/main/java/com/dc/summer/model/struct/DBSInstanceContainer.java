
package com.dc.summer.model.struct;

import com.dc.code.NotNull;
import com.dc.summer.model.runtime.DBRProgressMonitor;

import java.util.Collection;

/**
 * Instance container.
 */
public interface DBSInstanceContainer extends DBSObject
{
    @NotNull
    DBSInstance getDefaultInstance();

    @NotNull
    Collection<? extends DBSInstance> getAvailableInstances();

    void shutdown(DBRProgressMonitor monitor);

}
