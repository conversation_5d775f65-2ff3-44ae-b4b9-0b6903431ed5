
package com.dc.summer.model.data;

import com.dc.summer.model.struct.rdb.DBSTable;
import com.dc.code.NotNull;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSAttributeBase;

public interface DBDInsertReplaceMethod {

    @NotNull
    String getOpeningClause(@NotNull DBSTable table, @NotNull DBRProgressMonitor monitor);

    String getTrailingClause(@NotNull DBSTable table, @NotNull DBRProgressMonitor monitor, DBSAttributeBase[] attributes);

}
