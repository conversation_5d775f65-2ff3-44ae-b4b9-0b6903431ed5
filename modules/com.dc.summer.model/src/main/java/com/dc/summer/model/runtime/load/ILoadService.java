

package com.dc.summer.model.runtime.load;

import com.dc.summer.model.runtime.DBRProgressMonitor;

import java.lang.reflect.InvocationTargetException;

/**
 * Lazy loading service
 * @param <RESULT> result type
 */
public interface ILoadService<RESULT> {

    String getServiceName();

    RESULT evaluate(DBRProgressMonitor monitor) throws InvocationTargetException, InterruptedException;

    boolean cancel() throws InvocationTargetException;

    Object getFamily();
}
