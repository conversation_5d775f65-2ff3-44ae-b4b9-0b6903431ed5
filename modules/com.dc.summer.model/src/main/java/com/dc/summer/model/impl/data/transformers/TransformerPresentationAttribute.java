
package com.dc.summer.model.impl.data.transformers;

import com.dc.summer.model.DBPDataKind;
import com.dc.summer.model.data.DBDAttributeBinding;
import com.dc.summer.model.impl.struct.AbstractAttribute;

/**
 * TransformerPresentationAttribute
 */
public class TransformerPresentationAttribute extends AbstractAttribute {

    private final DBPDataKind dataKind;

    public TransformerPresentationAttribute(
        DBDAttributeBinding attribute,
        String typeName,
        int typeId,
        DBPDataKind dataKind)
    {
        super(
            attribute.getName(),
            typeName,
            typeId,
            attribute.getOrdinalPosition(),
            attribute.getMaxLength(),
            attribute.getScale(),
            attribute.getPrecision(),
            attribute.isRequired(),
            attribute.isAutoGenerated());
        this.dataKind = dataKind;
    }

    @Override
    public DBPDataKind getDataKind() {
        return dataKind;
    }
}
