

package com.dc.summer.model.runtime;

import java.lang.reflect.InvocationTargetException;

/**
 * DBRRunnableContext
 */
public interface DBRRunnableContext
{
    /**
     * Runs blocking process.
     * If any exception will occure when running this process then it'll written in log
     * @param runnable runnable implementation
     */
    void run(
        boolean fork,
        boolean cancelable,
        DBRRunnableWithProgress runnable)
        throws InvocationTargetException, InterruptedException;
}
