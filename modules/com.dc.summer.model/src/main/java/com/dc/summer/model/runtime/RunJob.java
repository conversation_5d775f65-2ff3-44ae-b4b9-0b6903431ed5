package com.dc.summer.model.runtime;

import com.dc.summer.Log;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.ListenerList;
import org.eclipse.core.runtime.jobs.IJobChangeListener;

@Data
public abstract class RunJob {

    private static final DBRProgressMonitor MONITOR = new LoggingProgressMonitor(Log.getLog(RunJob.class));

    @Getter(AccessLevel.NONE)
    private final ListenerList<IJobChangeListener> listeners = new ListenerList<>(ListenerList.IDENTITY);

    private String name;

    private Thread thread;
    public static final int RUNNING = 0x04;

    protected RunJob(String name) {
        this.name = name;
        this.thread = Thread.currentThread();
    }

    public final void schedule(String taskName) {
        this.schedule(0L, taskName);
    }

    public final void schedule() {
        this.schedule(0L);
    }


    public final void schedule(long delay) {
        schedule(delay, null);
    }

    public final void schedule(long delay, String taskName) {
        if (delay > 0) {
            try {
                Thread.sleep(delay);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
        DBRProgressMonitor monitor;
        if (taskName == null) {
            monitor = MONITOR;
        } else {
            monitor = new LoggingProgressMonitor(Log.getLog(RunJob.class));
            monitor.getNestedMonitor().setTaskName(taskName);
        }
        this.schedule(monitor);
        if (!listeners.isEmpty()) {
            listeners.forEach(listener -> listener.done(null));
        }
    }

    protected abstract IStatus schedule(DBRProgressMonitor progressMonitor);

    public void join() throws InterruptedException {

    }

    public boolean cancel() {
        return true;
    }

    public void addJobChangeListener(IJobChangeListener listener) {
        this.listeners.add(listener);
    }

    public void setSystem(boolean b) {

    }

    public void setUser(boolean b) {

    }

    protected void canceling() {

    }

    public boolean belongsTo(Object family) {
        return false;
    }

    protected int getState() {
        return RUNNING;
    }

}
