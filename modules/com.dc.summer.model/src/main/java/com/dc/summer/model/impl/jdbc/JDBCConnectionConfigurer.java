
package com.dc.summer.model.impl.jdbc;

import com.dc.summer.DBException;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.runtime.DBRProgressMonitor;

import java.sql.Connection;
import java.util.Properties;

/**
 * JDBCConnectionConfigurer
 */
public interface JDBCConnectionConfigurer {

    void beforeConnection(DBRProgressMonitor monitor, DBPConnectionConfiguration connectionInfo, Properties connectProps) throws DBCException;

    void afterConnection(DBRProgressMonitor monitor, DBPConnectionConfiguration connectionInfo, Properties connectProps, Connection connection, Throwable error) throws DBException;

}
