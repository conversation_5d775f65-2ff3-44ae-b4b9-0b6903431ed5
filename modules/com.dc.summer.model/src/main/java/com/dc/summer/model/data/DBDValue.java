

package com.dc.summer.model.data;

/**
 * DBDValue
 */
public interface DBDValue extends DBDObject {

    Object getRawValue();

    /**
     * check this value is NULL
     * @return true for NULL values
     */
    boolean isNull();

    /**
     * Checks if this value was modified on client-side.
     */
    boolean isModified();

    /**
     * Releases allocated resources. Resets to original value
     */
    void release();

}
