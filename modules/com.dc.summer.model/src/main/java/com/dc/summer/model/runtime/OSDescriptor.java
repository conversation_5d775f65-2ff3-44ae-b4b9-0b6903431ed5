

package com.dc.summer.model.runtime;

import lombok.ToString;

@ToString
/**
 * Operating system descriptor
 */
public class OSDescriptor {
    private final String family;
    private final String arch;

    public OSDescriptor(String family, String arch) {
        this.family = family;
        this.arch = arch;
    }

    public boolean matches(OSDescriptor os) {
        if (!family.equals(os.family)) {
            return false;
        }
        return arch == null || (os.arch != null && arch.equals(os.arch));
    }
}
