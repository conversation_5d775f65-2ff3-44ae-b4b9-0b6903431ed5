
package com.dc.summer.model.security;

import java.util.ArrayList;
import java.util.List;

/**
 * Auth provider descriptor
 */
public class SMAuthProviderDescriptor {

    private String id;
    private String label;
    private String description;
    private String icon;
    private List<SMAuthCredentialsProfile> credentialProfiles;
    private List<SMAuthProviderCustomConfiguration> customConfigurations;

    public SMAuthProviderDescriptor() {
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public List<SMAuthCredentialsProfile> getCredentialProfiles() {
        return credentialProfiles;
    }

    public void setCredentialProfiles(List<SMAuthCredentialsProfile> credentialProfiles) {
        this.credentialProfiles = credentialProfiles;
    }

    public List<SMAuthProviderCustomConfiguration> getCustomConfigurations() {
        return customConfigurations;
    }

    public void setCustomConfigurations(List<SMAuthProviderCustomConfiguration> customConfigurations) {
        this.customConfigurations = customConfigurations;
    }

    public void addCustomConfiguration(SMAuthProviderCustomConfiguration customConfiguration) {
        if (this.customConfigurations == null) {
            this.customConfigurations = new ArrayList<>();
        }
        this.customConfigurations.add(customConfiguration);
    }

}
