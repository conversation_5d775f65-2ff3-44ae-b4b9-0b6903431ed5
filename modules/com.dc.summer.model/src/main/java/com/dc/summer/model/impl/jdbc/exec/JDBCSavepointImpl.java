
package com.dc.summer.model.impl.jdbc.exec;

import com.dc.summer.Log;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.exec.DBCSavepoint;
import com.dc.summer.model.impl.jdbc.JDBCExecutionContext;

import java.sql.SQLException;
import java.sql.Savepoint;

/**
 * Savepoint
 */
public class JDBCSavepointImpl implements DBCSavepoint, Savepoint {

    private static final Log log = Log.getLog(JDBCSavepointImpl.class);

    private JDBCExecutionContext context;
    private Savepoint original;

    public JDBCSavepointImpl(JDBCExecutionContext context, Savepoint savepoint)
    {
        this.context = context;
        this.original = savepoint;
    }

    @Override
    public int getId()
    {
        try {
            return original.getSavepointId();
        }
        catch (SQLException e) {
            log.error(e);
            return 0;
        }
    }

    @Override
    public String getName()
    {
        try {
            return original.getSavepointName();
        }
        catch (SQLException e) {
            log.error(e);
            return null;
        }
    }

    @Override
    public DBCExecutionContext getContext()
    {
        return context;
    }

    @Override
    public int getSavepointId()
        throws SQLException
    {
        return original.getSavepointId();
    }

    @Override
    public String getSavepointName()
        throws SQLException
    {
        return original.getSavepointName();
    }

    public Savepoint getOriginal()
    {
        return original;
    }
}
