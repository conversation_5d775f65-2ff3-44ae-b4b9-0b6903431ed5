
package com.dc.summer.model.sql.parser;

import com.dc.code.NotNull;

/**
 * Represents information about the predicate describing one dialect feature which requires special handling during SQL parsing
 */
public interface SQLTokenPredicate {

    /**
     * Action to perform during parse on condition match
     */
    @NotNull
    SQLParserActionKind getActionKind();

    /**
     * Maximum lengths of corresponding suffixes under condition. Zero when prefix is enough to trigger the action.
     */
    int getMaxSuffixLength();
}
