

package com.dc.summer.model.exec;

import com.dc.summer.model.impl.jdbc.JDBCExecutionContext;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Execution purpose.
 *
 * Each query which executed withing application have some purpose.
 * Some of queries are instantiated by user, some are executed internally to obtain metadata, etc.
 * This enum defines different query purposes.
 *
 * Note: for sure, we can't detect ALL executed queries. Some of them are executed by drivers internally,
 * some are executed by plugins and not reported to query manager.
 */
public enum DBCExecutionPurpose {

    USER(0, "User", true),               // User query
    USER_FILTERED(1, "UserFiltered", true),      // User query with additional filters
    USER_SCRIPT(2, "UserScript", true),        // User script query
    UTIL(3, "Util", false),              // Utility query (utility method initialized by user)
    META(4, JDBCExecutionContext.TYPE_METADATA, false),              // Metadata query, processed by data source providers internally
    MAIN(6, JDBCExecutionContext.TYPE_MAIN, false),

    ;

    private final int id;
    private final String title;
    private final boolean user;

    DBCExecutionPurpose(int id, String title, boolean user) {
        this.id = id;
        this.title = title;
        this.user = user;
    }

    public int getId() {
        return id;
    }

    public String getTitle() {
        return title;
    }

    public boolean isUser() {
        return user;
    }           // Metadata modifications (DDL)

    private static final Map<Integer, DBCExecutionPurpose> INTEGER_DBC_EXECUTION_PURPOSE_MAP = Arrays.stream(DBCExecutionPurpose.values()).collect(Collectors.toMap(DBCExecutionPurpose::getId, Function.identity()));

    public static DBCExecutionPurpose getById(int id) {
        DBCExecutionPurpose purpose = INTEGER_DBC_EXECUTION_PURPOSE_MAP.get(id);
        if (purpose != null) {
            return purpose;
        }
        return USER;
    }

    private static final Map<String, DBCExecutionPurpose> STRING_DBC_EXECUTION_PURPOSE_MAP = Arrays.stream(DBCExecutionPurpose.values()).collect(Collectors.toMap(DBCExecutionPurpose::getTitle, Function.identity()));

    public static DBCExecutionPurpose getByTitle(String title) {
        DBCExecutionPurpose purpose = STRING_DBC_EXECUTION_PURPOSE_MAP.get(title.split(" ")[0]);
        if (purpose != null) {
            return purpose;
        }
        return USER;
    }

    public static DBCExecutionPurpose getByContext(DBCExecutionContext context) {
        return getByTitle(context.getContextName());
    }

}
