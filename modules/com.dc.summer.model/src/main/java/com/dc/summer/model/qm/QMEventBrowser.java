

package com.dc.summer.model.qm;

import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.model.qm.filters.QMEventCriteria;
import com.dc.summer.model.runtime.DBRProgressMonitor;

/**
 * Query manager history
 */
public interface QMEventBrowser {

    QMEventCursor getQueryHistoryCursor(
        @NotNull DBRProgressMonitor monitor,
        @NotNull QMEventCriteria criteria, QMEventFilter filter)
        throws DBException;

}
