
package com.dc.summer.model.impl.jdbc.cache;

import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.exec.jdbc.JDBCStatement;
import com.dc.summer.model.messages.ModelMessages;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.struct.DBSObject;

import java.sql.SQLException;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Struct cache with ability to load/search single object by name.
 */
public abstract class JDBCStructLookupCache<OWNER extends DBSObject, OBJECT extends DBSObject, CHILD extends DBSObject>
    extends JDBCStructCache<OWNER, OBJECT, CHILD>
    implements JDBCObjectLookup<OWNER, OBJECT>
{
    private final Set<String> missingNames = new HashSet<>();

    public JDBCStructLookupCache(Object objectNameColumn) {
        super(objectNameColumn);
    }

    @Override
    public OBJECT getObject(@NotNull DBRProgressMonitor monitor, @NotNull OWNER owner, @NotNull String name)
        throws DBException
    {
        OBJECT cachedObject = getCachedObject(name);
        if (cachedObject != null) {
            return cachedObject;
        }
        if (isFullyCached() || owner.getDataSource() == null || !owner.getDataSource().getContainer().isConnected() || missingNames.contains(name)) {
            return null;
        }
        // Now cache just one object
        OBJECT object = reloadObject(monitor, owner, null, name);
        if (object != null) {
            cacheObject(object);
        } else {
            // Not found!
            missingNames.add(name);
        }
        return object;
    }

    public OBJECT refreshObject(@NotNull DBRProgressMonitor monitor, @NotNull OWNER owner, @NotNull OBJECT oldObject)
        throws DBException
    {
        String objectName = oldObject.getName();
        if (!isFullyCached()) {
            this.loadObjects(monitor, owner);
        } else {
            OBJECT newObject = this.reloadObject(monitor, owner, oldObject, null);
            if (isChildrenCached(oldObject)) {
                clearChildrenCache(oldObject);
            }
            if (newObject != null) {
                deepCopyCachedObject(newObject, oldObject);
            } else {
                removeObject(oldObject, false);
            }
//            removeObject(oldObject, false);
//            if (newObject != null) {
//                cacheObject(newObject);
//            }
            return oldObject;
        }
        return getCachedObject(objectName);
    }


    protected OBJECT reloadObject(@NotNull DBRProgressMonitor monitor, @NotNull OWNER owner, @Nullable OBJECT object, @Nullable String objectName)
        throws DBException
    {
        DBPDataSource dataSource = owner.getDataSource();
        if (dataSource == null) {
            throw new DBException(ModelMessages.error_not_connected_to_database);
        }
        try (JDBCSession session = DBUtils.openMetaSession(monitor, owner,
            object == null ?
                "Load object '" + objectName + "' from " + owner.getName() :
                "Reload object '" + object + "' from " + owner.getName()))
        {
            beforeCacheLoading(session, owner);
            try (JDBCStatement dbStat = prepareLookupStatement(session, owner, object, objectName)) {
                dbStat.setFetchSize(1);
                dbStat.executeStatement();
                JDBCResultSet dbResult = dbStat.getResultSet();
                if (dbResult != null) {
                    try {
                        while (dbResult.next()) {
                            OBJECT remoteObject = fetchObject(session, owner, dbResult);
                            if (isValidObject(monitor, owner, remoteObject)) {
                                return remoteObject;
                            }
                        }
                    } finally {
                        dbResult.close();
                    }
                }
                return null;
            } finally {
                afterCacheLoading(session, owner);
            }
        } catch (SQLException ex) {
            throw new DBException("Error loading object metadata from database", ex, dataSource);
        }
    }

    @NotNull
    @Override
    protected JDBCStatement prepareObjectsStatement(@NotNull JDBCSession session, @NotNull OWNER owner)
        throws SQLException
    {
        return prepareLookupStatement(session, owner, null, null);
    }

    @Override
    public void setCache(List<OBJECT> objects) {
        super.setCache(objects);
        this.missingNames.clear();
    }

    @Override
    public void clearCache() {
        super.clearCache();
        this.missingNames.clear();
    }

}
