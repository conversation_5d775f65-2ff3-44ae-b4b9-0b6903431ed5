
package com.dc.summer.model.impl.jdbc;

import com.dc.summer.Log;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.DBPTransactionIsolation;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.summer.model.impl.AbstractDataSourceInfo;
import com.dc.summer.model.impl.sql.ChangeTableDataStatement;
import com.dc.summer.model.impl.struct.RelationalObjectType;
import com.dc.summer.model.messages.ModelMessages;
import com.dc.summer.model.struct.DBSObjectType;
import com.dc.utils.CommonUtils;
import org.osgi.framework.Version;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * JDBCDataSourceInfo
 */
public class JDBCDataSourceInfo extends AbstractDataSourceInfo implements ChangeTableDataStatement
{
    private static final Log log = Log.getLog(JDBCDataSourceInfo.class);

    public static final String TERM_SCHEMA = ModelMessages.model_jdbc_Schema;
    public static final String TERM_PROCEDURE = ModelMessages.model_jdbc_Procedure;
    public static final String TERM_CATALOG = ModelMessages.model_jdbc_Database;

    private boolean readOnly;
    private boolean readOnlyData;
    private boolean readOnlyMetaData;
    private String databaseProductName;
    private String databaseProductVersion;
    private String driverName;
    private String driverVersion;
    private Version databaseVersion;
    private String schemaTerm;
    private String procedureTerm;
    private String catalogTerm;

    private boolean supportsTransactions;
    private List<DBPTransactionIsolation> supportedIsolations;

    private boolean supportsReferences = true;
    private boolean supportsIndexes = true;
    private boolean supportsStoredCode = true;
    private boolean supportsBatchUpdates = false;
    private boolean supportsScroll;
    private boolean supportsViews = true;
    private boolean queryComment;

    public JDBCDataSourceInfo(DBPDataSourceContainer container)
    {
        this.readOnly = false;
        this.databaseProductName = "?"; //$NON-NLS-1$
        this.databaseProductVersion = "?"; //$NON-NLS-1$
        this.driverName = container.getDriver().getName(); //$NON-NLS-1$
        this.driverVersion = "?"; //$NON-NLS-1$
        this.databaseVersion = new Version(0, 0, 0);
        this.schemaTerm = TERM_SCHEMA;
        this.procedureTerm = TERM_PROCEDURE;
        this.catalogTerm = TERM_CATALOG;
        this.supportsBatchUpdates = false;

        this.supportsTransactions = false;
        this.supportedIsolations = new ArrayList<>();
        this.supportedIsolations.add(0, JDBCTransactionIsolation.NONE);
        this.supportsScroll = true;
    }

    public JDBCDataSourceInfo(JDBCDatabaseMetaData metaData)
    {
        // 说忽略DatabaseMetaData.isReadonly()结果。它在某些驱动程序中坏了（总是正确的），例如在Reshift中。
        if (!isIgnoreReadOnlyFlag()) {
            try {
                this.readOnly = metaData.isReadOnly();
            } catch (Throwable e) {
                log.debug(e.getMessage());
                this.readOnly = false;
            }
        } else {
            this.readOnly = false;
        }
        try {
            // 获取数据库产品名称
            this.databaseProductName = metaData.getDatabaseProductName();
        } catch (Throwable e) {
            log.debug(e.getMessage());
            this.databaseProductName = "?"; //$NON-NLS-1$
        }
        try {
            // 获取数据库产品版本
            this.databaseProductVersion = metaData.getDatabaseProductVersion();
        } catch (Throwable e) {
            log.debug(e.getMessage());
            this.databaseProductVersion = "?"; //$NON-NLS-1$
        }
        try {
            // 获取驱动程序名
            String name = metaData.getDriverName();
            if (name != null) {
                this.driverName = name;
            }
        } catch (Throwable e) {
            log.debug(e.getMessage());
            this.driverName = "?"; //$NON-NLS-1$
        }
        try {
            // 获取驱动程序版本
            this.driverVersion = metaData.getDriverVersion();
        } catch (Throwable e) {
            log.debug(e.getMessage());
            this.driverVersion = "?"; //$NON-NLS-1$
        }
        try {
            // 注册一个 org.osgi.framework 的版本。
            databaseVersion = new Version(metaData.getDatabaseMajorVersion(), metaData.getDatabaseMinorVersion(), 0);
        } catch (Throwable e) {
            try {
                databaseVersion = new Version(databaseProductVersion);
            } catch (IllegalArgumentException e1) {
                log.debug("Can't determine database version. Use default");
                databaseVersion = new Version(0, 0, 0);
            }
        }
        try {
            // 模式术语
            this.schemaTerm = makeTermString(metaData.getSchemaTerm(), TERM_SCHEMA);
        } catch (Throwable e) {
            log.debug(e.getMessage());
            this.schemaTerm = TERM_SCHEMA;
        }
        try {
            // 程序术语
            this.procedureTerm = makeTermString(metaData.getProcedureTerm(), TERM_PROCEDURE);
        } catch (Throwable e) {
            log.debug(e.getMessage());
            this.procedureTerm = TERM_PROCEDURE;
        }
        try {
            // 目录术语
            this.catalogTerm = makeTermString(metaData.getCatalogTerm(), TERM_CATALOG);
        } catch (Throwable e) {
            log.debug(e.getMessage());
            this.catalogTerm = TERM_CATALOG;
        }
        try {
            // 支持批量更新
            supportsBatchUpdates = metaData.supportsBatchUpdates();
        } catch (Throwable e) {
            log.debug(e.getMessage());
        }

        try {
            // 支持事务处理
            supportsTransactions = metaData.supportsTransactions();
        } catch (Throwable e) {
            log.debug(e.getMessage());
            supportsTransactions = true;
        }

        // 支持事务隔离级别
        supportedIsolations = new ArrayList<>();
        if (supportsTransactions) {
            try {
                for (JDBCTransactionIsolation txi : JDBCTransactionIsolation.values()) {
                    if (metaData.supportsTransactionIsolationLevel(txi.getCode())) {
                        supportedIsolations.add(txi);
                    }
                }
            } catch (Throwable e) {
                log.debug(e.getMessage());
            }
            if (!supportedIsolations.contains(JDBCTransactionIsolation.NONE)) {
                supportedIsolations.add(0, JDBCTransactionIsolation.NONE);
            }
            addCustomTransactionIsolationLevels(supportedIsolations);
        }

        supportsScroll = true;
    }

    protected void addCustomTransactionIsolationLevels(List<DBPTransactionIsolation> isolations) {
        // to be overrided in implementors
    }

    // Says to ignore DatabaseMetaData.isReadonly() results. It is broken in some drivers (always true), e.g. in Reshift.
    protected boolean isIgnoreReadOnlyFlag() {
        return true;
    }

    private String makeTermString(String term, String defTerm)
    {
        return CommonUtils.isEmpty(term) ? defTerm : CommonUtils.capitalizeWord(term.toLowerCase());
    }

    @Override
    public boolean isReadOnlyData()
    {
        return readOnly || readOnlyData;
    }

    protected void setReadOnlyData(boolean readOnly) {
        this.readOnlyData = readOnly;
    }

    @Override
    public boolean isReadOnlyMetaData()
    {
        return readOnly || readOnlyMetaData;
    }

    protected void setReadOnlyMetaData(boolean readOnlyMetaData) {
        this.readOnlyMetaData = readOnlyMetaData;
    }

    @Override
    public String getDatabaseProductName()
    {
        return databaseProductName;
    }

    @Override
    public String getDatabaseProductVersion()
    {
        return databaseProductVersion;
    }

    @Override
    public Version getDatabaseVersion() {
        return databaseVersion;
    }

    @Override
    public String getDriverName()
    {
        return driverName;
    }

    @Override
    public String getDriverVersion()
    {
        return driverVersion;
    }

    @Override
    public String getSchemaTerm()
    {
        return schemaTerm;
    }

    @Override
    public String getProcedureTerm()
    {
        return procedureTerm;
    }

    @Override
    public String getCatalogTerm()
    {
        return catalogTerm;
    }

    @Override
    public boolean supportsTransactions()
    {
        return supportsTransactions;
    }

    @Override
    public boolean supportsSavepoints()
    {
        return false;
    }

    @Override
    public boolean supportsReferentialIntegrity()
    {
        return supportsReferences;
    }

    public void setSupportsReferences(boolean supportsReferences)
    {
        this.supportsReferences = supportsReferences;
    }

    @Override
    public boolean supportsIndexes()
    {
        return supportsIndexes;
    }

    public void setSupportsIndexes(boolean supportsIndexes)
    {
        this.supportsIndexes = supportsIndexes;
    }


    public boolean supportsViews() {
        return supportsViews;
    }

    public void setSupportsViews(boolean supportsViews) {
        this.supportsViews = supportsViews;
    }

    @Override
    public boolean supportsStoredCode() {
        return supportsStoredCode;
    }

    public void setSupportsStoredCode(boolean supportsStoredCode) {
        this.supportsStoredCode = supportsStoredCode;
    }

    @Override
    public Collection<DBPTransactionIsolation> getSupportedTransactionsIsolation()
    {
        return supportedIsolations;
    }

    @Override
    public boolean supportsResultSetLimit() {
        return true;
    }

    @Override
    public boolean supportsResultSetScroll()
    {
        return supportsScroll;
    }

    @Override
    public boolean isDynamicMetadata() {
        return false;
    }

    @Override
    public boolean supportsMultipleResults() {
        return false;
    }

    @Override
    public boolean isMultipleResultsFetchBroken() {
        return false;
    }

    @Override
    public DBSObjectType[] getSupportedObjectTypes() {
        return new DBSObjectType[] {
            RelationalObjectType.TYPE_TABLE,
            RelationalObjectType.TYPE_VIEW,
            RelationalObjectType.TYPE_TABLE_COLUMN,
            RelationalObjectType.TYPE_VIEW_COLUMN,
            RelationalObjectType.TYPE_INDEX,
            RelationalObjectType.TYPE_CONSTRAINT,
            RelationalObjectType.TYPE_PROCEDURE,
            RelationalObjectType.TYPE_SEQUENCE,
            RelationalObjectType.TYPE_TRIGGER,
            RelationalObjectType.TYPE_DATA_TYPE
        };
    }

    public void setSupportsResultSetScroll(boolean supportsScroll)
    {
        this.supportsScroll = supportsScroll;
    }

    @Override
    public boolean supportsBatchUpdates()
    {
        return supportsBatchUpdates;
    }

    @Override
    public String getCheckTableExistenceSql(String name) {
        return "select 1 from " + name + " where 1<>1";
    }

    @Override
    public String getFormatColumnName(String column) {
        return super.getFormatColumnName(column);
    }

    @Override
    public boolean supportDDLCallable() {
        return false;
    }

    @Override
    public String generateTableUpdateBegin(String tableName) {
        return "UPDATE " + tableName;
    }

    @Override
    public String generateTableUpdateSet() {
        return "SET ";
    }

    @Override
    public String generateTableDeleteFrom(String tableName) {
        return "DELETE FROM " + tableName;
    }

    @Override
    public boolean isQueryComment() {
        return queryComment;
    }

    @Override
    public void setQueryComment(boolean queryComment) {
        this.queryComment = queryComment;
    }
}
