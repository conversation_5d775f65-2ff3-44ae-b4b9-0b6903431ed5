

package com.dc.summer.model.struct;

import com.dc.summer.model.DBPNamedObject;

import java.util.Locale;

/**
 * DBSEntityConstraintType
 */
public class DBSActionTiming implements DBPNamedObject
{
    public static final DBSActionTiming BEFORE = new DBSActionTiming("BEFORE");
    public static final DBSActionTiming AFTER = new DBSActionTiming("AFTER");
    public static final DBSActionTiming INSTEAD = new DBSActionTiming("INSTEAD");
    public static final DBSActionTiming UNKNOWN = new DBSActionTiming("UNKNOWN");

    private final String name;

    protected DBSActionTiming(String name)
    {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public String toString()
    {
        return getName();
    }

    public static DBSActionTiming getByName(String name)
    {
        if (name.toUpperCase(Locale.ENGLISH).equals(BEFORE.getName())) {
            return BEFORE;
        } else if (name.toUpperCase(Locale.ENGLISH).equals(AFTER.getName())) {
            return AFTER;
        } else {
            return UNKNOWN;
        }
    }
}