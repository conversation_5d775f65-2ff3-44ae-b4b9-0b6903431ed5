
package com.dc.summer.model.task;

import com.dc.code.Nullable;

/**
 * Task execution listener
 */
public interface DBTTaskExecutionListener {

    void taskStarted(@Nullable DBTTask task);

    void taskFinished(@Nullable DBTTask task, @Nullable Object result, @Nullable Throwable error, @Nullable Object settings);

    void subTaskFinished(@Nullable DBTTask task, @Nullable Throwable error, @Nullable Object settings);

}
