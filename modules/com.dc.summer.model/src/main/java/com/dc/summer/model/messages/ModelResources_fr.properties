error_not_connected_to_database = Pas de connexion \u00E0 la base de donn\u00E9es
model_connection_events_event_after_connect = Apr\u00E8s la connexion
model_connection_events_event_after_disconnect = Apr\u00E8s la d\u00E9connexion
model_connection_events_event_before_connect = Avant la connexion
model_connection_events_event_before_disconnect = Avant la d\u00E9connexion

dialog_connection_wizard_start_connection_monitor_close = Fermer la connexion
dialog_connection_wizard_start_connection_monitor_connected = Connect\u00E9 depuis ({0} ms)
dialog_connection_wizard_start_connection_monitor_start = Obtenir la connexion
dialog_connection_wizard_start_connection_monitor_subtask_test = Tester la connexion
dialog_connection_wizard_start_connection_monitor_success = Succ\u00E8s
dialog_connection_wizard_start_connection_monitor_thread = Tester la connexion \u00E0 la source de donn\u00E9es
dialog_connection_wizard_start_dialog_error_message = Erreur de connectivit\u00E9 \u00E0 la base de donn\u00E9es

controls_querylog__ms = \ ms
controls_querylog_action_clear_log = Vider le journal
controls_querylog_action_copy = Copier
controls_querylog_action_copy_all_fields = Copier tous les champs
controls_querylog_action_select_all = Tout s\u00E9lectionner
controls_querylog_column_connection_name = Source de donn\u00E9es
controls_querylog_column_connection_tooltip = Connexion \u00E0 laquelle appartient cet \u00E9v\u00E8nement de base de donn\u00E9es
controls_querylog_column_context_name = Connexion
controls_querylog_column_context_tooltip = Connexion physique r\u00E9elle affect\u00E9e par cet \u00E9v\u00E8nement
controls_querylog_column_duration_name = Dur\u00E9e
controls_querylog_column_duration_tooltip = Dur\u00E9e d'ex\u00E9cution de l'op\u00E9ration
controls_querylog_column_result_name = R\u00E9sultat
controls_querylog_column_result_tooltip = R\u00E9sultat d'ex\u00E9cution
controls_querylog_column_rows_name = Lignes
controls_querylog_column_rows_tooltip = Nombre de lignes trait\u00E9es par l'instruction
controls_querylog_column_text_name = Texte
controls_querylog_column_text_tooltip = Description de l'instruction SQL
controls_querylog_column_time_name = Heure
controls_querylog_column_time_tooltip = Heure \u00E0 laquelle l'instruction a \u00E9t\u00E9 ex\u00E9cut\u00E9e
controls_querylog_column_type_name = Type
controls_querylog_column_type_tooltip = Type d'\u00E9v\u00E8nement
controls_querylog_commit = Appliquer (Commit)
controls_querylog_connected_to = Connect\u00E9 \u00E0 "
controls_querylog_disconnected_from = D\u00E9connect\u00E9 de "
controls_querylog_error = Erreur [
controls_querylog_format_minutes = {0} Min {1} sec 
controls_querylog_job_refresh = Recharger le journal d'\u00E9v\u00E8nements du gestionnaire de requ\u00EAtes
controls_querylog_label_result = R\u00E9sultat
controls_querylog_label_text = Texte
controls_querylog_label_time = Heure
controls_querylog_label_type = Type
controls_querylog_rollback = Retour arri\u00E8re (Rollback)
controls_querylog_savepoint = Point de sauvegardeSavepoint
controls_querylog_script = Script
controls_querylog_shell_text = Vue 
controls_querylog_success = Succ\u00E8s
controls_querylog_transaction = Transaction