
package com.dc.summer.model.impl.edit;

import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.sql.SQLUtils;

/**
 * Comment action
 */
public class SQLDatabasePersistActionComment extends SQLDatabasePersistAction {

    public SQLDatabasePersistActionComment(DBPDataSource dataSource, String comment) {
        super("Comment",
            SQLUtils.getDialectFromDataSource(dataSource).getSingleLineComments()[0] + " " + comment,
            ActionType.COMMENT,
            false);
    }
}
