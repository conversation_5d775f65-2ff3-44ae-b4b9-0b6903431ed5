
package com.dc.parser.ext.hetu.statement.ddl;

import com.dc.parser.model.statement.ddl.ExistsCreateTableStatement;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import com.dc.parser.model.segment.ddl.table.CreateTableOptionSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.ddl.CreateTableStatement;
import com.dc.parser.ext.hetu.statement.HetuStatement;

import java.util.Optional;

/**
 * Hetu create table statement.
 */
@RequiredArgsConstructor
@Getter
@Setter
public final class HetuCreateTableStatement extends CreateTableStatement implements HetuStatement, ExistsCreateTableStatement {
    
    private final boolean ifNotExists;
    
    private SimpleTableSegment likeTable;
    
    private CreateTableOptionSegment createTableOptionSegment;
    
    public Optional<SimpleTableSegment> getLikeTable() {
        return Optional.ofNullable(likeTable);
    }
    
    public Optional<CreateTableOptionSegment> getCreateTableOptionSegment() {
        return Optional.ofNullable(createTableOptionSegment);
    }

    @Override
    public boolean isIfNotExists() {
        return ifNotExists;
    }
}
