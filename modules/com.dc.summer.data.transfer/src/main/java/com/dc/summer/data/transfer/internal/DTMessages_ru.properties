data_transfer_wizard_init_column_description = \u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435
data_transfer_wizard_init_column_exported = \u042D\u043A\u0441\u043F\u043E\u0440\u0442
data_transfer_wizard_init_description = \u041F\u043E\u0434\u0442\u0432\u0435\u0440\u0434\u0438\u0442\u0435 \u0446\u0435\u043B\u044C \u0438 \u0444\u043E\u0440\u043C\u0430\u0442 \u0442\u0440\u0430\u043D\u0441\u0444\u0435\u0440\u0430
data_transfer_wizard_init_name = \u0426\u0435\u043B\u0438 \u044D\u043A\u0441\u043F\u043E\u0440\u0442\u0430
data_transfer_wizard_init_title = \u0424\u043E\u0440\u043C\u0430\u0442 \u044D\u043A\u0441\u043F\u043E\u0440\u0442\u0430
data_transfer_wizard_job_container_name = \u041F\u0440\u0435\u043E\u0431\u0440\u0430\u0437\u043E\u0432\u0430\u0442\u044C \u0434\u0430\u043D\u043D\u044B\u0435 \u0438\u0437 "{0}" \u0432 "{1}"
data_transfer_wizard_job_name = \u042D\u043A\u0441\u043F\u043E\u0440\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0434\u0430\u043D\u043D\u044B\u0435
data_transfer_wizard_job_task_export = \u042D\u043A\u0441\u043F\u043E\u0440\u0442 \u0434\u0430\u043D\u043D\u044B\u0445
data_transfer_wizard_job_task_export_table_data = \u042D\u043A\u0441\u043F\u043E\u0440\u0442 \u0434\u0430\u043D\u043D\u044B\u0445 \u0442\u0430\u0431\u043B\u0438\u0446\u044B
data_transfer_wizard_job_task_retrieve = \u0427\u0442\u0435\u043D\u0438\u0435 \u0447\u0438\u0441\u043B\u0430 \u0441\u0442\u0440\u043E\u043A
data_transfer_wizard_mappings_name = \u0417\u0430\u0434\u0430\u0442\u044C \u0441\u043E\u043E\u0442\u0435\u0442\u0441\u0442\u0432\u0438\u044F
data_transfer_wizard_restricted_title = \u0414\u0435\u0439\u0441\u0442\u0432\u0438\u0435 \u0437\u0430\u043F\u0440\u0435\u0449\u0435\u043D\u043E
data_transfer_wizard_restricted_description = \u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F \u0442\u0440\u0430\u043D\u0441\u0444\u0435\u0440\u0430 \u0434\u0430\u043D\u043D\u044B\u0445 \u0432 \u0431\u0430\u0437\u0443 ''{0}'' \u0437\u0430\u043F\u0440\u0435\u0449\u0451\u043D\u044B \u043D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0430\u043C\u0438 \u0441\u043E\u0435\u0434\u0438\u043D\u0435\u043D\u0438\u044F
data_transfer_wizard_output_checkbox_compress = \u0421\u0436\u0430\u0442\u0438\u0435
data_transfer_wizard_output_checkbox_new_connection = \u041E\u0442\u043A\u0440\u044B\u0442\u044C \u043D\u043E\u0432\u043E\u0435 \u0441\u043E\u0435\u0434\u0438\u043D\u0435\u043D\u0438\u0435
data_transfer_wizard_output_checkbox_select_row_count = \u0412\u044B\u0431\u0438\u0440\u0430\u0442\u044C \u0447\u0438\u0441\u043B\u043E \u0441\u0442\u0440\u043E\u043A
data_transfer_wizard_output_checkbox_selected_columns_only = \u0422\u043E\u043B\u044C\u043A\u043E \u0432\u044B\u0431\u0440\u0430\u043D\u043D\u044B\u0435 \u043A\u043E\u043B\u043E\u043D\u043A\u0438
data_transfer_wizard_output_checkbox_selected_rows_only = \u0422\u043E\u043B\u044C\u043A\u043E \u0432\u044B\u0431\u0440\u0430\u043D\u043D\u044B\u0435 \u0441\u0442\u0440\u043E\u043A\u0438
data_transfer_wizard_output_combo_extract_type_item_by_segments = \u041F\u043E \u0441\u0435\u0433\u043C\u0435\u043D\u0442\u0430\u043C
data_transfer_wizard_output_combo_extract_type_item_single_query = \u041E\u0434\u043D\u0438\u043C \u0437\u0430\u043F\u0440\u043E\u0441\u043E\u043C
data_transfer_wizard_output_description = \u041A\u043E\u043D\u0444\u0438\u0433\u0443\u0440\u0430\u0446\u0438\u044F \u044D\u043A\u0441\u043F\u043E\u0440\u0442\u0430
data_transfer_wizard_output_dialog_directory_message = \u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u043F\u0430\u043F\u043A\u0443 \u0434\u043B\u044F \u0446\u0435\u043B\u0435\u0432\u044B\u0445 \u0444\u0430\u0439\u043B\u043E\u0432
data_transfer_wizard_output_dialog_directory_text = \u0426\u0435\u043B\u0435\u0432\u0430\u044F \u043F\u0430\u043F\u043A\u0430
data_transfer_wizard_output_group_general = \u041E\u0431\u0449\u0435\u0435
data_transfer_wizard_output_group_progress = \u041F\u0440\u043E\u0433\u0440\u0435\u0441\u0441
data_transfer_wizard_output_label_copy_to_clipboard = \u041A\u043E\u043F\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0432 \u043A\u043B\u0438\u043F\u0431\u043E\u0440\u0434
data_transfer_wizard_output_label_use_single_file = \u041F\u0438\u0441\u0430\u0442\u044C \u0432 \u043E\u0434\u0438\u043D \u0444\u0430\u0439\u043B
data_transfer_wizard_output_label_use_single_file_tip = \u041F\u0438\u0441\u0430\u0442\u044C \u0432\u0441\u0435 \u043F\u043E\u0442\u043E\u043A\u0438 \u0432 \u043E\u0434\u0438\u043D \u043A\u043E\u043D\u0435\u0447\u043D\u044B\u0439 \u0444\u0430\u0439\u043B
data_transfer_wizard_output_label_directory = \u041F\u0430\u043F\u043A\u0430
data_transfer_wizard_output_label_encoding = \u041A\u043E\u0434\u0438\u0440\u043E\u0432\u043A\u0430
data_transfer_wizard_output_label_timestamp_pattern = \u0428\u0430\u0431\u043B\u043E\u043D \u0432\u0440\u0435\u043C\u0435\u043D\u043D\u043E\u0439 \u043E\u0442\u043C\u0435\u0442\u043A\u0438
data_transfer_wizard_output_label_extract_type = \u0427\u0442\u0435\u043D\u0438\u0435 \u0434\u0430\u043D\u043D\u044B\u0445
data_transfer_wizard_output_label_file_name_pattern = \u0428\u0430\u0431\u043B\u043E\u043D \u0438\u043C\u0435\u043D\u0438 \u0444\u0430\u0439\u043B\u0430
data_transfer_wizard_output_label_insert_bom = \u0412\u0441\u0442\u0430\u0432\u0438\u0442\u044C BOM
data_transfer_wizard_output_label_insert_bom_tooltip = BOM (Byte-Order-Mark) \u0438\u0441\u043F\u043E\u043B\u044C\u0437\u0443\u0435\u0442\u0441\u044F \u0434\u043B\u044F \u043C\u0430\u0440\u043A\u0438\u0440\u043E\u0432\u043A\u0438 \u043A\u043E\u0434\u0438\u0440\u043E\u0432\u043A\u0438 Unicode \u0438 \u0442\u0440\u0435\u0431\u0443\u0435\u0442\u0441\u044F \u043D\u0435\u043A\u043E\u0442\u043E\u0440\u044B\u043C\u0438 \u043F\u0440\u043E\u0433\u0440\u0430\u043C\u043C\u0430\u043C\u0438 (\u043D\u0430\u043F\u0440\u0438\u043C\u0435\u0440 MS Excel). \u0412 \u0442\u043E \u0436\u0435 \u0432\u0440\u0435\u043C\u044F \u043D\u0435 \u043F\u043E\u0434\u0434\u0435\u0440\u0438\u0432\u0430\u0435\u0442\u0441\u044F \u043D\u0435\u043A\u043E\u0442\u043E\u0440\u044B\u043C\u0438 \u0434\u0440\u0443\u0433\u0438\u043C\u0438 \u043F\u0440\u043E\u0433\u0440\u0430\u043C\u043C\u0430\u043C\u0438.
data_transfer_wizard_output_label_max_threads = \u0427\u0438\u0441\u043B\u043E \u043F\u043E\u0442\u043E\u043A\u043E\u0432
data_transfer_wizard_output_label_segment_size = \u0420\u0430\u0437\u043C\u0435\u0440 \u0441\u0435\u0433\u043C\u0435\u043D\u0442\u043E\u0432
data_transfer_wizard_output_error_empty_output_directory = \u041F\u0430\u043F\u043A\u0430 \u0432\u044B\u0432\u043E\u0434\u0430 \u043D\u0435 \u043C\u043E\u0436\u0435\u0442 \u0431\u044B\u0442\u044C \u043F\u0443\u0441\u0442\u043E\u0439
data_transfer_wizard_output_error_empty_output_filename = \u0428\u0430\u0431\u043B\u043E\u043D \u0438\u043C\u0435\u043D\u0438 \u0444\u0430\u0439\u043B\u0430 \u0432\u044B\u0432\u043E\u0434\u0430 \u043D\u0435 \u043C\u043E\u0436\u0435\u0442 \u0431\u044B\u0442\u044C \u043F\u0443\u0441\u0442\u044B\u043C
data_transfer_wizard_output_error_invalid_charset = \u041D\u0435\u0432\u0435\u0440\u043D\u0430\u044F \u043A\u043E\u0434\u0438\u0440\u043E\u0432\u043A\u0430
data_transfer_wizard_output_event_processor_configure = <a>\u041D\u0430\u0441\u0442\u0440\u043E\u0438\u0442\u044C</a>
data_transfer_wizard_output_event_processor_configure_title = \u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438 ''{0}''
data_transfer_wizard_output_event_processor_error_incomplete_configuration = \u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438 \u0434\u043B\u044F ''{0}'' \u043D\u0435\u043F\u043E\u043B\u043D\u044B\u0435
data_transfer_wizard_output_name = \u0412\u044B\u0432\u043E\u0434
data_transfer_wizard_output_title = \u0412\u044B\u0432\u043E\u0434
data_transfer_wizard_page_input_files_description = \u041A\u043E\u043D\u0444\u0438\u0433\u0443\u0440\u0430\u0446\u0438\u044F \u0432\u0445\u043E\u0434\u043D\u044B\u0445 \u0444\u0430\u0439\u043B\u043E\u0432 \u0438 \u043F\u0430\u043F\u043E\u043A
data_transfer_wizard_page_input_files_name = \u0412\u0445\u043E\u0434\u043D\u044B\u0435 \u0444\u0430\u0439\u043B\u044B
data_transfer_wizard_page_input_files_title = \u0412\u0445\u043E\u0434\u043D\u044B\u0435 \u0444\u0430\u0439\u043B\u044B
data_transfer_wizard_page_preview_description = \u041F\u0440\u043E\u0441\u043C\u043E\u0442\u0440 \u0442\u043E\u0433\u043E \u043A\u0430\u043A \u0434\u0430\u043D\u043D\u044B\u0435 \u0431\u0443\u0434\u0443\u0442 \u0438\u043C\u043F\u043E\u0440\u0442\u0438\u0440\u043E\u0432\u0430\u043D\u044B \u0432 \u0442\u0430\u0431\u043B\u0438\u0446\u0443
data_transfer_wizard_page_ddl_name = \u041F\u0440\u043E\u0441\u043C\u043E\u0442\u0440 \u0441\u0445\u0435\u043C\u044B
data_transfer_wizard_page_ddl_description = \u041F\u0440\u043E\u0441\u043C\u043E\u0442\u0440 \u0441\u0445\u0435\u043C\u044B (\u0435\u0441\u043B\u0438 \u043D\u043E\u0432\u044B\u0435 \u0442\u0430\u0431\u043B\u0438\u0446\u044B \u0438\u043B\u0438 \u043A\u043E\u043B\u043E\u043D\u043A\u0438 \u0434\u043E\u043B\u0436\u043D\u044B \u0431\u044B\u0442\u044C \u0441\u043E\u0437\u0434\u0430\u043D\u044B)
data_transfer_wizard_page_preview_name = \u041F\u0440\u043E\u0441\u043C\u043E\u0442\u0440 \u0434\u0430\u043D\u043D\u044B\u0445
data_transfer_wizard_page_preview_title = \u041F\u0440\u043E\u0441\u043C\u043E\u0442\u0440 \u0438\u043C\u043F\u043E\u0440\u0442\u0430 \u0434\u0430\u043D\u043D\u044B\u0445
data_transfer_wizard_producers_description = \u041A\u043E\u043D\u0444\u0438\u0433\u0443\u0440\u0430\u0446\u0438\u044F \u0438\u0441\u0445\u043E\u0434\u043D\u044B\u0445 \u0444\u0430\u0439\u043B\u043E\u0432 \u0438 \u0444\u043E\u0440\u043C\u0430\u0442\u0430
data_transfer_wizard_producers_title = \u0418\u0441\u0445\u043E\u0434\u043D\u044B\u0439 \u0444\u043E\u0440\u043C\u0430\u0442
data_transfer_wizard_settings_binaries_item_inline = \u0412\u0441\u0442\u0440\u0430\u0438\u0432\u0430\u0442\u044C
data_transfer_wizard_settings_binaries_item_save_to_file = \u0421\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C \u0432 \u0444\u0430\u0439\u043B
data_transfer_wizard_settings_binaries_item_set_to_null = \u0423\u0441\u0442\u0430\u043D\u043E\u0432\u0438\u0442\u044C \u0432 NULL
data_transfer_wizard_settings_button_edit = \u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C ...
data_transfer_wizard_settings_column_mapping_type = \u0422\u0438\u043F \u0441\u043E\u043E\u0442\u0432\u0435\u0442\u0441\u0442\u0432\u0438\u044F
data_transfer_wizard_settings_group_column_mappings = \u0421\u043E\u043E\u0442\u0432\u0435\u0442\u0441\u0442\u0432\u0438\u0435 \u043A\u043E\u043B\u043E\u043D\u043E\u043A
data_transfer_wizard_settings_group_exporter = \u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438 \u044D\u043A\u0441\u043F\u043E\u0440\u0442\u0430
data_transfer_wizard_settings_group_general = \u041E\u0431\u0449\u0435\u0435
data_transfer_wizard_settings_group_importer = \u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438 \u0438\u043C\u043F\u043E\u0440\u0442\u0430
data_transfer_wizard_settings_group_input_files = \u0412\u0445\u043E\u0434\u043D\u044B\u0435 \u0444\u0430\u0439\u043B\u044B
data_transfer_wizard_settings_group_preview = \u041F\u0440\u0435\u0434\u043F\u0440\u043E\u0441\u043C\u043E\u0442\u0440
data_transfer_wizard_settings_group_preview_columns = \u041A\u043E\u043B\u043E\u043D\u043A\u0438
data_transfer_wizard_settings_group_preview_table = \u0422\u0430\u0431\u043B\u0438\u0446\u0430
data_transfer_wizard_settings_label_binaries = \u0414\u0432\u043E\u0438\u0447\u043D\u044B\u0435 \u0434\u0430\u043D\u043D\u044B\u0435
data_transfer_wizard_settings_label_encoding = \u041A\u043E\u0434\u0438\u0440\u043E\u0432\u043A\u0430
data_transfer_wizard_settings_label_formatting = \u0424\u043E\u0440\u043C\u0430\u0442\u0438\u0440\u043E\u0432\u0430\u043D\u0438\u0435
data_transfer_wizard_settings_listbox_formatting_item_default = <\u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438 \u0441\u043E\u0435\u0434\u0438\u043D\u0435\u043D\u0438\u044F>
data_transfer_wizard_settings_name = \u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438
data_transfer_wizard_settings_title = \u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438
data_transfer_wizard_settings_description = \u0423\u0441\u0442\u0430\u043D\u043E\u0432\u043A\u0430 \u043F\u0430\u0440\u0430\u043C\u0435\u0442\u0440\u043E\u0432 \u0442\u0440\u0430\u043D\u0441\u0444\u0435\u0440\u0430 \u0434\u0430\u043D\u043D\u044B\u0445
data_transfer_wizard_output_checkbox_split_files = \u0420\u0430\u0437\u0431\u0438\u0442\u044C \u0432\u044B\u0445\u043E\u0434\u043D\u043E\u0439 \u0444\u0430\u0439\u043B
data_transfer_db_consumer_target_container = \u041A\u043E\u043D\u0442\u0435\u0439\u043D\u0435\u0440 \u0446\u0435\u043B\u0438
data_transfer_wizard_output_checkbox_split_files_tip = \u041C\u0430\u043A\u0441\u0438\u043C\u0430\u043B\u044C\u043D\u044B\u0439 \u0440\u0430\u0437\u043C\u0435\u0440 \u0444\u0430\u0439\u043B\u0430
sql_script_task_page_settings_option_auto_commit = \u0410\u0432\u0442\u043E-\u043A\u043E\u043C\u043C\u0438\u0442
sql_script_task_page_settings_option_dump_results = \u0412\u044B\u0432\u0435\u0441\u0442\u0438 \u0440\u0435\u0437\u0443\u043B\u044C\u0442\u0430\u0442\u044B \u0437\u0430\u043F\u0440\u043E\u0441\u0430 \u0432 \u043B\u043E\u0433-\u0444\u0430\u0439\u043B
sql_script_task_page_settings_option_ignore_errors = \u0418\u0433\u043D\u043E\u0440\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u043E\u0448\u0438\u0431\u043A\u0438
sql_script_task_page_settings_group_script = \u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438 \u0441\u043A\u0440\u0438\u043F\u0442\u0430
sql_script_task_page_settings_group_connections = \u0421\u043E\u0435\u0434\u0438\u043D\u0435\u043D\u0438\u044F
sql_script_task_page_settings_group_files = \u0424\u0430\u0439\u043B\u044B
sql_script_task_page_settings_description = \u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0441\u043A\u0440\u0438\u043F\u0442\u044B \u0438 \u0441\u043E\u0435\u0434\u0438\u043D\u0435\u043D\u0438\u044F. \u041A\u0430\u0436\u0434\u044B\u0439 \u0441\u043A\u0440\u0438\u043F\u0442 \u0431\u0443\u0434\u0435\u0442 \u0432\u044B\u043F\u043E\u043B\u043D\u044F\u0442\u044C\u0441\u044F \u0432\u043E \u0432\u0441\u0435\u0445 \u0432\u044B\u0431\u0440\u0430\u043D\u043D\u044B\u0445 \u0441\u043E\u0435\u0434\u0438\u043D\u0435\u043D\u0438\u044F\u0445.
sql_script_task_page_settings_title = \u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438 \u0432\u044B\u043F\u043E\u043B\u043D\u0435\u043D\u0438\u044F SQL \u0441\u043A\u0440\u0438\u043F\u0442\u0430
sql_script_task_title = \u0412\u044B\u043F\u043E\u043B\u043D\u0435\u043D\u0438\u0435 SQL \u0441\u043A\u0440\u0438\u043F\u0442\u0430
data_transfer_db_consumer_ddl = \u0421\u0445\u0435\u043C\u0430 ...
data_transfer_db_consumer_auto_assign = \u0410\u0432\u0442\u043E\u043F\u0440\u0438\u0441\u0432\u043E\u0435\u043D\u0438\u0435
data_transfer_db_consumer_auto_assign_description = \u0410\u0432\u0442\u043E\u043F\u0440\u0438\u0441\u0432\u043E\u0435\u043D\u0438\u0435 \u0441\u043E\u043E\u0442\u0432\u0435\u0442\u0441\u0442\u0432\u0438\u0439 \u0442\u0430\u0431\u043B\u0438\u0446 \u0438 \u0441\u0442\u043E\u043B\u0431\u0446\u043E\u0432
data_transfer_db_consumer_existing_table = \u041E\u0431\u0437\u043E\u0440 ...
data_transfer_db_consumer_existing_table_description = \u0412\u044B\u0431\u0440\u0430\u0442\u044C \u0446\u0435\u043B\u0435\u0432\u0443\u044E \u0442\u0430\u0431\u043B\u0438\u0446\u0443
data_transfer_db_consumer_new_table = \u0421\u043E\u0437\u0434\u0430\u0442\u044C ...
data_transfer_db_consumer_new_table_description = \u0421\u043E\u0437\u0434\u0430\u0442\u044C \u043D\u043E\u0432\u0443\u044E \u0446\u0435\u043B\u0435\u0432\u0443\u044E \u0442\u0430\u0431\u043B\u0438\u0446\u0443
data_transfer_db_consumer_column_mappings = \u0421\u0442\u043E\u043B\u0431\u0446\u044B ...
data_transfer_db_consumer_column_mappings_description = \u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0430 \u0441\u043E\u043E\u0442\u0432\u0435\u0442\u0441\u0442\u0432\u0438\u0439 \u0441\u0442\u043E\u043B\u0431\u0446\u043E\u0432
data_transfer_db_consumer_choose_container = \u0412\u044B\u0431\u043E\u0440 \u043A\u043E\u043D\u0442\u0435\u0439\u043D\u0435\u0440\u0430
database_consumer_settings_option_use_transactions = \u0418\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u044C \u0442\u0440\u0430\u043D\u0437\u0430\u043A\u0446\u0438\u0438
database_consumer_settings_option_commit_after = \u0412\u044B\u043F\u043E\u043B\u043D\u044F\u0442\u044C Commit \u043F\u043E\u0441\u043B\u0435 \u0432\u0441\u0442\u0430\u0432\u043A\u0438 \u0441\u0442\u0440\u043E\u043A
database_consumer_settings_option_transfer_auto_generated_columns = \u0421\u0434\u0435\u043B\u0430\u0442\u044C \u0442\u0440\u0430\u043D\u0441\u0444\u0435\u0440 \u0430\u0432\u0442\u043E\u043C\u0430\u0442\u0438\u0447\u0435\u0441\u043A\u0438 \u0441\u0433\u0435\u043D\u0435\u0440\u0438\u0440\u043E\u0432\u0430\u043D\u043D\u044B\u0445 \u0441\u0442\u043E\u043B\u0431\u0446\u043E\u0432
database_consumer_settings_option_truncate_before_load = \u041E\u0447\u0438\u0441\u0442\u0438\u0442\u044C \u043F\u0435\u0440\u0435\u0434 \u0437\u0430\u0433\u0440\u0443\u0437\u043A\u043E\u0439
data_transfer_settings_title_find_producer = \u041D\u0435 \u0443\u0434\u0430\u0435\u0442\u0441\u044F \u043D\u0430\u0439\u0442\u0438 \u043F\u0440\u043E\u0438\u0437\u0432\u043E\u0434\u0438\u0442\u0435\u043B\u044F
data_transfer_settings_message_find_data_producer = \u041D\u0435 \u0443\u0434\u0430\u0435\u0442\u0441\u044F \u043D\u0430\u0439\u0442\u0438 \u0434\u0435\u0441\u043A\u0440\u0438\u043F\u0442\u043E\u0440 \u043F\u0440\u043E\u0438\u0437\u0432\u043E\u0434\u0438\u0442\u0435\u043B\u044F \u0434\u0430\u043D\u043D\u044B\u0445 \u0432 \u0440\u0435\u0435\u0441\u0442\u0440\u0435
data_transfer_settings_title_find_consumer = \u041D\u0435 \u0443\u0434\u0430\u0435\u0442\u0441\u044F \u043D\u0430\u0439\u0442\u0438 \u043F\u043E\u0442\u0440\u0435\u0431\u0438\u0442\u0435\u043B\u044F
data_transfer_settings_message_find_data_consumer = \u041D\u0435 \u0443\u0434\u0430\u0435\u0442\u0441\u044F \u043D\u0430\u0439\u0442\u0438 \u0434\u0435\u0441\u043A\u0440\u0438\u043F\u0442\u043E\u0440 \u043F\u043E\u0442\u0440\u0435\u0431\u0438\u0442\u0435\u043B\u044F \u0434\u0430\u043D\u043D\u044B\u0445 \u0432 \u0440\u0435\u0435\u0441\u0442\u0440\u0435
database_consumer_settings_title_init_connection = \u041D\u0430\u0447\u0430\u043B\u044C\u043D\u043E\u0435 \u0441\u043E\u0435\u0434\u0438\u043D\u0435\u043D\u0438\u0435
database_consumer_settings_message_error_connecting = \u041E\u0448\u0438\u0431\u043A\u0430 \u043F\u043E\u0434\u043A\u043B\u044E\u0447\u0435\u043D\u0438\u044F \u043A \u0438\u0441\u0442\u043E\u0447\u043D\u0438\u043A\u0443 \u0434\u0430\u043D\u043D\u044B\u0445
database_mapping_container_message_get_attributes_from = \u041D\u0435 \u0443\u0434\u0430\u0435\u0442\u0441\u044F \u043F\u043E\u043B\u0443\u0447\u0438\u0442\u044C \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u043E\u0442 {0}
database_mapping_container_title_attributes_read_failed = \u0421\u0431\u043E\u0439 \u0447\u0442\u0435\u043D\u0438\u044F \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432
database_transfer_consumer_task_error_occurred_during_data_load = \u041F\u0440\u043E\u0438\u0437\u043E\u0448\u043B\u0430 \u043E\u0448\u0438\u0431\u043A\u0430 \u0432\u043E \u0432\u0440\u0435\u043C\u044F \u0437\u0430\u0433\u0440\u0443\u0437\u043A\u0438 \u0434\u0430\u043D\u043D\u044B\u0445
stream_transfer_consumer_title_run_process = \u0417\u0430\u043F\u0443\u0441\u0442\u0438\u0442\u044C \u043F\u0440\u043E\u0446\u0435\u0441\u0441
stream_transfer_consumer_message_error_running_process = \u041E\u0448\u0438\u0431\u043A\u0430 \u0437\u0430\u043F\u0443\u0441\u043A\u0430 \u043F\u0440\u043E\u0446\u0435\u0441\u0441\u0430 [{0}]
stream_transfer_consumer_title_attributes_read_failed = \u0421\u0431\u043E\u0439 \u0447\u0442\u0435\u043D\u0438\u044F \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432
stream_transfer_consumer_message_cannot_get_attributes_from = \u041D\u0435 \u0443\u0434\u0430\u0435\u0442\u0441\u044F \u043F\u043E\u043B\u0443\u0447\u0438\u0442\u044C \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u043E\u0442 {0}
stream_transfer_consumer_title_configuration_load_failed = \u0421\u0431\u043E\u0439 \u0447\u0442\u0435\u043D\u0438\u044F \u043A\u043E\u043D\u0444\u0438\u0433\u0443\u0440\u0430\u0446\u0438\u0438
stream_transfer_consumer_message_cannot_load_configuration = \u041D\u0435 \u0443\u0434\u0430\u0435\u0442\u0441\u044F \u0437\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044C \u043A\u043E\u043D\u0444\u0438\u0433\u0443\u0440\u0430\u0446\u0438\u044E \u0442\u0440\u0430\u043D\u0441\u0444\u0435\u0440\u0430
data_transfer_summary_title = {0} \u043D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438
