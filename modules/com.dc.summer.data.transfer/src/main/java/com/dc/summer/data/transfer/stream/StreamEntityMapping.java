
package com.dc.summer.data.transfer.stream;

import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBPQualifiedObject;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.data.DBDDataFilter;
import com.dc.summer.model.data.DBDDataReceiver;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCExecutionSource;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.DBCStatistics;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.*;
import com.dc.summer.data.transfer.stream.model.StreamDataSource;
import com.dc.utils.CommonUtils;

import java.io.File;
import java.util.*;

public class StreamEntityMapping implements DBSEntity, DBSDataContainer, DBPQualifiedObject {
    private final File inputFile;
    private final DBPDataSource dataSource;
    private final String entityName;
    private final List<StreamDataImporterColumnInfo> streamColumns = new ArrayList<>();

    public StreamEntityMapping(File inputFile) {
        this.inputFile = inputFile;
        this.entityName = inputFile.getName();
        this.dataSource = new StreamDataSource(entityName);
    }

    StreamEntityMapping(Map<String, Object> config) throws DBCException {
        this.entityName = CommonUtils.toString(config.get("entityId"));

        String inputFileName = CommonUtils.toString(config.get("inputFile"));
        if (CommonUtils.isEmpty(inputFileName)) {
            inputFileName = this.entityName;
        }
        this.inputFile = new File(inputFileName);

        this.dataSource = new StreamDataSource(entityName);
    }

    public File getInputFile() {
        return inputFile;
    }

    public String getEntityName() {
        return entityName;
    }

    @NotNull
    @Override
    public DBSEntityType getEntityType() {
        return DBSEntityType.TABLE;
    }

    @Override
    public List<StreamDataImporterColumnInfo> getAttributes(@NotNull DBRProgressMonitor monitor) throws DBException {
        return streamColumns;
    }

    @Override
    public DBSEntityAttribute getAttribute(@NotNull DBRProgressMonitor monitor, @NotNull String attributeName) throws DBException {
        return DBUtils.findObject(streamColumns, attributeName);
    }

    @Override
    public Collection<? extends DBSEntityConstraint> getConstraints(@NotNull DBRProgressMonitor monitor) throws DBException {
        return null;
    }

    @Override
    public Collection<? extends DBSEntityAssociation> getAssociations(@NotNull DBRProgressMonitor monitor) throws DBException {
        return null;
    }

    @Override
    public Collection<? extends DBSEntityAssociation> getReferences(@NotNull DBRProgressMonitor monitor) throws DBException {
        return null;
    }

    @Override
    public String getDescription() {
        return null;
    }

    @Override
    public DBSObject getParentObject() {
        return dataSource;
    }

    @NotNull
    @Override
    public DBPDataSource getDataSource() {
        return dataSource;
    }

    @Override
    public String[] getSupportedFeatures() {
        return new String[] {FEATURE_DATA_SELECT};
    }

    @NotNull
    @Override
    public DBCStatistics readData(@NotNull DBCExecutionSource source, @NotNull DBCSession session, @NotNull DBDDataReceiver dataReceiver, DBDDataFilter dataFilter, long firstRow, long maxRows, long flags, int fetchSize, List<Object> data) throws DBCException {
        throw new DBCException("Not implemented");
    }

    @NotNull
    @Override
    public DBCStatistics readData(@NotNull DBCExecutionSource source, @NotNull DBCSession session, @NotNull DBDDataReceiver dataReceiver, DBDDataFilter dataFilter, long firstRow, long maxRows, long flags, int fetchSize, int stage, List<Object> data) throws DBCException {
        throw new DBCException("Not implemented");
    }

    @Override
    public long countData(@NotNull DBCExecutionSource source, @NotNull DBCSession session, @Nullable DBDDataFilter dataFilter, long flags) throws DBCException {
        return -1;
    }

    @NotNull
    @Override
    public String getName() {
        return entityName;
    }

    @Override
    public boolean isPersisted() {
        return true;
    }

    @NotNull
    @Override
    public String getFullyQualifiedName(DBPEvaluationContext context) {
        return getName();
    }

    public List<StreamDataImporterColumnInfo> getStreamColumns() {
        return streamColumns;
    }

    void setStreamColumns(List<StreamDataImporterColumnInfo> streamColumns) {
        this.streamColumns.clear();
        this.streamColumns.addAll(streamColumns);
    }

    public StreamDataImporterColumnInfo getStreamColumn(String name) {
        for (StreamDataImporterColumnInfo col : streamColumns) {
            if (name.equals(col.getName())) {
                return col;
            }
        }
        return null;
    }

    Map<String, Object>saveSettings() {
        Map<String, Object> mappings = new LinkedHashMap<>();
        mappings.put("entityId", entityName);
        return mappings;
    }

    public boolean isSameColumns(@NotNull StreamEntityMapping mapping) {
        if (streamColumns.size() != mapping.streamColumns.size()) {
            return false;
        }
        for (int index = 0; index < streamColumns.size(); index++) {
            StreamDataImporterColumnInfo oldColumn = streamColumns.get(index);
            StreamDataImporterColumnInfo newColumn = mapping.streamColumns.get(index);
            if (!oldColumn.getName().equals(newColumn.getName())) {
                return false;
            }
        }
        return true;
    }

    @Override
    public String toString() {
        return inputFile.getAbsolutePath();
    }

    @Override
    public int hashCode() {
        return inputFile.hashCode();
    }

    @Override
    public boolean equals(Object obj) {
        return obj instanceof StreamEntityMapping &&
            CommonUtils.equalObjects(inputFile, ((StreamEntityMapping) obj).inputFile);
    }
}
