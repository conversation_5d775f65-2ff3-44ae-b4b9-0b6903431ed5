
package com.dc.summer.data.transfer.stream;

import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.DBPNamedObject;
import com.dc.summer.model.data.DBDAttributeBinding;
import com.dc.summer.model.data.DBDContentStorage;
import com.dc.summer.model.data.DBDDisplayFormat;

import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * IStreamDataExporter
 */
public interface IStreamDataExporterSite {

    DBPNamedObject getSource();

    DBDDisplayFormat getExportFormat();

    Map<String, Object> getProperties();

    DBDAttributeBinding[] getAttributes();

    AtomicLong getBytesWritten();

    OutputStream getOutputStream();

    @Nullable
    File getOutputFile();

    List<File> getOutputFiles();

    PrintWriter getWriter();

    void flush() throws IOException;

    void writeBinaryData(@NotNull DBDContentStorage cs) throws IOException;

    @NotNull
    String getOutputEncoding();

}