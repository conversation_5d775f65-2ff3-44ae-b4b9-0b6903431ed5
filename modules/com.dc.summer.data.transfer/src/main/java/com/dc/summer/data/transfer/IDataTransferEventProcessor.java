
package com.dc.summer.data.transfer;

import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.model.runtime.DBRProgressMonitor;

import java.util.Map;

public interface IDataTransferEventProcessor<T extends IDataTransferConsumer<?, ?>> {
    void processEvent(@NotNull DBRProgressMonitor monitor, @NotNull Event event, @NotNull T consumer, @NotNull Map<String, Object> processorSettings) throws DBException;

    enum Event {
        START,
        FINISH
    }
}
