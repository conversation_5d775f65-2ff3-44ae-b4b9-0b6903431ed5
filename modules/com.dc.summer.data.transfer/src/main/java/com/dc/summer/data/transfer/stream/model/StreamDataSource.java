

package com.dc.summer.data.transfer.stream.model;

import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPDataSourceInfo;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.data.DBDFormatSettings;
import com.dc.summer.model.data.DBDValueHandler;
import com.dc.summer.model.data.DBDValueHandlerProvider;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.impl.AbstractSimpleDataSource;
import com.dc.summer.model.impl.data.DefaultValueHandler;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.SQLDialect;
import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.model.struct.DBSTypedObject;

import java.util.Collection;

/**
 * Stream data source. It is a fake client-side datasource to emulate database-like data producers.
 */
public class StreamDataSource extends AbstractSimpleDataSource<StreamExecutionContext> implements DBDValueHandlerProvider {

    private final StreamDataSourceDialect dialect;

    public StreamDataSource(StreamDataSourceContainer container) {
        super(container);
        this.executionContext = new StreamExecutionContext(this, "Main");
        this.dialect = new StreamDataSourceDialect();
    }

    public StreamDataSource(String inputName) {
        this(new StreamDataSourceContainer(inputName));
    }

    @NotNull
    @Override
    public DBPDataSourceInfo getInfo() {
        return new StreamDataSourceInfo();
    }

    @Override
    public SQLDialect getSQLDialect() {
        return dialect;
    }

    @Override
    public void initialize(@NotNull DBRProgressMonitor monitor) throws DBException {

    }

    @NotNull
    @Override
    public StreamExecutionContext openIsolatedContext(DBRProgressMonitor monitor, String purpose, DBCExecutionContext initFrom, Boolean autoCommit, DBPConnectionConfiguration configuration) throws DBException {
        return new StreamExecutionContext(this, purpose);
    }

    @Override
    public void closeClient() {
        // nothing to do here
    }

    // We need to implement value handler provider to pass default value handler for attribute bindings
    @Nullable
    @Override
    public DBDValueHandler getValueHandler(DBPDataSource dataSource, DBDFormatSettings preferences, DBSTypedObject typedObject) {
        return DefaultValueHandler.INSTANCE;
    }

    @Override
    public Collection<? extends DBSObject> getChildren(@NotNull DBRProgressMonitor monitor) throws DBException {
        return null;
    }

    @Nullable
    @Override
    public DBSObject getChild(@NotNull DBRProgressMonitor monitor, @NotNull String childName) throws DBException {
        return null;
    }

    @NotNull
    @Override
    public Class<? extends DBSObject> getPrimaryChildType(@Nullable DBRProgressMonitor monitor) throws DBException {
        return DBSObject.class;
    }

    @Override
    public void cacheStructure(@NotNull DBRProgressMonitor monitor, int scope) throws DBException {

    }

}
