
package com.dc.summer.data.transfer;

/**
 * DataTransfer constants
 */
public class DTConstants {

    public static final String TASK_IMPORT = "dataImport";
    public static final String TASK_EXPORT = "dataExport";

    public static final String PRODUCT_FEATURE_SIMPLE_DATA_TRANSFER = "simpleDataTransfer";

    public static final String DEFAULT_TABLE_NAME_EXPORT = "export";
    public static final String DEFAULT_TABLE_NAME = "<TABLE_NAME>";

    public static final String LINE_DELIMITER = "lineDelimiter";

    public static final String TEXT_IDENTIFIER = "textIdentifier";

    public static final String COLUMN_DELIMITER = "columnDelimiter";

    public static final String PROP_WATERMARK_CONTENT = "watermarkContent";

    public static final String PROP_WATERMARK_ANGLE = "watermarkAngle";

    public static final String CONTEXT_SCHEMA_NAME = "contextSchemaName";

    public static final String RESULT_STAGE = "stage";
}
