package com.dc.summer.data.transfer.stream.exporter.proxy;

import com.dc.summer.model.proxy.DBPHandlerProxy;
import lombok.Getter;

import java.util.concurrent.atomic.AtomicLong;

public abstract class BytesWrittenPHandlerProxy<T> extends DBPHandlerProxy<T> {

    @Getter
    private AtomicLong bytesWritten;

    protected BytesWrittenPHandlerProxy(T t, AtomicLong bytesWritten) {
        super(t);
        this.bytesWritten = bytesWritten;
    }

}
