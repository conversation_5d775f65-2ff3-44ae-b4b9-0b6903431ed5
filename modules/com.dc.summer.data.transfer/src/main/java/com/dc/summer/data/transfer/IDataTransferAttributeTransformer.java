
package com.dc.summer.data.transfer;

import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.model.data.DBDAttributeBinding;
import com.dc.summer.model.exec.DBCSession;

import java.util.Map;

/**
 * Attribute transformer for data transfer.
 * Transformer may have a state. It is instantiated for each entity attribute.
 */
public interface IDataTransferAttributeTransformer {

    Object transformAttribute(
        @NotNull DBCSession session,
        @NotNull DBDAttributeBinding[] dataAttributes,
        @NotNull Object[] dataRow,
        @NotNull DBDAttributeBinding attribute,
        @Nullable Object attrValue,
        @NotNull Map<String, Object> options)
        throws DBException;

}
