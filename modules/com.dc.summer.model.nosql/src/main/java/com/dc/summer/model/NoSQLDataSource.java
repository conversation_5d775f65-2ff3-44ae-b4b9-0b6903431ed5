package com.dc.summer.model;

import com.dc.code.NotNull;
import com.dc.summer.model.document.data.DBDataWrapper;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.impl.AbstractSimpleDataSource;

public abstract class NoSQLDataSource<EXECUTION_CONTEXT extends DBCExecutionContext> extends AbstractSimpleDataSource<EXECUTION_CONTEXT> {

   protected NoSQLDataSource(@NotNull DBPDataSourceContainer container) {
      super(container);
   }

   public abstract NoSQLDialect getSQLDialect();

   public abstract DBDataWrapper getDataWrapper();

}
