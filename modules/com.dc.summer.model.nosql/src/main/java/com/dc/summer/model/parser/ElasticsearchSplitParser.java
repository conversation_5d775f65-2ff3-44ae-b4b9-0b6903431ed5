package com.dc.summer.model.parser;

import com.dc.summer.model.nosql.parser.autogen.EsSplitLexer;
import com.dc.summer.model.nosql.parser.autogen.EsSplitParser;
import org.antlr.v4.runtime.*;

public class ElasticsearchSplitParser extends AntlrSplitParser {

    @Override
    protected ParserRuleContext parse(Parser parser) {
        return ((EsSplitParser) parser).root();
    }

    @Override
    protected Lexer createLexerFormCharStream(CharStream charStream) {
        return new EsSplitLexer(charStream);
    }

    @Override
    protected Parser createParserFromTokenStream(CommonTokenStream tokenStream) {
        return new EsSplitParser(tokenStream);
    }

    @Override
    protected ISplitListener createParserListener() {
        return new ElasticsearchSplitListener();
    }
}
