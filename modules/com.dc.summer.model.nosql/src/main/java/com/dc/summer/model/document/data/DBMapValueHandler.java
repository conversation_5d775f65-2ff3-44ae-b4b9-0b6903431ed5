package com.dc.summer.model.document.data;

import java.util.Map;
import com.dc.code.NotNull;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.impl.data.DefaultValueHandler;
import com.dc.summer.model.struct.DBSTypedObject;

public class DBMapV<PERSON>ueHandler extends De<PERSON>ultValueHandler {
   public static final DBMapValueHandler INSTANCE = new DBMapValueHandler();

   public Object createNewValueObject(@NotNull DBCSession session, @NotNull DBSTypedObject type) throws DBCException {
      return new DBMapValue(session.getDataSource(), (Map)null);
   }
}
