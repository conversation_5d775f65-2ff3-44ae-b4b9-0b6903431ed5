package com.dc.summer.model.content;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.nio.ByteBuffer;
import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.model.DBValueFormatting;
import com.dc.summer.model.data.DBDBinaryFormatter;
import com.dc.summer.model.data.DBDContentCached;
import com.dc.summer.model.data.DBDContentStorage;
import com.dc.summer.model.data.DBDDisplayFormat;
import com.dc.summer.model.data.DBDValueCloneable;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.impl.data.AbstractContent;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.utils.CommonUtils;

public class DBContentByteBuffer extends AbstractContent implements DBDValueCloneable, DBDContentStorage, DBDContentCached {
   private static final Log log = Log.getLog(DBContentByteBuffer.class);
   private ByteBuffer originalData;
   private ByteBuffer data;

   public DBContentByteBuffer(DBCExecutionContext executionContext) {
      super(executionContext);
      this.data = this.originalData = null;
   }

   public DBContentByteBuffer(DBCExecutionContext executionContext, ByteBuffer data) {
      super(executionContext);
      this.data = this.originalData = data;
   }

   private DBContentByteBuffer(DBContentByteBuffer copyFrom) {
      super(copyFrom);
      this.data = copyFrom.data;
      this.originalData = copyFrom.originalData;
   }

   public ByteBuffer getData() {
      return this.data;
   }

   public InputStream getContentStream() throws IOException {
      return this.data == null ? new ByteArrayInputStream(new byte[0]) : new ByteArrayInputStream(this.data.array());
   }

   public Reader getContentReader() throws IOException {
      return new InputStreamReader(this.getContentStream());
   }

   public long getContentLength() {
      return this.data == null ? 0L : (long)this.data.limit();
   }

   public String getCharset() {
      return DBValueFormatting.getDefaultBinaryFileEncoding(this.executionContext.getDataSource());
   }

   public DBContentByteBuffer cloneStorage(DBRProgressMonitor monitor) throws IOException {
      return this.cloneValue(monitor);
   }

   public @NotNull String getContentType() {
      return "application/octet-stream";
   }

   public DBDContentStorage getContents(DBRProgressMonitor monitor) throws DBCException {
      return this;
   }

   public boolean updateContents(DBRProgressMonitor monitor, DBDContentStorage storage) throws DBException {
      if (storage == null) {
         this.data = null;
      } else {
         try {
            Throwable var3 = null;
            Object var4 = null;

            try {
               InputStream is = storage.getContentStream();

               try {
                  byte[] tmpBuf = new byte[(int)storage.getContentLength()];
                  int count = is.read(tmpBuf);
                  if (count != tmpBuf.length) {
                     log.warn("Actual content length (" + count + ") is less than declared (" + tmpBuf.length + ")");
                  }

                  this.data = ByteBuffer.wrap(tmpBuf);
               } finally {
                  if (is != null) {
                     is.close();
                  }

               }
            } catch (Throwable var15) {
               if (var3 == null) {
                  var3 = var15;
               } else if (var3 != var15) {
                  var3.addSuppressed(var15);
               }

               throw var3;
            }
         } catch (Throwable var16) {
            throw new DBCException("IO error while reading content", var16);
         }
      }

      this.modified = true;
      return false;
   }

   public void resetContents() {
      this.data = this.originalData;
      this.modified = false;
   }

   public Object getRawValue() {
      return this.data;
   }

   public boolean isNull() {
      return this.data == null;
   }

   public void release() {
      this.data = this.originalData;
   }

   public String getDisplayString(DBDDisplayFormat format) {
      if (this.data == null) {
         return null;
      } else {
         DBDBinaryFormatter formatter = DBValueFormatting.getBinaryPresentation(this.executionContext.getDataSource());
         int maxLength = this.executionContext.getDataSource().getContainer().getPreferenceStore().getInt("resultset.binary.stringMaxLength");
         int length = this.data.limit();
         if (format == DBDDisplayFormat.UI && length > maxLength) {
            length = maxLength;
         }

         String string = formatter.toString(this.data.array(), 0, length);
         return length == this.data.limit() ? string : string + "..." + " [" + this.data.limit() + "]";
      }
   }

   public DBContentByteBuffer cloneValue(DBRProgressMonitor monitor) {
      return new DBContentByteBuffer(this);
   }

   public Object getCachedValue() {
      return this.data;
   }

   public boolean equals(Object obj) {
      if (this == obj) {
         return true;
      } else {
         return obj instanceof DBContentByteBuffer ? CommonUtils.equalObjects(this.data, ((DBContentByteBuffer)obj).data) : false;
      }
   }
}
