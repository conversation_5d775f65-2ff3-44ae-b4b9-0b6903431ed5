package com.dc.summer.model.document.handlers;

import java.util.Date;
import com.dc.code.NotNull;
import com.dc.summer.model.data.DBDDisplayFormat;
import com.dc.summer.model.data.DBDFormatSettings;
import com.dc.summer.model.data.json.JSONUtils;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCFeatureNotSupportedException;
import com.dc.summer.model.exec.DBCResultSet;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.DBCStatement;
import com.dc.summer.model.impl.data.DateTimeCustomValueHandler;
import com.dc.summer.model.struct.DBSTypedObject;

public class DocumentTimestampValueHandler extends DateTimeCustomValueHandler {
   public DocumentTimestampValueHandler(DBDFormatSettings formatSettings) {
      super(formatSettings);
   }

   public @NotNull String getValueDisplayString(@NotNull DBSTypedObject column, Object value, @NotNull DBDDisplayFormat format) {
      return format == DBDDisplayFormat.NATIVE && value instanceof Date ? JSONUtils.formatISODate((Date)value) : super.getValueDisplayString(column, value, format);
   }

   public Object fetchValueObject(@NotNull DBCSession session, @NotNull DBCResultSet resultSet, @NotNull DBSTypedObject type, int index) throws DBCException {
      return resultSet.getAttributeValue(index);
   }

   public final void bindValueObject(@NotNull DBCSession session, @NotNull DBCStatement statement, @NotNull DBSTypedObject columnMetaData, int index, Object value) throws DBCException {
      throw new DBCFeatureNotSupportedException();
   }

   protected String getFormatterId(DBSTypedObject column) {
      return "timestamp";
   }
}
