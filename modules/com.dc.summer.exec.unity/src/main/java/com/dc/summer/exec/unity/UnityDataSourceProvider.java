package com.dc.summer.exec.unity;

import com.dc.summer.DBException;
import com.dc.summer.exec.unity.model.UnityDataSource;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.connection.DBPDriver;
import com.dc.summer.model.impl.jdbc.JDBCDataSourceProvider;
import com.dc.summer.model.impl.jdbc.JDBCURL;
import com.dc.summer.model.runtime.DBRProgressMonitor;


public class UnityDataSourceProvider extends JDBCDataSourceProvider {

    @Override
    public long getFeatures() {
        return FEATURE_NONE;
    }

    @Override
    public DBPDataSource openDataSource(DBRProgressMonitor monitor, DBPDataSourceContainer container) throws DBException {
        return new UnityDataSource(monitor, container);
    }

    @Override
    public String getConnectionURL(DBPDriver driver, DBPConnectionConfiguration connectionInfo) {
        return JDBCURL.generateUrlByTemplate(driver, connectionInfo);
    }

}
