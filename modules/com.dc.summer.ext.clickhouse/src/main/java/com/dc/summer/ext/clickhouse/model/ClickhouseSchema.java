
package com.dc.summer.ext.clickhouse.model;

import com.dc.summer.ext.generic.model.GenericCatalog;
import com.dc.summer.ext.generic.model.GenericTableBase;
import com.dc.summer.model.DBPObjectStatisticsCollector;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.ext.generic.model.GenericDataSource;
import com.dc.summer.ext.generic.model.GenericSchema;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSEntity;
import com.dc.summer.model.struct.DBSObject;

import java.sql.SQLException;
import java.util.List;

/**
 * ClickhouseSchema
 */
public class ClickhouseSchema extends GenericSchema implements DBPObjectStatisticsCollector
{
    private boolean hasStatistics = true;

    public ClickhouseSchema(@NotNull GenericDataSource dataSource, @Nullable GenericCatalog catalog, @NotNull String schemaName) {
        super(dataSource, catalog, schemaName);
    }

    @Override
    public List<ClickhouseTable> getPhysicalTables(DBRProgressMonitor monitor) throws DBException {
        return (List<ClickhouseTable>) super.getPhysicalTables(monitor);
    }

    @Override
    public List<ClickhouseTable> getTables(DBRProgressMonitor monitor) throws DBException {
        return (List<ClickhouseTable>) super.getTables(monitor);
    }

    @Override
    public boolean isStatisticsCollected() {
        return hasStatistics;
    }

    @Override
    public synchronized DBSObject refreshObject(@NotNull DBRProgressMonitor monitor) throws DBException {
        resetStatistics();
        return super.refreshObject(monitor);
    }

    void resetStatistics() {
        hasStatistics = false;
    }

    @Override
    public void collectObjectStatistics(DBRProgressMonitor monitor, boolean totalSizeOnly, boolean forceRefresh) throws DBException {
        if (hasStatistics && !forceRefresh) {
            return;
        }
        try (DBCSession session = DBUtils.openMetaSession(monitor, this, "Read relation statistics")) {
            try (JDBCPreparedStatement dbStat = ((JDBCSession)session).prepareStatement(
                "select table," +
                        "sum(bytes) as table_size, " +
                        "sum(rows) as table_rows, " +
                        "max(modification_time) as latest_modification," +
                        "min(min_date) AS min_date," +
                        "max(max_date) AS max_date " +
                    "FROM system.parts\n" +
                    "WHERE database=? AND active\n" +
                    "GROUP BY table"))
            {
                dbStat.setString(1, getName());
                try (JDBCResultSet dbResult = dbStat.executeQuery()) {
                    while (dbResult.next()) {
                        String tableName = dbResult.getString(1);
                        GenericTableBase table = getTable(monitor, tableName);
                        if (table instanceof ClickhouseTable) {
                            ((ClickhouseTable)table).fetchStatistics(dbResult);
                        }
                    }
                }
            } catch (SQLException e) {
                throw new DBCException("Error reading schema statistics", e);
            }
        } finally {
            hasStatistics = true;
        }
    }

    @NotNull
    @Override
    public Class<? extends DBSEntity> getPrimaryChildType(@Nullable DBRProgressMonitor monitor) throws DBException {
        return ClickhouseTable.class;
    }
}
