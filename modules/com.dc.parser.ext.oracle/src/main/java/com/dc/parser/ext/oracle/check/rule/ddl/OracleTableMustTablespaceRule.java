package com.dc.parser.ext.oracle.check.rule.ddl;

import com.dc.parser.ext.oracle.segment.table.OracleRelationalTableSegment;
import com.dc.parser.ext.oracle.statement.ddl.OracleCreateTableStatement;
import com.dc.parser.model.enums.CheckRuleDatabasePrefix;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.ddl.TableMustTablespaceRule;
import com.dc.parser.model.statement.SQLStatement;

public class OracleTableMustTablespaceRule extends TableMustTablespaceRule {

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        if (!(sqlStatement instanceof OracleCreateTableStatement)) {
            return CheckResult.DEFAULT_SUCCESS_RESULT;
        }

        OracleCreateTableStatement checkStatement = (OracleCreateTableStatement) sqlStatement;
        if (checkStatement.getRelationalTable().orElse(null) instanceof OracleRelationalTableSegment) {
            OracleRelationalTableSegment relationalTable = (OracleRelationalTableSegment) checkStatement.getRelationalTable().orElse(null);
            if (relationalTable.getPhysicalProperties() != null
                    && relationalTable.getPhysicalProperties().getTablespace() != null
                    && relationalTable.getPhysicalProperties().getTablespace().getIdentifier() != null) {
                return CheckResult.DEFAULT_SUCCESS_RESULT;
            }
        }

        return CheckResult.buildFailResult(parameter.getCheckRuleContent());
    }

    @Override
    public String getType() {
        return CheckRuleDatabasePrefix.ORACLE_.getValue() + super.getType();
    }

}
