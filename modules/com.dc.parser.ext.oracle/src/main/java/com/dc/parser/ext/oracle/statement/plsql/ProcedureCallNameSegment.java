
package com.dc.parser.ext.oracle.statement.plsql;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import com.dc.parser.model.segment.SQLSegment;
import com.dc.parser.model.segment.ddl.packages.PackageSegment;
import com.dc.parser.model.value.identifier.IdentifierValue;

import java.util.Optional;

/**
 * PL/SQL procedure call name segment.
 */
@RequiredArgsConstructor
@Getter
@Setter
public final class ProcedureCallNameSegment implements SQLSegment {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    private final IdentifierValue identifier;
    
    private PackageSegment packageSegment;
    
    public Optional<PackageSegment> getPackageSegment() {
        return Optional.ofNullable(packageSegment);
    }
    
    @Override
    public String toString() {
        return getPackageSegment().map(s -> s.getIdentifier().getValueWithQuoteCharacters() + ".").orElse("") + identifier.getValueWithQuoteCharacters();
    }
}
