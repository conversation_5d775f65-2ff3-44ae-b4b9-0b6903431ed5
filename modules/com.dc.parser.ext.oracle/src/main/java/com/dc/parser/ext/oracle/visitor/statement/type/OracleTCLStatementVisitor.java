
package com.dc.parser.ext.oracle.visitor.statement.type;

import com.dc.parser.model.api.ASTNode;
import com.dc.parser.model.api.visitor.statement.type.TCLStatementVisitor;
import com.dc.parser.ext.oracle.parser.autogen.OracleStatementParser.*;
import com.dc.parser.ext.oracle.visitor.statement.OracleStatementVisitor;
import com.dc.parser.model.value.identifier.IdentifierValue;
import com.dc.parser.ext.oracle.statement.tcl.*;

/**
 * TCL statement visitor for Oracle.
 */
public final class OracleTCLStatementVisitor extends OracleStatementVisitor implements TCLStatementVisitor {
    
    @Override
    public ASTNode visitSetTransaction(final SetTransactionContext ctx) {
        return new OracleSetTransactionStatement();
    }
    
    @Override
    public ASTNode visitCommit(final CommitContext ctx) {
        return new OracleCommitStatement();
    }
    
    @Override
    public ASTNode visitRollback(final RollbackContext ctx) {
        OracleRollbackStatement result = new OracleRollbackStatement();
        if (null != ctx.savepointClause().savepointName()) {
            result.setSavepointName(((IdentifierValue) visit(ctx.savepointClause().savepointName())).getValue());
        }
        return result;
    }
    
    @Override
    public ASTNode visitSavepoint(final SavepointContext ctx) {
        OracleSavepointStatement result = new OracleSavepointStatement();
        result.setSavepointName(((IdentifierValue) visit(ctx.savepointName())).getValue());
        return result;
    }
    
    @Override
    public ASTNode visitSetConstraints(final SetConstraintsContext ctx) {
        return new OracleSetConstraintsStatement();
    }
}
