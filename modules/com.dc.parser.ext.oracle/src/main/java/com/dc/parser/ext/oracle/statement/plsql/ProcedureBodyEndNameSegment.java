
package com.dc.parser.ext.oracle.statement.plsql;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import com.dc.parser.model.segment.SQLSegment;
import com.dc.parser.model.value.identifier.IdentifierValue;

/**
 * PL/SQL procedure body end name segment.
 */
@RequiredArgsConstructor
@Getter
public final class ProcedureBodyEndNameSegment implements SQLSegment {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    private final IdentifierValue identifier;
    
    @Override
    public String toString() {
        return identifier.getValueWithQuoteCharacters();
    }
}
