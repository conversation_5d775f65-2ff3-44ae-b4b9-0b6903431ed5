
package com.dc.summer.ext.generic.model;

import com.dc.summer.model.impl.jdbc.struct.JDBCAttribute;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.struct.DBSTypedObject;
import com.dc.summer.model.struct.rdb.DBSProcedureParameter;
import com.dc.summer.model.struct.rdb.DBSProcedureParameterKind;
import com.dc.code.NotNull;
import com.dc.code.Nullable;

/**
 * GenericTable
 */
public class GenericProcedureParameter extends JDBCAttribute implements DBSProcedureParameter
{
    private String remarks;
    private GenericProcedure procedure;
    private DBSProcedureParameterKind parameterKind;

    public GenericProcedureParameter(
            GenericProcedure procedure,
            String columnName,
            String typeName,
            int valueType,
            int ordinalPosition,
            int columnSize,
            Integer scale,
            Integer precision,
            boolean notNull,
            String remarks,
            DBSProcedureParameterKind parameterKind)
    {
        super(columnName,
            typeName,
            valueType,
            ordinalPosition,
            columnSize,
            scale,
            precision,
            notNull,
            false);
        this.remarks = remarks;
        this.procedure = procedure;
        this.parameterKind = parameterKind;
    }

    @NotNull
    @Override
    public GenericDataSource getDataSource()
    {
        return procedure.getDataSource();
    }

    @Override
    public GenericProcedure getParentObject()
    {
        return procedure;
    }

    @NotNull
    @Override
    @Property(viewable = true, order = 30)
    public DBSProcedureParameterKind getParameterKind()
    {
        return parameterKind;
    }

    @Nullable
    @Override
    public String getDescription()
    {
        return remarks;
    }

    @NotNull
    @Override
    public DBSTypedObject getParameterType() {
        return this;
    }
}
