<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="delete_with_sharding_value"
              value="DELETE FROM t_order WHERE order_id = ? AND user_id = ? AND status=?"/>
    <sql-case id="delete_without_sharding_value" value="DELETE FROM t_order WHERE status=?"/>
    <sql-case id="delete_with_special_character_without_sharding_value"
              value="DELETE FROM `t_order` WHERE `status`='init'" db-types="MySQL"/>
    <sql-case id="delete_with_alias" value="DELETE FROM t_order AS o WHERE status=?" db-types="MySQL,SQLServer"/>
    <sql-case id="delete_with_alias_without_as"
              value="DELETE product_price_history pp WHERE (product_id, currency_code, effective_from_date) IN (SELECT product_id, currency_code, MAX(effective_from_date) FROM product_price_history GROUP BY product_id, currency_code)"
              db-types="Oracle"/>
    <sql-case id="delete_with_order_by_row_count"
              value="DELETE FROM t_order WHERE order_id = ? AND user_id = ? AND status=? ORDER BY order_id LIMIT ?"
              db-types="MySQL"/>
    <sql-case id="delete_with_output_clause"
              value="DELETE FROM t_order OUTPUT DELETED.order_id, DELETED.user_id INTO @MyTableVar (temp_order_id, temp_user_id) WHERE order_id = ?"
              db-types="SQLServer"/>
    <sql-case id="delete_with_output_clause_without_output_table_columns"
              value="DELETE FROM t_order OUTPUT DELETED.order_id, DELETED.user_id INTO @MyTableVar WHERE order_id = ?"
              db-types="SQLServer"/>
    <sql-case id="delete_with_output_clause_without_output_table"
              value="DELETE FROM t_order OUTPUT DELETED.order_id, DELETED.user_id WHERE order_id = ?"
              db-types="SQLServer"/>
    <sql-case id="delete_with_output_clause_column_shorthand"
              value="DELETE FROM t_order OUTPUT DELETED.* WHERE order_id = ?" db-types="SQLServer"/>
    <sql-case id="delete_with_top" value="DELETE TOP(10) FROM t_order WHERE order_id = ?" db-types="SQLServer"/>
    <sql-case id="delete_with_top_percent" value="DELETE TOP(10) PERCENT FROM t_order WHERE order_id = ?"
              db-types="SQLServer"/>
    <sql-case id="delete_with_with_clause"
              value="WITH cte (order_id, user_id) AS (SELECT order_id, user_id FROM t_order) DELETE t_order FROM cte WHERE t_order.order_id = cte.order_id"
              db-types="SQLServer"/>
    <sql-case id="delete_without_columns_with_with_clause"
              value="WITH cte AS (SELECT order_id, user_id FROM t_order) DELETE t_order FROM cte WHERE t_order.order_id = cte.order_id"
              db-types="SQLServer"/>
    <sql-case id="delete_multi_tables"
              value="DELETE t_order, t_order_item from t_order, t_order_item where t_order.order_id = t_order_item.order_id and t_order.status = ?"
              db-types="MySQL"/>
    <sql-case id="delete_multi_tables_with_using"
              value="DELETE from t_order, t_order_item using t_order left join t_order_item on t_order.order_id = t_order_item.order_id where t_order.status = ?"
              db-types="MySQL"/>
    <sql-case id="delete_with_query_hint" value="DELETE FROM t_order WHERE order_id = ?" db-types="SQLServer"/>
    <sql-case id="delete_with_simple_table" value="DELETE FROM product_descriptions WHERE language_id = 'AR'"
              db-types="Oracle"/>
    <sql-case id="delete_with_simple_subquery"
              value="DELETE FROM (SELECT * FROM employees) WHERE job_id = 'SA_REP' AND commission_pct &lt; 0.2"
              db-types="Oracle"/>
    <sql-case id="delete_with_simple_subquery_without_from"
              value="DELETE (SELECT * FROM product_price_history) WHERE currency_code = 'EUR'" db-types="Oracle"/>
    <sql-case id="delete_with_remote_database_table" value="DELETE FROM hr.locations@remote WHERE location_id > 3000"
              db-types="Oracle"/>
    <sql-case id="delete_with_returning_into"
              value="DELETE FROM employees WHERE job_id = 'SA_REP' AND hire_date + TO_YMINTERVAL('01-00') &lt; SYSDATE RETURNING salary INTO bnd1"
              db-types="Oracle"/>
    <sql-case id="delete_with_partition" value="DELETE FROM sales PARTITION (sales_q1_1998) WHERE amount_sold > 1000"
              db-types="Oracle"/>
    <sql-case id="delete_with_table" value="DELETE product_price_history" db-types="Oracle"/>
    <sql-case id="delete_with_schema" value="DELETE FROM db1.t_order"/>
    <sql-case id="delete_with_simple_condition" value="DELETE FROM Q1_2000_sales WHERE amount_sold &lt; 0"
              db-types="Oracle"/>
    <sql-case id="delete_with_output_clause_with_compress_function"
              value="DELETE FROM player OUTPUT deleted.id,deleted.name, deleted.surname,deleted.datemodifier,COMPRESS(deleted.info) INTO dbo.inactivePlayers WHERE datemodified &lt; @startOfYear"
              db-types="SQLServer"/>
    <sql-case id="delete_returning_expressions" value="DELETE FROM t2 WHERE id = 2 RETURNING id,t&amp;t"
              db-types="MySQL,Firebird"/>
</sql-cases>
