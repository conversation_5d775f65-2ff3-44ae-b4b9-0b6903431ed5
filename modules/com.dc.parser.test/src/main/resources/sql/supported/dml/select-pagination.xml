<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="select_pagination_with_offset"
              value="SELECT i.* FROM t_order o JOIN t_order_item i ON o.user_id = i.user_id AND o.order_id = i.order_id WHERE o.user_id IN (?, ?) AND o.order_id BETWEEN ? AND ? ORDER BY i.item_id DESC OFFSET ?"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="select_pagination_with_row_count"
              value="SELECT i.* FROM t_order o JOIN t_order_item i ON o.user_id = i.user_id AND o.order_id = i.order_id WHERE o.user_id IN (?, ?) AND o.order_id BETWEEN ? AND ? ORDER BY i.item_id DESC LIMIT ?"
              db-types="MySQL,H2,PostgreSQL,GaussDB"/>F
    <sql-case id="select_pagination_with_top"
              value="SELECT * FROM (SELECT TOP (?) row_number() OVER (ORDER BY i.item_id DESC) AS rownum_, i.item_id, o.order_id as order_id, o.status as status, o.user_id as user_id FROM t_order o JOIN t_order_item i ON o.user_id = i.user_id AND o.order_id = i.order_id WHERE o.user_id IN (?, ?) AND o.order_id BETWEEN ? AND ?) AS row_"
              db-types="SQLServer"/>
    <sql-case id="select_pagination_with_top_percent_with_ties"
              value="SELECT * FROM (SELECT TOP (?) PERCENT WITH TIES row_number() OVER (ORDER BY i.item_id DESC) AS rownum_, i.item_id, o.order_id as order_id, o.status as status, o.user_id as user_id FROM t_order o JOIN t_order_item i ON o.user_id = i.user_id AND o.order_id = i.order_id WHERE o.user_id IN (?, ?) AND o.order_id BETWEEN ? AND ?) AS row_"
              db-types="SQLServer"/>
    <sql-case id="select_pagination_with_row_number"
              value="SELECT * FROM (SELECT row_.*, rownum rownum_ FROM (SELECT order0_.order_id AS order_id, order0_.status AS status, order0_.user_id AS user_id FROM t_order order0_ JOIN t_order_item i ON order0_.user_id = i.user_id AND order0_.order_id = i.order_id WHERE order0_.user_id IN (?, ?) AND order0_.order_id BETWEEN ? AND ? ORDER BY i.item_id DESC) row_ WHERE rownum &lt;= ?)"
              db-types="Oracle"/>
    <sql-case id="select_pagination_with_limit_with_back_quotes"
              value="SELECT i.* FROM `t_order` o JOIN `t_order_item` i ON o.user_id = i.user_id AND o.order_id = i.order_id WHERE o.`user_id` IN (?, ?) AND o.`order_id` BETWEEN ? AND ? ORDER BY i.item_id DESC LIMIT ?, ?"
              db-types="MySQL"/>
    <sql-case id="select_pagination_with_limit_and_offset_keyword"
              value="SELECT i.* FROM `t_order` o JOIN `t_order_item` i ON o.user_id = i.user_id AND o.order_id = i.order_id WHERE o.`user_id` IN (?, ?) AND o.`order_id` BETWEEN ? AND ? ORDER BY i.item_id DESC LIMIT ? OFFSET ?"
              db-types="MySQL"/>
    <sql-case id="select_pagination_with_offset_and_limit"
              value="SELECT i.* FROM t_order o JOIN t_order_item i ON o.user_id = i.user_id AND o.order_id = i.order_id WHERE o.user_id IN (?, ?) AND o.order_id BETWEEN ? AND ? ORDER BY i.item_id DESC OFFSET ? LIMIT ?"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="select_pagination_with_offset_and_limit_all"
              value="SELECT i.* FROM t_order o JOIN t_order_item i ON o.user_id = i.user_id AND o.order_id = i.order_id WHERE o.user_id IN (?, ?) AND o.order_id BETWEEN ? AND ? ORDER BY i.item_id DESC OFFSET ? LIMIT ALL"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="select_pagination_with_top_for_greater_than"
              value="SELECT * FROM (SELECT TOP (?) row_number() OVER (ORDER BY i.item_id DESC) AS rownum_, i.item_id, o.order_id as order_id, o.status as status, o.user_id as user_id FROM t_order o JOIN t_order_item i ON o.user_id = i.user_id AND o.order_id = i.order_id WHERE o.user_id IN (?, ?) AND o.order_id BETWEEN ? AND ?) AS row_ WHERE row_.rownum_ &gt; ?"
              db-types="SQLServer"/>
    <sql-case id="select_pagination_with_top_percent_with_ties_for_greater_than"
              value="SELECT * FROM (SELECT TOP (?) PERCENT WITH TIES row_number() OVER (ORDER BY i.item_id DESC) AS rownum_, i.item_id, o.order_id as order_id, o.status as status, o.user_id as user_id FROM t_order o JOIN t_order_item i ON o.user_id = i.user_id AND o.order_id = i.order_id WHERE o.user_id IN (?, ?) AND o.order_id BETWEEN ? AND ?) AS row_ WHERE row_.rownum_ &gt; ?"
              db-types="SQLServer"/>
    <sql-case id="select_pagination_with_top_for_greater_than_and_equal"
              value="SELECT * FROM (SELECT TOP (?) row_number() OVER (ORDER BY i.item_id DESC) AS rownum_, i.item_id, o.order_id as order_id, o.status as status, o.user_id as user_id FROM t_order o JOIN t_order_item i ON o.user_id = i.user_id AND o.order_id = i.order_id WHERE o.user_id IN (?, ?) AND o.order_id BETWEEN ? AND ?) AS row_ WHERE row_.rownum_ &gt;= ?"
              db-types="SQLServer"/>
    <sql-case id="select_pagination_with_top_percent_with_ties_for_greater_than_and_equal"
              value="SELECT * FROM (SELECT TOP (?) PERCENT WITH TIES row_number() OVER (ORDER BY i.item_id DESC) AS rownum_, i.item_id, o.order_id as order_id, o.status as status, o.user_id as user_id FROM t_order o JOIN t_order_item i ON o.user_id = i.user_id AND o.order_id = i.order_id WHERE o.user_id IN (?, ?) AND o.order_id BETWEEN ? AND ?) AS row_ WHERE row_.rownum_ &gt;= ?"
              db-types="SQLServer"/>
    <sql-case id="select_pagination_with_row_number_for_greater_than"
              value="SELECT * FROM (SELECT row_.*, rownum rownum_ FROM (SELECT order0_.order_id AS order_id, order0_.status AS status, order0_.user_id AS user_id FROM t_order order0_ JOIN t_order_item i ON order0_.user_id = i.user_id AND order0_.order_id = i.order_id WHERE order0_.user_id IN (?, ?) AND order0_.order_id BETWEEN ? AND ? ORDER BY i.item_id DESC) row_ WHERE rownum &lt;= ?) tt WHERE tt.rownum_ &gt; ?"
              db-types="Oracle"/>
    <sql-case id="select_pagination_with_row_number_for_greater_than_and_equal"
              value="SELECT * FROM (SELECT row_.*, rownum rownum_ FROM (SELECT order0_.order_id AS order_id, order0_.status AS status, order0_.user_id AS user_id FROM t_order order0_ JOIN t_order_item i ON order0_.user_id = i.user_id AND order0_.order_id = i.order_id WHERE order0_.user_id IN (?, ?) AND order0_.order_id BETWEEN ? AND ? ORDER BY i.item_id DESC) row_ WHERE rownum &lt;= ?) tt WHERE tt.rownum_ &gt;= ?"
              db-types="Oracle"/>
    <sql-case id="select_pagination_with_row_number_not_at_end"
              value="SELECT * FROM t_order WHERE ROWNUM &lt;= ? ORDER BY order_id" db-types="Oracle"/>
    <sql-case id="select_pagination_with_fetch_first_with_row_number"
              value="SELECT * FROM t_order ORDER BY order_id FETCH FIRST 5 ROWS ONLY" db-types="Oracle"/>
    <sql-case id="select_pagination_with_offset_fetch"
              value="SELECT * FROM t_order ORDER BY order_id OFFSET 0 ROW FETCH NEXT ? ROWS ONLY" db-types="SQLServer"/>
    <sql-case id="select_pagination_with_limit_offset_and_row_count"
              value="SELECT order_id, user_id FROM t_order LIMIT 1, 2" db-types="MySQL,GaussDB"/>
    <sql-case id="select_pagination_with_limit_row_count" value="SELECT order_id, user_id FROM t_order LIMIT 2"
              db-types="MySQL"/>
    <sql-case id="select_pagination_with_limit_fetch_count" value="SELECT * FROM t_new_order FETCH NEXT 3 ROW ONLY"
              db-types="GaussDB,PostgreSQL"/>
</sql-cases>
