<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="alter_publication_rename" value="ALTER PUBLICATION old_publication RENAME TO new_publication;"
              db-types="PostgreSQL"/>
    <sql-case id="alter_publication_owner" value="ALTER PUBLICATION new_publication OWNER TO CURRENT_ROLE;"
              db-types="PostgreSQL"/>
    <sql-case id="alter_publication_set_table" value="ALTER PUBLICATION mypublication SET TABLE users, departments;"
              db-types="PostgreSQL"/>
    <sql-case id="alter_publication_add_table" value="ALTER PUBLICATION mypublication ADD TABLE stores, cities;"
              db-types="PostgreSQL"/>
    <sql-case id="alter_publication_drop_table" value="ALTER PUBLICATION mypublication DROP TABLE users;"
              db-types="PostgreSQL"/>
    <sql-case id="alter_publication_set_definition" value="ALTER PUBLICATION noinsert SET (publish = 'update, delete');"
              db-types="PostgreSQL"/>
</sql-cases>
