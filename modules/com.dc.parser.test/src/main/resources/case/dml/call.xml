<?xml version="1.0" encoding="UTF-8"?>

<sql-parser-test-cases>
    <call sql-case-id="call_without_parameters">
        <procedure-name name="p" start-index="5" stop-index="6"/>
    </call>

    <call sql-case-id="call_with_parameters_all_variable">
        <procedure-name name="p" start-index="5" stop-index="6"/>
        <procedure-parameter>
            <common-expression literal-text="@order_id" start-index="7" stop-index="15"/>
        </procedure-parameter>
        <procedure-parameter>
            <common-expression literal-text="@user_id" start-index="18" stop-index="25"/>
        </procedure-parameter>
    </call>

    <call sql-case-id="call_with_parameters_all_placeholder">
        <procedure-name name="p" start-index="5" stop-index="6"/>
        <procedure-parameter>
            <parameter-marker-expression parameter-index="0" start-index="7" stop-index="7"/>
        </procedure-parameter>
        <procedure-parameter>
            <parameter-marker-expression parameter-index="1" start-index="10" stop-index="10"/>
        </procedure-parameter>
    </call>

    <call sql-case-id="call_with_parameters_all_expression">
        <procedure-name name="p" start-index="5" stop-index="6"/>
        <procedure-parameter>
            <literal-expression value="user" start-index="7" stop-index="12"/>
        </procedure-parameter>
        <procedure-parameter>
            <literal-expression value="order" start-index="15" stop-index="21"/>
        </procedure-parameter>
    </call>

    <call sql-case-id="call_with_parameters_mix">
        <procedure-name name="p" start-index="5" stop-index="6"/>
        <procedure-parameter>
            <common-expression literal-text="@order_id" start-index="7" stop-index="15"/>
        </procedure-parameter>
        <procedure-parameter>
            <literal-expression value="user" start-index="18" stop-index="23"/>
        </procedure-parameter>
        <procedure-parameter>
            <parameter-marker-expression parameter-index="0" start-index="26" stop-index="26"/>
        </procedure-parameter>
    </call>

    <call sql-case-id="call_with_named_notation_with_null">
        <procedure-name name="p" start-index="5" stop-index="6"/>
        <procedure-parameter>
            <common-expression literal-text="a => null" start-index="7" stop-index="15"/>
        </procedure-parameter>
        <procedure-parameter>
            <common-expression literal-text="b => 8" start-index="18" stop-index="23"/>
        </procedure-parameter>
        <procedure-parameter>
            <common-expression literal-text="c => 2" start-index="26" stop-index="31"/>
        </procedure-parameter>
    </call>

    <call sql-case-id="call_with_named_notation">
        <procedure-name name="p" start-index="5" stop-index="6"/>
        <procedure-parameter>
            <common-expression literal-text="b => 8" start-index="7" stop-index="12"/>
        </procedure-parameter>
        <procedure-parameter>
            <common-expression literal-text="c => 2" start-index="15" stop-index="20"/>
        </procedure-parameter>
        <procedure-parameter>
            <common-expression literal-text="a => 0" start-index="23" stop-index="28"/>
        </procedure-parameter>
    </call>

    <call sql-case-id="call_with_mixed_notation">
        <procedure-name name="p" start-index="5" stop-index="6"/>
        <procedure-parameter>
            <literal-expression value="null" start-index="7" stop-index="10"/>
        </procedure-parameter>
        <procedure-parameter>
            <literal-expression value="7" start-index="13" stop-index="13"/>
        </procedure-parameter>
        <procedure-parameter>
            <common-expression literal-text="c => 2" start-index="16" stop-index="21"/>
        </procedure-parameter>
    </call>

    <call sql-case-id="call_with_mixed_notation_with_null">
        <procedure-name name="p" start-index="5" stop-index="6"/>
        <procedure-parameter>
            <literal-expression value="null" start-index="7" stop-index="10"/>
        </procedure-parameter>
        <procedure-parameter>
            <common-expression literal-text="c => 4" start-index="13" stop-index="18"/>
        </procedure-parameter>
        <procedure-parameter>
            <common-expression literal-text="b => 11" start-index="21" stop-index="27"/>
        </procedure-parameter>
    </call>

    <call sql-case-id="call_with_mixed_notation_with_apos">
        <procedure-name name="p" start-index="5" stop-index="6"/>
        <procedure-parameter>
            <literal-expression value="10" start-index="7" stop-index="8"/>
        </procedure-parameter>
        <procedure-parameter>
            <common-expression literal-text="b => 'Hello'" start-index="11" stop-index="22"/>
        </procedure-parameter>
    </call>

    <call sql-case-id="call_with_named_notation_with_apos">
        <procedure-name name="p" start-index="5" stop-index="6"/>
        <procedure-parameter>
            <common-expression literal-text="b => 'Hello'" start-index="7" stop-index="18"/>
        </procedure-parameter>
        <procedure-parameter>
            <common-expression literal-text="a => 10" start-index="21" stop-index="27"/>
        </procedure-parameter>
    </call>

    <call sql-case-id="call_with_positional_notation_with_expression">
        <procedure-name name="p" start-index="5" stop-index="6"/>
        <procedure-parameter>
            <common-expression literal-text="1.0/0.1" start-index="7" stop-index="13"/>
        </procedure-parameter>
    </call>

    <call sql-case-id="call_with_mysql_firewall_group_enlist">
        <procedure-name name="mysql.sp_firewall_group_enlist"/>
        <procedure-parameter>
            <literal-expression value="fwgrp" start-index="36" stop-index="42"/>
        </procedure-parameter>
        <procedure-parameter>
            <literal-expression value="member2@localhost" start-index="45" stop-index="63"/>
        </procedure-parameter>
    </call>
</sql-parser-test-cases>
