<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <insert sql-case-id="insert_into_values1">
        <table name="test_nested" start-index="12" stop-index="22"/>
        <columns start-index="23" stop-index="23"/>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="1" start-index="31" stop-index="31"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_into_values2">
        <table name="emp_table" start-index="12" stop-index="20"/>
        <columns start-index="21" stop-index="21"/>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="1" start-index="29" stop-index="29"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="John" start-index="31" stop-index="36"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="1000.00" start-index="38" stop-index="44"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="Architect" start-index="46" stop-index="56"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_into_values3">
        <table name="emp_table" start-index="12" stop-index="20"/>
        <columns start-index="21" stop-index="21"/>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="2" start-index="29" stop-index="29"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="Robert" start-index="31" stop-index="38"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="900.00" start-index="40" stop-index="45"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="Developer" start-index="47" stop-index="57"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_into_values4">
        <table name="emp_table" start-index="12" stop-index="20"/>
        <columns start-index="21" stop-index="21"/>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="3" start-index="29" stop-index="29"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="James" start-index="31" stop-index="37"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="2000.00" start-index="39" stop-index="45"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="Director" start-index="47" stop-index="56"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_into_values5">
        <table name="dept" start-index="12" stop-index="15"/>
        <columns start-index="16" stop-index="16"/>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="1" start-index="24" stop-index="24"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="Sales" start-index="26" stop-index="32"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="500 Oracle pkwy" start-index="34" stop-index="50"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="Redwood S" start-index="52" stop-index="62"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="CA" start-index="64" stop-index="67"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="94065" start-index="69" stop-index="75"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_into_values6">
        <table name="dept" start-index="12" stop-index="15"/>
        <columns start-index="16" stop-index="16"/>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="2" start-index="24" stop-index="24"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="ST" start-index="26" stop-index="29"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="400 Oracle Pkwy" start-index="31" stop-index="47"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="Redwood S" start-index="49" stop-index="59"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="CA" start-index="61" stop-index="64"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="94065" start-index="66" stop-index="72"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_into_values7">
        <table name="dept" start-index="12" stop-index="15"/>
        <columns start-index="16" stop-index="16"/>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="3" start-index="24" stop-index="24"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="Apps" start-index="26" stop-index="31"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="300 Oracle pkwy" start-index="33" stop-index="49"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="Redwood S" start-index="51" stop-index="61"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="CA" start-index="63" stop-index="66"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="94065" start-index="68" stop-index="74"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_all_placeholders" parameters="1, 1, 'init'">
        <table name="t_order" start-index="12" stop-index="18"/>
        <columns start-index="20" stop-index="46">
            <column name="order_id" start-index="21" stop-index="28"/>
            <column name="user_id" start-index="31" stop-index="37"/>
            <column name="status" start-index="40" stop-index="45"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="56" stop-index="56"/>
                    <literal-expression value="1" start-index="56" stop-index="56"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="59" stop-index="59"/>
                    <literal-expression value="1" start-index="59" stop-index="59"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2" start-index="62" stop-index="62"/>
                    <literal-expression value="init" start-index="62" stop-index="67"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_historical_type_cast_syntax" parameters="1, 1, 'init'">
        <table name="t_order" start-index="12" stop-index="18"/>
        <columns start-index="20" stop-index="46">
            <column name="order_id" start-index="21" stop-index="28"/>
            <column name="user_id" start-index="31" stop-index="37"/>
            <column name="status" start-index="40" stop-index="45"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="56" stop-index="56"/>
                    <literal-expression value="1" start-index="56" stop-index="56"/>
                </assignment-value>
                <assignment-value>
                    <binary-operation-expression start-index="59" stop-index="65">
                        <left>
                            <parameter-marker-expression parameter-index="2" start-index="59" stop-index="59"/>
                        </left>
                        <right>
                            <common-expression text="int4" start-index="62" stop-index="65"/>
                        </right>
                        <operator>::</operator>
                    </binary-operation-expression>
                </assignment-value>
                <assignment-value>
                    <binary-operation-expression start-index="68" stop-index="74">
                        <left>
                            <parameter-marker-expression parameter-index="3" start-index="68" stop-index="68"/>
                        </left>
                        <right>
                            <common-expression text="text" start-index="71" stop-index="74"/>
                        </right>
                        <operator>::</operator>
                    </binary-operation-expression>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_now_function" parameters="1, 1, 'init'">
        <table name="t_order_item" start-index="12" stop-index="23"/>
        <columns start-index="25" stop-index="75">
            <column name="item_id" start-index="26" stop-index="32"/>
            <column name="order_id" start-index="35" stop-index="42"/>
            <column name="user_id" start-index="45" stop-index="51"/>
            <column name="status" start-index="54" stop-index="59"/>
            <column name="creation_date" start-index="62" stop-index="74"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="85" stop-index="85"/>
                    <literal-expression value="1" start-index="85" stop-index="85"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="88" stop-index="88"/>
                    <literal-expression value="1" start-index="88" stop-index="88"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2" start-index="91" stop-index="91"/>
                    <literal-expression value="init" start-index="91" stop-index="96"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="insert" start-index="94" stop-index="101" literal-start-index="99"
                                        literal-stop-index="106"/>
                </assignment-value>
                <assignment-value>
                    <function function-name="now" text="now()" start-index="104" stop-index="108"
                              literal-start-index="109" literal-stop-index="113"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_without_parameters">
        <table name="t_order" start-index="12" stop-index="18"/>
        <columns start-index="20" stop-index="46">
            <column name="order_id" start-index="21" stop-index="28"/>
            <column name="user_id" start-index="31" stop-index="37"/>
            <column name="status" start-index="40" stop-index="45"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0"/>
                    <literal-expression value="1" start-index="56" stop-index="56"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1"/>
                    <literal-expression value="1" start-index="59" stop-index="59"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2"/>
                    <literal-expression value="insert" start-index="62" stop-index="69"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_uuid_column" parameters="1, 1">
        <table name="t_order" start-index="12" stop-index="18"/>
        <columns start-index="20" stop-index="28">
            <column name="id" start-index="21" stop-index="22"/>
            <column name="uuid" start-index="24" stop-index="27"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="38" stop-index="38"/>
                    <literal-expression value="1" start-index="38" stop-index="38"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="41" stop-index="41"/>
                    <literal-expression value="1" start-index="41" stop-index="41"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_multiple_values">
        <table name="t_order" start-index="12" stop-index="18"/>
        <columns start-index="20" stop-index="46">
            <column name="order_id" start-index="21" stop-index="28"/>
            <column name="user_id" start-index="31" stop-index="37"/>
            <column name="status" start-index="40" stop-index="45"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="1" start-index="56" stop-index="56"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="1" start-index="59" stop-index="59"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="insert" start-index="62" stop-index="69"/>
                </assignment-value>
            </value>
            <value>
                <assignment-value>
                    <literal-expression value="2" start-index="74" stop-index="74"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="2" start-index="77" stop-index="77"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="insert2" start-index="80" stop-index="88"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_special_characters">
        <table name="t_order" start-delimiter="`" end-delimiter="`" start-index="12" stop-index="20"/>
        <columns start-index="22" stop-index="54">
            <column name="order_id" start-delimiter="`" end-delimiter="`" start-index="23" stop-index="32"/>
            <column name="user_id" start-delimiter="`" end-delimiter="`" start-index="35" stop-index="43"/>
            <column name="status" start-delimiter="`" end-delimiter="`" start-index="46" stop-index="53"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="1" start-index="64" stop-index="64"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="1" start-index="67" stop-index="67"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="insert" start-index="70" stop-index="77"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_special_syntax">
        <table name="t_order" start-index="33" stop-index="39"/>
        <columns start-index="41" stop-index="67">
            <column name="order_id" start-index="42" stop-index="49"/>
            <column name="user_id" start-index="52" stop-index="58"/>
            <column name="status" start-index="61" stop-index="66"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0"/>
                    <literal-expression value="1" start-index="77" stop-index="77"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1"/>
                    <literal-expression value="1" start-index="80" stop-index="80"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2"/>
                    <literal-expression value="insert" start-index="83" stop-index="90"/>
                </assignment-value>
            </value>
        </values>
        <comment start-index="7" stop-index="26" text="/*+ index(field1) */"/>
        <returning>
            <projections start-index="103" stop-index="169">
                <column-projection name="order_id" start-index="103" stop-index="110"/>
                <shorthand-projection start-index="113" stop-index="113"/>
                <shorthand-projection start-index="116" stop-index="124">
                    <owner name="t_order" start-index="116" stop-index="122"/>
                </shorthand-projection>
                <column-projection name="user_id" alias="u" start-index="127" stop-index="135"/>
                <column-projection name="status" alias="s" start-index="138" stop-index="156">
                    <owner name="t_order" start-index="138" stop-index="144"/>
                </column-projection>
                <expression-projection text="'OK'" alias="result" start-index="159" stop-index="169"/>
            </projections>
        </returning>
    </insert>

    <insert sql-case-id="insert_with_all_placeholders_for_table_identifier" parameters="1, 1, 'init'">
        <table name="t_order" start-index="12" stop-index="18"/>
        <columns start-index="20" stop-index="70">
            <column name="order_id" start-index="21" stop-index="36">
                <owner name="t_order" start-index="21" stop-index="27"/>
            </column>
            <column name="user_id" start-index="39" stop-index="53">
                <owner name="t_order" start-index="39" stop-index="45"/>
            </column>
            <column name="status" start-index="56" stop-index="69">
                <owner name="t_order" start-index="56" stop-index="62"/>
            </column>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="80" stop-index="80"/>
                    <literal-expression value="1" start-index="80" stop-index="80"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="83" stop-index="83"/>
                    <literal-expression value="1" start-index="83" stop-index="83"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2" start-index="86" stop-index="86"/>
                    <literal-expression value="init" start-index="86" stop-index="91"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_without_columns_with_all_placeholders" parameters="1, 1, 'init'">
        <table name="t_order" start-index="12" stop-index="18"/>
        <columns start-index="19" stop-index="19"/>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="28" stop-index="28"/>
                    <literal-expression value="1" start-index="28" stop-index="28"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="31" stop-index="31"/>
                    <literal-expression value="1" start-index="31" stop-index="31"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2" start-index="34" stop-index="34"/>
                    <literal-expression value="init" start-index="34" stop-index="39"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_set_with_all_placeholders" parameters="1, 1, 'init'">
        <table name="t_order" start-index="12" stop-index="18"/>
        <set start-index="20" stop-index="60" literal-stop-index="65">
            <assignment>
                <column name="order_id" start-index="24" stop-index="31"/>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="35" stop-index="35"/>
                    <literal-expression value="1" literal-start-index="35" literal-stop-index="35"/>
                </assignment-value>
            </assignment>
            <assignment>
                <column name="user_id" start-index="38" stop-index="44"/>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="48" stop-index="48"/>
                    <literal-expression value="1" literal-start-index="48" literal-stop-index="48"/>
                </assignment-value>
            </assignment>
            <assignment>
                <column name="status" start-index="51" stop-index="56"/>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2" start-index="60" stop-index="60"/>
                    <literal-expression value="init" literal-start-index="60" literal-stop-index="65"/>
                </assignment-value>
            </assignment>
        </set>
    </insert>
    <insert sql-case-id="insert_duplicate_key_update">
        <table name="t_order" start-index="12" stop-index="18"/>
        <set start-index="20" stop-index="34" literal-stop-index="34">
            <assignment>
                <column name="b" start-index="24" stop-index="24"/>
                <assignment-value>
                    <literal-expression value="11" literal-start-index="26" literal-stop-index="29"/>
                </assignment-value>
            </assignment>
            <assignment>
                <column name="a" start-index="32" stop-index="32"/>
                <assignment-value>
                    <literal-expression value="0" literal-start-index="34" literal-stop-index="34"/>
                </assignment-value>
            </assignment>
        </set>
        <on-duplicate-key-columns start-index="43" stop-index="76" literal-start-index="36" literal-stop-index="76">
            <assignment start-index="65" stop-index="69">
                <column name="b" start-index="65" stop-index="65"/>
                <assignment-value>
                    <common-expression literal-text="n.a" start-index="67" stop-index="69"/>
                </assignment-value>
            </assignment>
            <assignment start-index="72" stop-index="76">
                <column name="a" start-index="72" stop-index="72"/>
                <assignment-value>
                    <common-expression literal-text="n.b" start-index="74" stop-index="76"/>
                </assignment-value>
            </assignment>
        </on-duplicate-key-columns>
    </insert>
    <insert sql-case-id="insert_with_underscore_charset">
        <table name="t_order" start-index="12" stop-index="18"/>
        <columns start-index="19" stop-index="19"/>
        <values>
            <value>
                <assignment-value>
                    <common-expression literal-text="_utf160x1EC2" start-index="27" stop-index="39"/>
                </assignment-value>
            </value>
            <value>
                <assignment-value>
                    <common-expression literal-text="_utf160x1EC3" start-index="44" stop-index="56"/>
                </assignment-value>
            </value>
            <value>
                <assignment-value>
                    <common-expression literal-text="_utf160x1EC5" start-index="61" stop-index="73"/>
                </assignment-value>
            </value>
            <value>
                <assignment-value>
                    <common-expression literal-text="_utf160x1EC0" start-index="78" stop-index="90"/>
                </assignment-value>
            </value>
            <value>
                <assignment-value>
                    <common-expression literal-text="_utf160x1EC7" start-index="95" stop-index="107"/>
                </assignment-value>
            </value>
            <value>
                <assignment-value>
                    <common-expression literal-text="_Utf160x1EBF" start-index="112" stop-index="124"/>
                </assignment-value>
            </value>
        </values>
    </insert>
    <insert sql-case-id="insert_with_partial_placeholders" parameters="1, 1">
        <table name="t_order" start-index="12" stop-index="18"/>
        <columns start-index="20" stop-index="46">
            <column name="order_id" start-index="21" stop-index="28"/>
            <column name="user_id" start-index="31" stop-index="37"/>
            <column name="status" start-index="40" stop-index="45"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="56" stop-index="56"/>
                    <literal-expression value="1" start-index="56" stop-index="56"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="59" stop-index="59"/>
                    <literal-expression value="1" start-index="59" stop-index="59"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="insert" start-index="62" stop-index="69"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_set_with_partial_placeholders" parameters="1, 1">
        <table name="t_order" start-index="12" stop-index="18"/>
        <set start-index="20" stop-index="67">
            <assignment>
                <column name="order_id" start-index="24" stop-index="31"/>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="35" stop-index="35"/>
                    <literal-expression value="1" start-index="35" stop-index="35"/>
                </assignment-value>
            </assignment>
            <assignment>
                <column name="user_id" start-index="38" stop-index="44"/>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="48" stop-index="48"/>
                    <literal-expression value="1" start-index="48" stop-index="48"/>
                </assignment-value>
            </assignment>
            <assignment>
                <column name="status" start-index="51" stop-index="56"/>
                <assignment-value>
                    <literal-expression value="insert" start-index="60" stop-index="67"/>
                </assignment-value>
            </assignment>
        </set>
    </insert>

    <insert sql-case-id="insert_with_generate_key_column" parameters="10000, 1000, 10">
        <table name="t_order_item" start-index="12" stop-index="23"/>
        <columns start-index="24" stop-index="74">
            <column name="item_id" start-index="25" stop-index="31"/>
            <column name="order_id" start-index="34" stop-index="41"/>
            <column name="user_id" start-index="44" stop-index="50"/>
            <column name="status" start-index="53" stop-index="58"/>
            <column name="creation_date" start-index="61" stop-index="73"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="84" stop-index="84"/>
                    <literal-expression value="10000" start-index="84" stop-index="88"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="87" stop-index="87"/>
                    <literal-expression value="1000" start-index="91" stop-index="94"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2" start-index="90" stop-index="90"/>
                    <literal-expression value="10" start-index="97" stop-index="98"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="insert" start-index="93" stop-index="100" literal-start-index="101"
                                        literal-stop-index="108"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="2017-08-08" start-index="103" stop-index="114" literal-start-index="111"
                                        literal-stop-index="122"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_without_generate_key_column" parameters="1000, 10">
        <table name="t_order_item" start-index="12" stop-index="23"/>
        <columns start-index="24" stop-index="65">
            <column name="order_id" start-index="25" stop-index="32"/>
            <column name="user_id" start-index="35" stop-index="41"/>
            <column name="status" start-index="44" stop-index="49"/>
            <column name="creation_date" start-index="52" stop-index="64"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="75" stop-index="75"/>
                    <literal-expression value="1000" start-index="75" stop-index="78"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="78" stop-index="78"/>
                    <literal-expression value="10" start-index="81" stop-index="82"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="insert" start-index="81" stop-index="88" literal-start-index="85"
                                        literal-stop-index="92"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="2017-08-08" start-index="91" stop-index="102" literal-start-index="95"
                                        literal-stop-index="106"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_set_with_generate_key_column" parameters="10000, 1000, 10">
        <table name="t_order_item" start-index="12" stop-index="23"/>
        <set start-index="25" stop-index="113" literal-stop-index="121">
            <assignment>
                <column name="item_id" start-index="29" stop-index="35"/>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="39" stop-index="39"/>
                    <literal-expression value="10000" start-index="39" stop-index="43"/>
                </assignment-value>
            </assignment>
            <assignment>
                <column name="order_id" start-index="42" stop-index="49" literal-start-index="46"
                        literal-stop-index="53"/>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="53" stop-index="53"/>
                    <literal-expression value="1000" start-index="57" stop-index="60"/>
                </assignment-value>
            </assignment>
            <assignment>
                <column name="user_id" start-index="56" stop-index="62" literal-start-index="63"
                        literal-stop-index="69"/>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2" start-index="66" stop-index="66"/>
                    <literal-expression value="10" start-index="73" stop-index="74"/>
                </assignment-value>
            </assignment>
            <assignment>
                <column name="status" start-index="69" stop-index="74" literal-start-index="77"
                        literal-stop-index="82"/>
                <assignment-value>
                    <literal-expression value="insert" start-index="78" stop-index="85" literal-start-index="86"
                                        literal-stop-index="93"/>
                </assignment-value>
            </assignment>
            <assignment>
                <column name="creation_date" start-index="88" stop-index="100" literal-start-index="96"
                        literal-stop-index="108"/>
                <assignment-value>
                    <literal-expression value="2017-08-08" start-index="102" stop-index="113" literal-start-index="110"
                                        literal-stop-index="121"/>
                </assignment-value>
            </assignment>
        </set>
    </insert>

    <insert sql-case-id="insert_set_without_generate_key_column" parameters="1000, 10">
        <table name="t_order_item" start-index="12" stop-index="23"/>
        <set start-index="25" stop-index="100" literal-stop-index="104">
            <assignment>
                <column name="order_id" start-index="29" stop-index="36"/>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="40" stop-index="40"/>
                    <literal-expression value="1000" start-index="40" stop-index="43"/>
                </assignment-value>
            </assignment>
            <assignment>
                <column name="user_id" start-index="43" stop-index="49" literal-start-index="46"
                        literal-stop-index="52"/>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="53" stop-index="53"/>
                    <literal-expression value="10" start-index="56" stop-index="57"/>
                </assignment-value>
            </assignment>
            <assignment>
                <column name="status" start-index="56" stop-index="61" literal-start-index="60"
                        literal-stop-index="65"/>
                <assignment-value>
                    <literal-expression value="insert" start-index="65" stop-index="72" literal-start-index="69"
                                        literal-stop-index="76"/>
                </assignment-value>
            </assignment>
            <assignment>
                <column name="creation_date" start-index="75" stop-index="87" literal-start-index="79"
                        literal-stop-index="91"/>
                <assignment-value>
                    <literal-expression value="2017-08-08" start-index="89" stop-index="100" literal-start-index="93"
                                        literal-stop-index="104"/>
                </assignment-value>
            </assignment>
        </set>
    </insert>

    <insert sql-case-id="insert_on_duplicate_key_update" parameters="1, 1, 'init'">
        <table name="t_order" start-index="12" stop-index="18"/>
        <columns start-index="20" stop-index="46">
            <column name="order_id" start-index="21" stop-index="28"/>
            <column name="user_id" start-index="31" stop-index="37"/>
            <column name="status" start-index="40" stop-index="45"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="56" stop-index="56"/>
                    <literal-expression value="1" start-index="56" stop-index="56"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="59" stop-index="59"/>
                    <literal-expression value="1" start-index="59" stop-index="59"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2" start-index="62" stop-index="62"/>
                    <literal-expression value="init" start-index="62" stop-index="67"/>
                </assignment-value>
            </value>
        </values>
        <on-duplicate-key-columns start-index="65" stop-index="111" literal-start-index="70" literal-stop-index="116">
            <assignment start-index="89" stop-index="111" literal-start-index="94" literal-stop-index="116">
                <column name="status" start-index="89" stop-index="94" literal-start-index="94"
                        literal-stop-index="99"/>
                <assignment-value>
                    <function function-name="VALUES" text="VALUES(status)" start-index="98" stop-index="111"
                              literal-start-index="103" literal-stop-index="116">
                        <parameter>
                            <column name="status" start-index="105" stop-index="110" literal-start-index="110"
                                    literal-stop-index="115"/>
                        </parameter>
                    </function>
                </assignment-value>
            </assignment>
        </on-duplicate-key-columns>
    </insert>

    <insert sql-case-id="insert_on_duplicate_key_update_with_placeholders" parameters="1, 1, 'init', 'init'">
        <table name="t_order" start-index="12" stop-index="18"/>
        <columns start-index="20" stop-index="46">
            <column name="order_id" start-index="21" stop-index="28"/>
            <column name="user_id" start-index="31" stop-index="37"/>
            <column name="status" start-index="40" stop-index="45"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="56" stop-index="56"/>
                    <literal-expression value="1" start-index="56" stop-index="56"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="59" stop-index="59"/>
                    <literal-expression value="1" start-index="59" stop-index="59"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2" start-index="62" stop-index="62"/>
                    <literal-expression value="init" start-index="62" stop-index="67"/>
                </assignment-value>
            </value>
        </values>
        <on-duplicate-key-columns start-index="65" stop-index="98" literal-start-index="70" literal-stop-index="108">
            <assignment start-index="89" stop-index="98" literal-start-index="94" literal-stop-index="108">
                <column name="status" start-index="89" stop-index="94" literal-start-index="94"
                        literal-stop-index="99"/>
                <assignment-value>
                    <parameter-marker-expression parameter-index="3" start-index="98" stop-index="98"/>
                    <literal-expression value="init" start-index="103" stop-index="108"/>
                </assignment-value>
            </assignment>
        </on-duplicate-key-columns>
    </insert>

    <insert sql-case-id="insert_on_duplicate_key_update_with_placeholders_postgres" parameters="1, 1, 'init', 'init'">
        <table name="t_order" start-index="12" stop-index="18"/>
        <columns start-index="20" stop-index="46">
            <column name="order_id" start-index="21" stop-index="28"/>
            <column name="user_id" start-index="31" stop-index="37"/>
            <column name="status" start-index="40" stop-index="45"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="56" stop-index="56"/>
                    <literal-expression value="1" start-index="56" stop-index="56"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="59" stop-index="59"/>
                    <literal-expression value="1" start-index="59" stop-index="59"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2" start-index="62" stop-index="62"/>
                    <literal-expression value="init" start-index="62" stop-index="67"/>
                </assignment-value>
            </value>
        </values>
        <on-duplicate-key-columns start-index="65" stop-index="111" literal-start-index="70" literal-stop-index="121">
            <assignment start-index="102" stop-index="107" literal-start-index="107" literal-stop-index="121">
                <column name="status" start-index="102" stop-index="107" literal-start-index="107"
                        literal-stop-index="112"/>
                <assignment-value>
                    <parameter-marker-expression parameter-index="3" start-index="111" stop-index="111"/>
                    <literal-expression value="init" start-index="116" stop-index="121"/>
                </assignment-value>
            </assignment>
        </on-duplicate-key-columns>
    </insert>

    <insert sql-case-id="insert_set_with_all_placeholders_for_table_identifier" parameters="1, 1, 'init'">
        <table name="t_order" start-index="12" stop-index="18"/>
        <set start-index="20" stop-index="84" literal-stop-index="89">
            <assignment>
                <column name="order_id" start-index="24" stop-index="39">
                    <owner name="t_order" start-index="24" stop-index="30"/>
                </column>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="43" stop-index="43"/>
                    <literal-expression value="1" start-index="43" stop-index="43"/>
                </assignment-value>
            </assignment>
            <assignment>
                <column name="user_id" start-index="46" stop-index="60">
                    <owner name="t_order" start-index="46" stop-index="52"/>
                </column>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="64" stop-index="64"/>
                    <literal-expression value="1" start-index="64" stop-index="64"/>
                </assignment-value>
            </assignment>
            <assignment>
                <column name="status" start-index="67" stop-index="80">
                    <owner name="t_order" start-index="67" stop-index="73"/>
                </column>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2" start-index="84" stop-index="84"/>
                    <literal-expression value="init" start-index="84" stop-index="89"/>
                </assignment-value>
            </assignment>
        </set>
    </insert>

    <insert sql-case-id="insert_on_duplicate_key_update_with_table_identifier" parameters="1, 1, 'init'">
        <table name="t_order" start-index="12" stop-index="18"/>
        <columns start-index="20" stop-index="70">
            <column name="order_id" start-index="21" stop-index="36">
                <owner name="t_order" start-index="21" stop-index="27"/>
            </column>
            <column name="user_id" start-index="39" stop-index="53">
                <owner name="t_order" start-index="39" stop-index="45"/>
            </column>
            <column name="status" start-index="56" stop-index="69">
                <owner name="t_order" start-index="56" stop-index="62"/>
            </column>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="80" stop-index="80"/>
                    <literal-expression value="1" start-index="80" stop-index="80"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="83" stop-index="83"/>
                    <literal-expression value="1" start-index="83" stop-index="83"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2" start-index="86" stop-index="86"/>
                    <literal-expression value="init" start-index="86" stop-index="91"/>
                </assignment-value>
            </value>
        </values>
        <on-duplicate-key-columns start-index="89" stop-index="151" literal-start-index="94" literal-stop-index="156">
            <assignment start-index="118" stop-index="151" literal-start-index="123" literal-stop-index="156">
                <column name="status" start-index="113" stop-index="126" literal-start-index="118"
                        literal-stop-index="131">
                    <owner name="t_order" start-index="113" stop-index="119" literal-start-index="118"
                           literal-stop-index="124"/>
                </column>
                <assignment-value>
                    <function function-name="VALUES" text="VALUES(t_order.status)" start-index="130" stop-index="151"
                              literal-start-index="135" literal-stop-index="156">
                        <parameter>
                            <column name="status" start-index="137" stop-index="150" literal-start-index="142"
                                    literal-stop-index="155">
                                <owner name="t_order" start-index="137" stop-index="143" literal-start-index="142"
                                       literal-stop-index="148"/>
                            </column>
                        </parameter>
                    </function>
                </assignment-value>
            </assignment>
        </on-duplicate-key-columns>
    </insert>

    <insert sql-case-id="insert_with_batch" parameters="1000, 10, 'init', 1100, 11, 'init'">
        <table name="t_order" start-index="12" stop-index="18"/>
        <columns start-index="20" stop-index="46">
            <column name="order_id" start-index="21" stop-index="28"/>
            <column name="user_id" start-index="31" stop-index="37"/>
            <column name="status" start-index="40" stop-index="45"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="56" stop-index="56"/>
                    <literal-expression value="1000" start-index="56" stop-index="59"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="59" stop-index="59"/>
                    <literal-expression value="10" start-index="62" stop-index="63"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2" start-index="62" stop-index="62"/>
                    <literal-expression value="init" start-index="66" stop-index="71"/>
                </assignment-value>
            </value>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="3" start-index="67" stop-index="67"/>
                    <literal-expression value="1100" start-index="76" stop-index="79"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="4" start-index="70" stop-index="70"/>
                    <literal-expression value="11" start-index="82" stop-index="83"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="5" start-index="73" stop-index="73"/>
                    <literal-expression value="init" start-index="86" stop-index="91"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_batch_and_irregular_parameters" parameters="1, 2, 2, 'init'">
        <table name="t_order" start-index="12" stop-index="18"/>
        <columns start-index="20" stop-index="46">
            <column name="order_id" start-index="21" stop-index="28"/>
            <column name="user_id" start-index="31" stop-index="37"/>
            <column name="status" start-index="40" stop-index="45"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="56" stop-index="56"/>
                    <literal-expression value="1" start-index="56" stop-index="56"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="1" start-index="59" stop-index="59"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="insert" start-index="62" stop-index="69"/>
                </assignment-value>
            </value>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="74" stop-index="74"/>
                    <literal-expression value="2" start-index="74" stop-index="74"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2" start-index="77" stop-index="77"/>
                    <literal-expression value="2" start-index="77" stop-index="77"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="3" start-index="80" stop-index="80"/>
                    <literal-expression value="init" start-index="80" stop-index="85"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_batch_and_composite_expression" parameters="1, 1, 'init', 2, 2, 'init'">
        <table name="t_order" start-index="12" stop-index="18"/>
        <columns start-index="20" stop-index="46">
            <column name="order_id" start-index="21" stop-index="28"/>
            <column name="user_id" start-index="31" stop-index="37"/>
            <column name="status" start-index="40" stop-index="45"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="56" stop-index="56"/>
                    <literal-expression value="1" start-index="56" stop-index="56"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="59" stop-index="59"/>
                    <literal-expression value="1" start-index="59" stop-index="59"/>
                </assignment-value>
                <assignment-value>
                    <function function-name="SUBSTR" text="SUBSTR(?, 1)" literal-text="SUBSTR('init', 1)"
                              literal-start-index="62" literal-stop-index="78" start-index="62" stop-index="73">
                        <parameter>
                            <parameter-marker-expression parameter-index="2" start-index="69" stop-index="69"/>
                            <literal-expression value="init" literal-start-index="69" literal-stop-index="74"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="1" start-index="72" stop-index="72" literal-start-index="77"
                                                literal-stop-index="77"/>
                        </parameter>
                    </function>
                </assignment-value>
            </value>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="3" start-index="78" stop-index="78"/>
                    <literal-expression value="2" literal-start-index="83" literal-stop-index="83"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="4" start-index="81" stop-index="81"/>
                    <literal-expression value="2" literal-start-index="86" literal-stop-index="86"/>
                </assignment-value>
                <assignment-value>
                    <function function-name="SUBSTR" text="SUBSTR(?, 1)" literal-text="SUBSTR('init', 1)"
                              start-index="84" stop-index="95" literal-start-index="89" literal-stop-index="105">
                        <parameter>
                            <parameter-marker-expression parameter-index="5" start-index="91" stop-index="91"/>
                            <literal-expression value="init" literal-start-index="96" literal-stop-index="101"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="1" start-index="94" stop-index="94" literal-start-index="104"
                                                literal-stop-index="104"/>
                        </parameter>
                    </function>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_batch_and_with_generate_key_column" parameters="10000, 1000, 10, 10010, 1001, 10">
        <table name="t_order_item" start-index="12" stop-index="23"/>
        <columns start-index="24" stop-index="74">
            <column name="item_id" start-index="25" stop-index="31"/>
            <column name="order_id" start-index="34" stop-index="41"/>
            <column name="user_id" start-index="44" stop-index="50"/>
            <column name="status" start-index="53" stop-index="58"/>
            <column name="creation_date" start-index="61" stop-index="73"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="84" stop-index="84"/>
                    <literal-expression value="10000" start-index="84" stop-index="88"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="87" stop-index="87"/>
                    <literal-expression value="1000" start-index="91" stop-index="94"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2" start-index="90" stop-index="90"/>
                    <literal-expression value="10" start-index="97" stop-index="98"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="insert" start-index="93" stop-index="100" literal-start-index="101"
                                        literal-stop-index="108"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="2017-08-08" start-index="103" stop-index="114" literal-start-index="111"
                                        literal-stop-index="122"/>
                </assignment-value>
            </value>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="3" start-index="119" stop-index="119"/>
                    <literal-expression value="10010" start-index="127" stop-index="131"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="4" start-index="122" stop-index="122"/>
                    <literal-expression value="1001" start-index="134" stop-index="137"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="5" start-index="125" stop-index="125"/>
                    <literal-expression value="10" start-index="140" stop-index="141"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="insert" start-index="128" stop-index="135" literal-start-index="144"
                                        literal-stop-index="151"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="2017-08-08" start-index="138" stop-index="149" literal-start-index="154"
                                        literal-stop-index="165"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_batch_and_without_generate_key_column" parameters="1000, 10, 1001, 10">
        <table name="t_order_item" start-index="12" stop-index="23"/>
        <columns start-index="24" stop-index="65">
            <column name="order_id" start-index="25" stop-index="32"/>
            <column name="user_id" start-index="35" stop-index="41"/>
            <column name="status" start-index="44" stop-index="49"/>
            <column name="creation_date" start-index="52" stop-index="64"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="75" stop-index="75"/>
                    <literal-expression value="1000" start-index="75" stop-index="78"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="78" stop-index="78"/>
                    <literal-expression value="10" start-index="81" stop-index="82"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="insert" start-index="81" stop-index="88" literal-start-index="85"
                                        literal-stop-index="92"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="2017-08-08" start-index="91" stop-index="102" literal-start-index="95"
                                        literal-stop-index="106"/>
                </assignment-value>
            </value>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2" start-index="107" stop-index="107"/>
                    <literal-expression value="1001" start-index="111" stop-index="114"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="3" start-index="110" stop-index="110"/>
                    <literal-expression value="10" start-index="117" stop-index="118"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="insert" start-index="113" stop-index="120" literal-start-index="121"
                                        literal-stop-index="128"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="2017-08-08" start-index="123" stop-index="134" literal-start-index="131"
                                        literal-stop-index="142"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_geography" parameters="7, 200, '{&quot;rule&quot;:&quot;null&quot;}'">
        <table name="t_order" start-index="12" stop-index="18"/>
        <columns start-index="19" stop-index="55">
            <column name="user_id" start-index="20" stop-index="26"/>
            <column name="order_id" start-index="29" stop-index="36"/>
            <column name="start_point" start-index="39" stop-index="49"/>
            <column name="rule" start-index="51" stop-index="54"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="65" stop-index="65"/>
                    <literal-expression value="7" start-index="65" stop-index="65"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="68" stop-index="68"/>
                    <literal-expression value="200" start-index="68" stop-index="70"/>
                </assignment-value>
                <assignment-value>
                    <common-expression text="ST_GeographyFromText('SRID=4326;POINT(100 200)')" start-index="71"
                                       stop-index="118" literal-start-index="73" literal-stop-index="120"/>
                    <function function-name="ST_GeographyFromText"
                              text="ST_GeographyFromText('SRID=4326;POINT(100 200)')" start-index="71" stop-index="118"
                              literal-start-index="73" literal-stop-index="120">
                        <parameter>
                            <literal-expression value="SRID=4326;POINT(100 200)" literal-start-index="94"
                                                start-index="92" literal-stop-index="119" stop-index="117"/>
                        </parameter>
                    </function>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2" start-index="121" stop-index="128"/>
                    <common-expression text="'{&quot;rule&quot;:&quot;null&quot;}'::jsonb" start-index="123"
                                       stop-index="146"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_without_columns_and_with_generate_key_column" parameters="10000, 1000, 10">
        <table name="t_order_item" start-index="12" stop-index="23"/>
        <columns start-index="24" stop-index="24"/>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="32" stop-index="32"/>
                    <literal-expression value="10000" literal-start-index="32" literal-stop-index="36"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="35" stop-index="35"/>
                    <literal-expression value="1000" start-index="35" stop-index="40" literal-start-index="39"
                                        literal-stop-index="42"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2" start-index="38" stop-index="38"/>
                    <literal-expression value="10" start-index="38" stop-index="41" literal-start-index="45"
                                        literal-stop-index="46"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="insert" start-index="41" stop-index="48" literal-start-index="49"
                                        literal-stop-index="56"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="2017-08-08" start-index="51" stop-index="62" literal-start-index="59"
                                        literal-stop-index="70"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_without_columns_and_without_generate_key_column" parameters="1000, 10">
        <table name="t_order_item" start-index="12" stop-index="23"/>
        <columns start-index="24" stop-index="24"/>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="32" stop-index="32"/>
                    <literal-expression value="1000" start-index="32" stop-index="35"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="35" stop-index="35"/>
                    <literal-expression value="10" start-index="38" stop-index="39"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="insert" start-index="38" stop-index="45" literal-start-index="42"
                                        literal-stop-index="49"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="2017-08-08" start-index="48" stop-index="59" literal-start-index="52"
                                        literal-stop-index="63"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <!-- // TODO
    <insert sql-case-id="assertInsertSelect" sql="INSERT INTO `order` ('order_id', 'state') (SELECT 1, 'RUNNING' FROM dual UNION ALL SELECT 2, 'RUNNING' FROM dual )"">
        <table name="order" />
        <condition-contexts>
           <condition-context/>
        </condition-contexts>
    </insert>
    -->

    <insert sql-case-id="insert_with_one_auto_increment_column">
        <table name="t_auto_increment_table" start-index="12" stop-index="33"/>
        <columns start-index="34" stop-index="34"/>
        <values>
            <value/>
        </values>
    </insert>

    <insert sql-case-id="insert_with_double_value">
        <table name="t_double_test" start-index="12" stop-index="24"/>
        <columns start-index="25" stop-index="30">
            <column name="col1" start-index="26" stop-index="29"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="1.22" start-index="39" stop-index="42"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_null_value">
        <table name="t_null_value_test" start-index="12" stop-index="28"/>
        <columns start-index="29" stop-index="34">
            <column name="col1" start-index="30" stop-index="33"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="null" start-index="43" stop-index="46"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_blob_value">
        <table name="t_blob_value_test" start-index="12" stop-index="28"/>
        <columns start-index="29" stop-index="34">
            <column name="col1" start-index="30" stop-index="33"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <common-expression text="_BINARY'This is a binary value.'" start-index="43" stop-index="74"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_function" parameters="1000, 10">
        <table name="t_order" start-index="12" stop-index="18"/>
        <columns start-index="19" stop-index="51">
            <column name="present_date" start-index="20" stop-index="31"/>
            <column name="order_id" start-index="34" stop-index="41"/>
            <column name="user_id" start-index="44" stop-index="50"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <function function-name="curdate" text="curdate()" start-index="61" stop-index="69"
                              literal-start-index="61" literal-stop-index="69"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="72" stop-index="72"/>
                    <literal-expression value="1000" start-index="72" stop-index="75"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="75" stop-index="75"/>
                    <literal-expression value="10" start-index="78" stop-index="79"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_on_duplicate_key_update_with_complicated_expression" parameters="1, 2, 45, 2000">
        <table name="emp" start-index="12" stop-index="14"/>
        <columns start-index="15" stop-index="42">
            <column name="order_id" start-index="16" stop-index="23"/>
            <column name="emp_id" start-index="25" stop-index="30"/>
            <column name="age" start-index="32" stop-index="34"/>
            <column name="salary" start-index="36" stop-index="41"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="52" stop-index="52"/>
                    <literal-expression value="1" start-index="52" stop-index="52"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="54" stop-index="54"/>
                    <literal-expression value="2" start-index="54" stop-index="54"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2" start-index="56" stop-index="56"/>
                    <literal-expression value="45" start-index="56" stop-index="57"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="3" start-index="58" stop-index="58"/>
                    <literal-expression value="2000" start-index="59" stop-index="62"/>
                </assignment-value>
            </value>
        </values>
        <on-duplicate-key-columns start-index="61" stop-index="126" literal-start-index="65" literal-stop-index="130">
            <assignment>
                <column name="salary" start-index="85" stop-index="90" literal-start-index="89"
                        literal-stop-index="94"/>
                <assignment-value>
                    <binary-operation-expression start-index="94" stop-index="126" literal-start-index="98"
                                                 literal-stop-index="130">
                        <left>
                            <binary-operation-expression start-index="94" stop-index="122" literal-start-index="98"
                                                         literal-stop-index="126">
                                <left>
                                    <function function-name="VALUES" text="VALUES(salary)" start-index="94"
                                              stop-index="107" literal-start-index="98" literal-stop-index="111">
                                        <parameter>
                                            <column name="salary" start-index="101" stop-index="106"
                                                    literal-start-index="105" literal-stop-index="110"/>
                                        </parameter>
                                        <literalText>VALUES(salary)</literalText>
                                    </function>
                                </left>
                                <operator>+</operator>
                                <right>
                                    <function function-name="VALUES" text="VALUES(salary)" start-index="109"
                                              stop-index="122" literal-start-index="113" literal-stop-index="126">
                                        <parameter>
                                            <column name="salary" start-index="116" stop-index="121"
                                                    literal-start-index="120" literal-stop-index="125"/>
                                        </parameter>
                                        <literalText>VALUES(salary)</literalText>
                                    </function>
                                </right>
                            </binary-operation-expression>
                        </left>
                        <operator>*</operator>
                        <right>
                            <literal-expression value="0.2" start-index="124" stop-index="126" literal-start-index="128"
                                                literal-stop-index="130"/>
                        </right>
                    </binary-operation-expression>
                </assignment-value>
            </assignment>
        </on-duplicate-key-columns>
    </insert>

    <insert sql-case-id="insert_with_unix_timestamp_function" parameters="'2019-10-19', 1000, 10">
        <table name="t_order" start-index="12" stop-index="18"/>
        <columns start-index="19" stop-index="45">
            <column name="status" start-index="20" stop-index="25"/>
            <column name="order_id" start-index="28" stop-index="35"/>
            <column name="user_id" start-index="38" stop-index="44"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <function function-name="unix_timestamp" text="unix_timestamp(?)"
                              literal-text="unix_timestamp('2019-10-19')" start-index="55" stop-index="71"
                              literal-start-index="55" literal-stop-index="82">
                        <parameter>
                            <literal-expression value="2019-10-19" start-index="70" stop-index="81"/>
                            <parameter-marker-expression parameter-index="0" start-index="70" stop-index="70"/>
                        </parameter>
                    </function>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="74" stop-index="74"/>
                    <literal-expression value="1000" start-index="85" stop-index="88"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2" start-index="77" stop-index="77"/>
                    <literal-expression value="10" start-index="91" stop-index="92"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_aggregation_function_column_name" parameters="1, 1, 1">
        <table name="t_order" start-index="12" stop-index="18"/>
        <columns start-index="20" stop-index="45">
            <column name="order_id" start-index="21" stop-index="28"/>
            <column name="user_id" start-index="31" stop-index="37"/>
            <column name="count" start-index="40" stop-index="44"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="55" stop-index="55"/>
                    <literal-expression value="1" start-index="55" stop-index="55"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="58" stop-index="58"/>
                    <literal-expression value="1" start-index="58" stop-index="58"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2" start-index="61" stop-index="61"/>
                    <literal-expression value="1" start-index="61" stop-index="61"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_str_to_date" parameters="'2019-12-10', 1, 1">
        <table name="t_order" start-index="12" stop-index="18"/>
        <columns start-index="19" stop-index="51">
            <column name="present_date" start-index="20" stop-index="31"/>
            <column name="order_id" start-index="34" stop-index="41"/>
            <column name="user_id" start-index="44" stop-index="50"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="89" stop-index="89"/>
                    <function function-name="str_to_date" text="str_to_date(?, '%Y-%m-%d')"
                              literal-text="str_to_date('2019-12-10', '%Y-%m-%d')" start-index="61" stop-index="86"
                              literal-start-index="61" literal-stop-index="97">
                        <parameter>
                            <literal-expression value="2019-12-10" start-index="73" stop-index="73"
                                                literal-start-index="73" literal-stop-index="84"/>
                            <parameter-marker-expression parameter-index="0" start-index="73" stop-index="73"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="%Y-%m-%d" start-index="76" stop-index="85"
                                                literal-start-index="87" literal-stop-index="96"/>
                        </parameter>
                    </function>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="89" stop-index="89"/>
                    <literal-expression value="1" start-index="100" stop-index="100"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2" start-index="92" stop-index="92"/>
                    <literal-expression value="1" start-index="103" stop-index="103"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_on_duplicate_key_update_with_base64_aes_encrypt" parameters="1, 1, 'init'">
        <table name="t_order" start-index="12" stop-index="18"/>
        <set start-index="20" stop-index="111" literal-stop-index="116">
            <assignment start-index="24" stop-index="35">
                <column name="order_id" start-index="24" stop-index="31"/>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="35" stop-index="35"/>
                    <literal-expression value="1" start-index="35" stop-index="35" literal-start-index="35"
                                        literal-stop-index="35"/>
                </assignment-value>
            </assignment>
            <assignment start-index="38" stop-index="48">
                <column name="user_id" start-index="38" stop-index="44"/>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="48" stop-index="48"/>
                    <literal-expression value="1" start-index="48" stop-index="48" literal-start-index="48"
                                        literal-stop-index="48"/>
                </assignment-value>
            </assignment>
            <assignment start-index="51" stop-index="111" literal-stop-index="116">
                <column name="status" start-index="51" stop-index="56"/>
                <assignment-value>
                    <function function-name="convert" text="convert(to_base64(aes_encrypt(?, 'key')) USING utf8)"
                              literal-text="convert(to_base64(aes_encrypt('init', 'key')) USING utf8)" start-index="60"
                              stop-index="111" literal-start-index="60" literal-stop-index="116">
                        <parameter>
                            <function function-name="to_base64" text="to_base64(aes_encrypt(?, 'key'))"
                                      literal-text="to_base64(aes_encrypt('init', 'key'))" start-index="68"
                                      stop-index="99" literal-start-index="68" literal-stop-index="104">
                                <parameter>
                                    <function function-name="aes_encrypt" text="aes_encrypt(?, 'key')"
                                              literal-text="aes_encrypt('init', 'key')" start-index="78" stop-index="98"
                                              literal-start-index="78" literal-stop-index="103">
                                        <parameter>
                                            <literal-expression value="init" start-index="85" stop-index="90"
                                                                literal-start-index="90" literal-stop-index="95"/>
                                            <parameter-marker-expression parameter-index="2" start-index="90"
                                                                         stop-index="90"/>
                                        </parameter>
                                        <parameter>
                                            <literal-expression value="key" start-index="93" stop-index="97"
                                                                literal-start-index="98" literal-stop-index="102"/>
                                        </parameter>
                                    </function>
                                </parameter>
                            </function>
                        </parameter>
                    </function>
                </assignment-value>
            </assignment>
        </set>
        <on-duplicate-key-columns start-index="113" stop-index="159" literal-start-index="118" literal-stop-index="164">
            <assignment start-index="118" stop-index="164" literal-start-index="123" literal-stop-index="169">
                <column name="status" start-index="137" stop-index="142" literal-start-index="142"
                        literal-stop-index="147"/>
                <assignment-value>
                    <common-expression text="VALUES(status)" start-index="146" stop-index="159"
                                       literal-start-index="151" literal-stop-index="164"/>
                    <function function-name="VALUES" text="VALUES(status)" start-index="146" stop-index="159"
                              literal-start-index="151" literal-stop-index="164">
                        <parameter>
                            <column name="status" start-index="153" stop-index="158" literal-start-index="158"
                                    literal-stop-index="163"/>
                        </parameter>
                    </function>
                </assignment-value>
            </assignment>
        </on-duplicate-key-columns>
    </insert>

    <insert sql-case-id="insert_all_with_all_placeholders" parameters="1, 1, 'init', 2, 2, 'init', 3, 3, 'init'">
        <multi-table-insert-type value="ALL"/>
        <multi-table-insert-into start-index="11" stop-index="183" literal-start-index="11" literal-stop-index="198">
            <insert-statement parameters="1, 1, 'init'">
                <table name="t_order" start-index="16" stop-index="22"/>
                <columns start-index="24" stop-index="50">
                    <column name="order_id" start-index="25" stop-index="32"/>
                    <column name="user_id" start-index="35" stop-index="41"/>
                    <column name="status" start-index="44" stop-index="49"/>
                </columns>
                <values>
                    <value>
                        <assignment-value>
                            <parameter-marker-expression parameter-index="0" start-index="60" stop-index="60"/>
                            <literal-expression value="1" start-index="60" stop-index="60"/>
                        </assignment-value>
                        <assignment-value>
                            <parameter-marker-expression parameter-index="1" start-index="63" stop-index="63"/>
                            <literal-expression value="1" start-index="63" stop-index="63"/>
                        </assignment-value>
                        <assignment-value>
                            <parameter-marker-expression parameter-index="2" start-index="66" stop-index="66"/>
                            <literal-expression value="init" start-index="66" stop-index="71"/>
                        </assignment-value>
                    </value>
                </values>
            </insert-statement>
            <insert-statement parameters="2, 2, 'init'">
                <table name="t_order" start-index="74" stop-index="80" literal-start-index="79"
                       literal-stop-index="85"/>
                <columns start-index="82" stop-index="108" literal-start-index="87" literal-stop-index="113">
                    <column name="order_id" start-index="83" stop-index="90" literal-start-index="88"
                            literal-stop-index="95"/>
                    <column name="user_id" start-index="93" stop-index="99" literal-start-index="98"
                            literal-stop-index="104"/>
                    <column name="status" start-index="102" stop-index="107" literal-start-index="107"
                            literal-stop-index="112"/>
                </columns>
                <values>
                    <value>
                        <assignment-value>
                            <parameter-marker-expression parameter-index="3" start-index="118" stop-index="118"/>
                            <literal-expression value="2" start-index="123" stop-index="123"/>
                        </assignment-value>
                        <assignment-value>
                            <parameter-marker-expression parameter-index="4" start-index="121" stop-index="121"/>
                            <literal-expression value="2" start-index="126" stop-index="126"/>
                        </assignment-value>
                        <assignment-value>
                            <parameter-marker-expression parameter-index="5" start-index="124" stop-index="124"/>
                            <literal-expression value="init" start-index="129" stop-index="134"/>
                        </assignment-value>
                    </value>
                </values>
            </insert-statement>
            <insert-statement parameters="2, 2, 'init'">
                <table name="t_order" start-index="132" stop-index="138" literal-start-index="142"
                       literal-stop-index="148"/>
                <columns start-index="140" stop-index="166" literal-start-index="150" literal-stop-index="176">
                    <column name="order_id" start-index="141" stop-index="148" literal-start-index="151"
                            literal-stop-index="158"/>
                    <column name="user_id" start-index="151" stop-index="157" literal-start-index="161"
                            literal-stop-index="167"/>
                    <column name="status" start-index="160" stop-index="165" literal-start-index="170"
                            literal-stop-index="175"/>
                </columns>
                <values>
                    <value>
                        <assignment-value>
                            <parameter-marker-expression parameter-index="6" start-index="176" stop-index="176"/>
                            <literal-expression value="3" start-index="186" stop-index="186"/>
                        </assignment-value>
                        <assignment-value>
                            <parameter-marker-expression parameter-index="7" start-index="179" stop-index="179"/>
                            <literal-expression value="3" start-index="189" stop-index="189"/>
                        </assignment-value>
                        <assignment-value>
                            <parameter-marker-expression parameter-index="8" start-index="182" stop-index="182"/>
                            <literal-expression value="init" start-index="192" stop-index="197"/>
                        </assignment-value>
                    </value>
                </values>
            </insert-statement>
        </multi-table-insert-into>
        <select>
            <from>
                <simple-table name="dual" start-index="199" stop-index="202" literal-start-index="214"
                              literal-stop-index="217"/>
            </from>
            <projections start-index="192" stop-index="192" literal-start-index="207" literal-stop-index="207">
                <shorthand-projection start-index="192" stop-index="192" literal-start-index="207"
                                      literal-stop-index="207"/>
            </projections>
        </select>
    </insert>

    <insert sql-case-id="insert_with_str_date_add" parameters="1, 1, 1">
        <table name="t_order" start-index="12" stop-index="18"/>
        <columns start-index="19" stop-index="51">
            <column name="present_date" start-index="20" stop-index="31"/>
            <column name="order_id" start-index="34" stop-index="41"/>
            <column name="user_id" start-index="44" stop-index="50"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <function function-name="date_add" text="date_add(now(),interval ? second)"
                              literal-text="date_add(now(),interval 1 second)" start-index="61" stop-index="93"
                              literal-start-index="61" literal-stop-index="93">
                        <parameter>
                            <function function-name="now" text="now()" start-index="70" stop-index="74"/>
                        </parameter>
                        <parameter>
                            <function function-name="interval" text="interval" start-index="76" stop-index="83">
                                <parameter>
                                    <parameter-marker-expression parameter-index="0" start-index="85" stop-index="85"/>
                                    <literal-expression value="1" start-index="85" stop-index="85"/>
                                </parameter>
                                <parameter>
                                    <literal-expression value="second" start-index="87" stop-index="92"/>
                                </parameter>
                            </function>
                        </parameter>
                    </function>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="96" stop-index="96"/>
                    <literal-expression value="1" start-index="96" stop-index="96"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2" start-index="99" stop-index="99"/>
                    <literal-expression value="1" start-index="99" stop-index="99"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_select_with_all_columns" parameters="100">
        <table name="t_order" start-index="12" stop-index="18"/>
        <columns start-index="20" stop-index="46">
            <column name="order_id" start-index="21" stop-index="28"/>
            <column name="user_id" start-index="31" stop-index="37"/>
            <column name="status" start-index="40" stop-index="45"/>
        </columns>
        <select parameters="100">
            <from>
                <simple-table name="t_order" start-index="86" stop-index="92"/>
            </from>
            <projections start-index="55" stop-index="79">
                <column-projection name="order_id" start-index="55" stop-index="62"/>
                <column-projection name="user_id" start-index="65" stop-index="71"/>
                <column-projection name="status" start-index="74" stop-index="79"/>
            </projections>
            <where start-index="94" stop-index="111" literal-stop-index="113">
                <expr>
                    <binary-operation-expression start-index="100" stop-index="111" literal-stop-index="113">
                        <left>
                            <column name="order_id" start-index="100" stop-index="107"/>
                        </left>
                        <operator>=</operator>
                        <right>
                            <literal-expression value="100" start-index="111" stop-index="113"/>
                            <parameter-marker-expression parameter-index="0" start-index="111" stop-index="111"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </where>
        </select>
    </insert>

    <insert sql-case-id="insert_select_without_columns" parameters="100">
        <table name="t_order" start-index="12" stop-index="18"/>
        <columns start-index="19" stop-index="19"/>
        <select parameters="100">
            <from>
                <simple-table name="t_order" start-index="58" stop-index="64"/>
            </from>
            <projections start-index="27" stop-index="51">
                <column-projection name="order_id" start-index="27" stop-index="34"/>
                <column-projection name="user_id" start-index="37" stop-index="43"/>
                <column-projection name="status" start-index="46" stop-index="51"/>
            </projections>
            <where start-index="66" stop-index="83" literal-stop-index="85">
                <expr>
                    <binary-operation-expression start-index="72" stop-index="83" literal-stop-index="85">
                        <left>
                            <column name="order_id" start-index="72" stop-index="79"/>
                        </left>
                        <operator>=</operator>
                        <right>
                            <literal-expression value="100" start-index="83" stop-index="85"/>
                            <parameter-marker-expression parameter-index="0" start-index="83" stop-index="83"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </where>
        </select>
    </insert>

    <insert sql-case-id="insert_select_with_generate_key_column" parameters="100">
        <table name="t_order_item" start-index="12" stop-index="23"/>
        <columns start-index="24" stop-index="74">
            <column name="item_id" start-index="25" stop-index="31"/>
            <column name="order_id" start-index="34" stop-index="41"/>
            <column name="user_id" start-index="44" stop-index="50"/>
            <column name="status" start-index="53" stop-index="58"/>
            <column name="creation_date" start-index="61" stop-index="73"/>
        </columns>
        <select parameters="100">
            <from>
                <simple-table name="t_order_item" start-index="139" stop-index="150"/>
            </from>
            <projections start-index="83" stop-index="132">
                <column-projection name="item_id" start-index="83" stop-index="89"/>
                <column-projection name="order_id" start-index="92" stop-index="99"/>
                <column-projection name="user_id" start-index="102" stop-index="108"/>
                <expression-projection text="insert" start-index="111" stop-index="118"/>
                <expression-projection text="2017-08-08" start-index="121" stop-index="132"/>
            </projections>
            <where start-index="152" stop-index="168" literal-stop-index="170">
                <expr>
                    <binary-operation-expression start-index="158" stop-index="168" literal-stop-index="170">
                        <left>
                            <column name="item_id" start-index="158" stop-index="164"/>
                        </left>
                        <operator>=</operator>
                        <right>
                            <literal-expression value="100" start-index="168" stop-index="170"/>
                            <parameter-marker-expression parameter-index="0" start-index="168" stop-index="168"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </where>
        </select>
    </insert>

    <insert sql-case-id="insert_select_without_generate_key_column" parameters="100">
        <table name="t_order_item" start-index="12" stop-index="23"/>
        <columns start-index="24" stop-index="65">
            <column name="order_id" start-index="25" stop-index="32"/>
            <column name="user_id" start-index="35" stop-index="41"/>
            <column name="status" start-index="44" stop-index="49"/>
            <column name="creation_date" start-index="52" stop-index="64"/>
        </columns>
        <select parameters="100">
            <from>
                <simple-table name="t_order_item" start-index="121" stop-index="132"/>
            </from>
            <projections start-index="74" stop-index="114">
                <column-projection name="order_id" start-index="74" stop-index="81"/>
                <column-projection name="user_id" start-index="84" stop-index="90"/>
                <expression-projection text="insert" start-index="93" stop-index="100"/>
                <expression-projection text="2017-08-08" start-index="103" stop-index="114"/>
            </projections>
            <where start-index="134" stop-index="151" literal-stop-index="153">
                <expr>
                    <binary-operation-expression start-index="140" stop-index="151" literal-stop-index="153">
                        <left>
                            <column name="order_id" start-index="140" stop-index="147"/>
                        </left>
                        <operator>=</operator>
                        <right>
                            <literal-expression value="100" start-index="151" stop-index="153"/>
                            <parameter-marker-expression parameter-index="0" start-index="151" stop-index="151"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </where>
        </select>
    </insert>

    <insert sql-case-id="insert_select_on_duplicate_key_update" parameters="100">
        <table name="t_order" start-index="12" stop-index="18"/>
        <columns start-index="19" stop-index="45">
            <column name="order_id" start-index="20" stop-index="27"/>
            <column name="user_id" start-index="30" stop-index="36"/>
            <column name="status" start-index="39" stop-index="44"/>
        </columns>
        <select parameters="100">
            <from>
                <simple-table name="t_order" start-index="85" stop-index="91"/>
            </from>
            <projections start-index="54" stop-index="78">
                <column-projection name="order_id" start-index="54" stop-index="61"/>
                <column-projection name="user_id" start-index="64" stop-index="70"/>
                <column-projection name="status" start-index="73" stop-index="78"/>
            </projections>
            <where start-index="93" stop-index="110" literal-stop-index="112">
                <expr>
                    <binary-operation-expression start-index="99" stop-index="110" literal-stop-index="112">
                        <left>
                            <column name="order_id" start-index="99" stop-index="106"/>
                        </left>
                        <operator>=</operator>
                        <right>
                            <literal-expression value="100" start-index="110" stop-index="112"/>
                            <parameter-marker-expression parameter-index="0" start-index="110" stop-index="110"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </where>
        </select>
        <on-duplicate-key-columns start-index="112" stop-index="158" literal-start-index="114" literal-stop-index="160">
            <assignment start-index="136" stop-index="158" literal-start-index="138" literal-stop-index="160">
                <column name="status" start-index="136" stop-index="141" literal-start-index="138"
                        literal-stop-index="143"/>
                <assignment-value>
                    <function function-name="VALUES" text="VALUES(status)" start-index="145" stop-index="158"
                              literal-start-index="147" literal-stop-index="160">
                        <parameter>
                            <column name="status" start-index="152" stop-index="157" literal-start-index="154"
                                    literal-stop-index="159"/>
                        </parameter>
                    </function>
                </assignment-value>
            </assignment>
        </on-duplicate-key-columns>
    </insert>

    <insert sql-case-id="insert_with_emoji_value">
        <table name="t_emoji_test" start-index="12" stop-index="23"/>
        <columns start-index="24" stop-index="29">
            <column name="col1" start-index="25" stop-index="28"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="99" stop-index="99"/>
                    <literal-expression value="test😀" start-index="38" stop-index="45"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_digit_literals_value">
        <table name="digit_literals_value_test" start-index="12" stop-index="36"/>
        <columns start-index="37" stop-index="48">
            <column name="col1" start-index="38" stop-index="41"/>
            <column name="col2" start-index="44" stop-index="47"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <common-expression text="x'1234'" start-index="57" stop-index="63"/>
                </assignment-value>
                <assignment-value>
                    <common-expression text="x''" start-index="66" stop-index="68"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_with_clause">
        <with start-index="0" stop-index="70">
            <common-table-expression name="cte" start-index="5" stop-index="70">
                <column name="order_id" start-index="10" stop-index="17"/>
                <column name="user_id" start-index="20" stop-index="26"/>
                <subquery-expression start-index="33" stop-index="69">
                    <select>
                        <from>
                            <simple-table name="t_order" start-index="63" stop-index="69"/>
                        </from>
                        <projections start-index="40" stop-index="56">
                            <column-projection name="order_id" start-index="40" stop-index="47"/>
                            <column-projection name="user_id" start-index="50" stop-index="56"/>
                        </projections>
                    </select>
                </subquery-expression>
            </common-table-expression>
        </with>
        <table name="t_order" start-index="84" stop-index="90"/>
        <columns start-index="92" stop-index="110">
            <column name="order_id" start-index="93" stop-index="100"/>
            <column name="user_id" start-index="103" stop-index="109"/>
        </columns>
        <select>
            <from>
                <simple-table name="cte" start-index="142" stop-index="144"/>
            </from>
            <projections start-index="119" stop-index="135">
                <column-projection name="order_id" start-index="119" stop-index="126"/>
                <column-projection name="user_id" start-index="129" stop-index="135"/>
            </projections>
        </select>
    </insert>

    <insert sql-case-id="insert_without_columns_with_with_clause">
        <with start-index="0" stop-index="50">
            <common-table-expression name="cte" start-index="5" stop-index="50">
                <subquery-expression start-index="13" stop-index="49">
                    <select>
                        <from>
                            <simple-table name="t_order" start-index="43" stop-index="49"/>
                        </from>
                        <projections start-index="20" stop-index="36">
                            <column-projection name="order_id" start-index="20" stop-index="27"/>
                            <column-projection name="user_id" start-index="30" stop-index="36"/>
                        </projections>
                    </select>
                </subquery-expression>
            </common-table-expression>
        </with>
        <table name="t_order" start-index="64" stop-index="70"/>
        <columns start-index="72" stop-index="90">
            <column name="order_id" start-index="73" stop-index="80"/>
            <column name="user_id" start-index="83" stop-index="89"/>
        </columns>
        <select>
            <from>
                <simple-table name="cte" start-index="122" stop-index="124"/>
            </from>
            <projections start-index="99" stop-index="115">
                <column-projection name="order_id" start-index="99" stop-index="106"/>
                <column-projection name="user_id" start-index="109" stop-index="115"/>
            </projections>
        </select>
    </insert>

    <insert sql-case-id="insert_without_into_keyword" parameters="1, 1, 'init'">
        <table name="t_order" start-index="7" stop-index="13"/>
        <columns start-index="15" stop-index="41">
            <column name="order_id" start-index="16" stop-index="23"/>
            <column name="user_id" start-index="26" stop-index="32"/>
            <column name="status" start-index="35" stop-index="40"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="51" stop-index="51"/>
                    <literal-expression value="1" start-index="51" stop-index="51"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="54" stop-index="54"/>
                    <literal-expression value="1" start-index="54" stop-index="54"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2" start-index="57" stop-index="57"/>
                    <literal-expression value="init" start-index="57" stop-index="62"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_default_values">
        <table name="t_order" start-index="12" stop-index="18"/>
        <columns start-index="20" stop-index="46">
            <column name="order_id" start-index="21" stop-index="28"/>
            <column name="user_id" start-index="31" stop-index="37"/>
            <column name="status" start-index="40" stop-index="45"/>
        </columns>
    </insert>

    <insert sql-case-id="insert_without_columns_with_default_values">
        <table name="t_order" start-index="12" stop-index="18"/>
        <columns start-index="19" stop-index="19"/>
    </insert>

    <insert sql-case-id="insert_with_top">
        <table name="t_order" start-index="15" stop-index="21"/>
        <columns start-index="23" stop-index="41">
            <column name="order_id" start-index="24" stop-index="31"/>
            <column name="user_id" start-index="34" stop-index="40"/>
        </columns>
        <select>
            <from>
                <simple-table name="t_order" start-index="73" stop-index="79"/>
            </from>
            <projections start-index="50" stop-index="66">
                <column-projection name="order_id" start-index="50" stop-index="57"/>
                <column-projection name="user_id" start-index="60" stop-index="66"/>
            </projections>
        </select>
    </insert>

    <insert sql-case-id="insert_with_top_percent">
        <table name="t_order" start-index="23" stop-index="29"/>
        <columns start-index="31" stop-index="49">
            <column name="order_id" start-index="32" stop-index="39"/>
            <column name="user_id" start-index="42" stop-index="48"/>
        </columns>
        <select>
            <from>
                <simple-table name="t_order" start-index="81" stop-index="87"/>
            </from>
            <projections start-index="58" stop-index="74">
                <column-projection name="order_id" start-index="58" stop-index="65"/>
                <column-projection name="user_id" start-index="68" stop-index="74"/>
            </projections>
        </select>
    </insert>

    <insert sql-case-id="insert_with_output_clause" parameters="1, 1">
        <table name="t_order" start-index="12" stop-index="18"/>
        <columns start-index="20" stop-index="38">
            <column name="order_id" start-index="21" stop-index="28"/>
            <column name="user_id" start-index="31" stop-index="37"/>
        </columns>
        <output start-index="40" stop-index="128">
            <output-columns start-index="47" stop-index="81">
                <column-projection name="order_id" start-index="47" stop-index="63"/>
                <column-projection name="user_id" start-index="66" stop-index="81"/>
            </output-columns>
            <output-table name="@MyTableVar" start-index="88" stop-index="98"/>
            <output-table-columns start-index="100" stop-index="128">
                <column name="temp_order_id" start-index="101" stop-index="113"/>
                <column name="temp_user_id" start-index="116" stop-index="127"/>
            </output-table-columns>
        </output>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="138" stop-index="138"/>
                    <literal-expression value="1" start-index="138" stop-index="138"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="141" stop-index="141"/>
                    <literal-expression value="1" start-index="141" stop-index="141"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_output_clause_without_output_table_columns" parameters="1, 1">
        <table name="t_order" start-index="12" stop-index="18"/>
        <columns start-index="20" stop-index="38">
            <column name="order_id" start-index="21" stop-index="28"/>
            <column name="user_id" start-index="31" stop-index="37"/>
        </columns>
        <output start-index="40" stop-index="98">
            <output-columns start-index="47" stop-index="81">
                <column-projection name="order_id" start-index="47" stop-index="63"/>
                <column-projection name="user_id" start-index="66" stop-index="81"/>
            </output-columns>
            <output-table name="@MyTableVar" start-index="88" stop-index="98"/>
        </output>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="108" stop-index="108"/>
                    <literal-expression value="1" start-index="108" stop-index="108"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="111" stop-index="111"/>
                    <literal-expression value="1" start-index="111" stop-index="111"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_output_clause_without_output_table" parameters="1, 1">
        <table name="t_order" start-index="12" stop-index="18"/>
        <columns start-index="20" stop-index="38">
            <column name="order_id" start-index="21" stop-index="28"/>
            <column name="user_id" start-index="31" stop-index="37"/>
        </columns>
        <output start-index="40" stop-index="81">
            <output-columns start-index="47" stop-index="81">
                <column-projection name="order_id" start-index="47" stop-index="63"/>
                <column-projection name="user_id" start-index="66" stop-index="81"/>
            </output-columns>
        </output>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="91" stop-index="91"/>
                    <literal-expression value="1" start-index="91" stop-index="91"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="94" stop-index="94"/>
                    <literal-expression value="1" start-index="94" stop-index="94"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_output_clause_column_shorthand" parameters="1, 1">
        <table name="t_order" start-index="12" stop-index="18"/>
        <columns start-index="20" stop-index="38">
            <column name="order_id" start-index="21" stop-index="28"/>
            <column name="user_id" start-index="31" stop-index="37"/>
        </columns>
        <output start-index="40" stop-index="56"/>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="66" stop-index="66"/>
                    <literal-expression value="1" start-index="66" stop-index="66"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="69" stop-index="69"/>
                    <literal-expression value="1" start-index="69" stop-index="69"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_without_columns">
        <table name="departments" start-index="12" stop-index="22"/>
        <columns start-index="23" stop-index="23"/>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="280" start-index="32" stop-index="34"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="Recreation" start-index="37" stop-index="48"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="121" start-index="51" stop-index="53"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="1700" start-index="56" stop-index="59"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_dml_table_expr_select">
        <select>
            <from>
                <simple-table name="employees" start-index="98" stop-index="106"/>
            </from>
            <projections start-index="20" stop-index="91">
                <column-projection name="employee_id" start-index="20" stop-index="30"/>
                <column-projection name="last_name" start-index="33" stop-index="41"/>
                <column-projection name="email" start-index="44" stop-index="48"/>
                <column-projection name="hire_date" start-index="51" stop-index="59"/>
                <column-projection name="job_id" start-index="62" stop-index="67"/>
                <column-projection name="salary" start-index="70" stop-index="75"/>
                <column-projection name="commission_pct" start-index="78" stop-index="91"/>
            </projections>
        </select>
        <columns start-index="108" stop-index="108"/>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="207" start-index="117" stop-index="119"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="Gregory" start-index="122" stop-index="130"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="<EMAIL>" start-index="133" stop-index="154"/>
                </assignment-value>
                <assignment-value>
                    <column name="sysdate" start-index="157" stop-index="163"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="PU_CLERK" start-index="166" stop-index="175"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="1.2E+3" start-index="178" stop-index="182"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="null" start-index="185" stop-index="188"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_select_subquery">
        <table name="bonuses" start-index="12" stop-index="18"/>
        <columns start-index="19" stop-index="19"/>
        <select>
            <from>
                <simple-table name="employees" start-index="56" stop-index="64"/>
            </from>
            <projections start-index="27" stop-index="49">
                <column-projection name="employee_id" start-index="27" stop-index="37"/>
                <expression-projection text="salary*1.1" start-index="40" stop-index="49"/>
            </projections>
            <where start-index="66" stop-index="92">
                <expr>
                    <binary-operation-expression start-index="72" stop-index="92">
                        <left>
                            <column name="commission_pct" start-index="72" stop-index="85"/>
                        </left>
                        <operator>&gt;</operator>
                        <right>
                            <literal-expression value="0.25" start-index="89" stop-index="92"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </where>
        </select>
    </insert>

    <insert sql-case-id="insert_with_multitable_element">
        <multi-table-insert-type value="ALL"/>
        <multi-table-insert-into start-index="11" stop-index="231">
            <insert-statement>
                <table name="sales" start-index="16" stop-index="20"/>
                <columns start-index="22" stop-index="56">
                    <column name="prod_id" start-index="23" stop-index="29"/>
                    <column name="cust_id" start-index="32" stop-index="38"/>
                    <column name="time_id" start-index="41" stop-index="47"/>
                    <column name="amount" start-index="50" stop-index="55"/>
                </columns>
                <values>
                    <value>
                        <assignment-value>
                            <column name="product_id" start-index="66" stop-index="75"/>
                        </assignment-value>
                        <assignment-value>
                            <column name="customer_id" start-index="78" stop-index="88"/>
                        </assignment-value>
                        <assignment-value>
                            <column name="weekly_start_date" start-index="91" stop-index="107"/>
                        </assignment-value>
                        <assignment-value>
                            <column name="sales_sun" start-index="110" stop-index="118"/>
                        </assignment-value>
                    </value>
                </values>
            </insert-statement>
            <insert-statement>
                <table name="sales" start-index="126" stop-index="130"/>
                <columns start-index="132" stop-index="166">
                    <column name="prod_id" start-index="133" stop-index="139"/>
                    <column name="cust_id" start-index="142" stop-index="148"/>
                    <column name="time_id" start-index="151" stop-index="157"/>
                    <column name="amount" start-index="160" stop-index="165"/>
                </columns>
                <values>
                    <value>
                        <assignment-value>
                            <column name="product_id" start-index="176" stop-index="185"/>
                        </assignment-value>
                        <assignment-value>
                            <column name="customer_id" start-index="188" stop-index="198"/>
                        </assignment-value>
                        <assignment-value>
                            <column name="sales_mon" start-index="201" stop-index="209"/>
                        </assignment-value>
                        <assignment-value>
                            <binary-operation-expression start-index="212" stop-index="230" literal-start-index="212"
                                                         literal-stop-index="230">
                                <left>
                                    <column name="weekly_start_date" start-index="212" stop-index="228"
                                            literal-start-index="212" literal-stop-index="228"/>
                                </left>
                                <operator>+</operator>
                                <right>
                                    <literal-expression value="1" start-index="230" stop-index="230"
                                                        literal-start-index="230" literal-stop-index="230"/>
                                </right>
                            </binary-operation-expression>
                        </assignment-value>
                    </value>
                </values>
            </insert-statement>
        </multi-table-insert-into>
        <select>
            <from>
                <simple-table name="sales_input_table" start-index="310" stop-index="326"/>
            </from>
            <projections start-index="240" stop-index="303">
                <column-projection name="product_id" start-index="240" stop-index="249"/>
                <column-projection name="customer_id" start-index="252" stop-index="262"/>
                <column-projection name="weekly_start_date" start-index="265" stop-index="281"/>
                <column-projection name="sales_sun" start-index="284" stop-index="292"/>
                <column-projection name="sales_mon" start-index="295" stop-index="303"/>
            </projections>
        </select>
    </insert>

    <insert sql-case-id="insert_all_with_multi_table_with_conditional_when">
        <multi-table-insert-type value="ALL"/>
        <multi-table-conditional-into start-index="7" stop-index="185">
            <conditional-into-when-then start-index="11" stop-index="59">
                <when start-index="16" stop-index="36">
                    <binary-operation-expression start-index="16" stop-index="36">
                        <left>
                            <column name="order_total" start-index="16" stop-index="26"/>
                        </left>
                        <operator>&lt;=</operator>
                        <right>
                            <literal-expression value="100000" start-index="31" stop-index="36"/>
                        </right>
                    </binary-operation-expression>
                </when>
                <then start-index="43" stop-index="59">
                    <insert-statement>
                        <table name="small_orders" start-index="48" stop-index="59"/>
                        <columns start-index="60" stop-index="60"/>
                    </insert-statement>
                </then>
            </conditional-into-when-then>
            <conditional-into-when-then start-index="61" stop-index="136">
                <when start-index="66" stop-index="112">
                    <binary-operation-expression start-index="66" stop-index="112">
                        <left>
                            <binary-operation-expression start-index="66" stop-index="86">
                                <left>
                                    <column name="order_total" start-index="66" stop-index="76"/>
                                </left>
                                <operator>&gt;</operator>
                                <right>
                                    <literal-expression value="1000000" start-index="80" stop-index="86"/>
                                </right>
                            </binary-operation-expression>
                        </left>
                        <operator>AND</operator>
                        <right>
                            <binary-operation-expression start-index="92" stop-index="112">
                                <left>
                                    <column name="order_total" start-index="92" stop-index="102"/>
                                </left>
                                <operator>&lt;=</operator>
                                <right>
                                    <literal-expression value="200000" start-index="107" stop-index="112"/>
                                </right>
                            </binary-operation-expression>
                        </right>
                    </binary-operation-expression>
                </when>
                <then start-index="119" stop-index="136">
                    <insert-statement>
                        <table name="medium_orders" start-index="124" stop-index="136"/>
                        <columns start-index="137" stop-index="137"/>
                    </insert-statement>
                </then>
            </conditional-into-when-then>
            <conditional-into-when-then start-index="138" stop-index="185">
                <when start-index="143" stop-index="162">
                    <binary-operation-expression start-index="143" stop-index="162">
                        <left>
                            <column name="order_total" start-index="143" stop-index="153"/>
                        </left>
                        <operator>&gt;</operator>
                        <right>
                            <literal-expression value="200000" start-index="157" stop-index="162"/>
                        </right>
                    </binary-operation-expression>
                </when>
                <then start-index="169" stop-index="185">
                    <insert-statement>
                        <table name="large_orders" start-index="174" stop-index="185"/>
                        <columns start-index="186" stop-index="186"/>
                    </insert-statement>
                </then>
            </conditional-into-when-then>
        </multi-table-conditional-into>
        <select>
            <from>
                <simple-table name="orders" start-index="248" stop-index="253"/>
            </from>
            <projections start-index="194" stop-index="241">
                <column-projection name="order_id" start-index="194" stop-index="201"/>
                <column-projection name="order_total" start-index="204" stop-index="214"/>
                <column-projection name="sales_rep_id" start-index="217" stop-index="228"/>
                <column-projection name="customer_id" start-index="231" stop-index="241"/>
            </projections>
        </select>
    </insert>

    <insert sql-case-id="insert_all_with_multi_table_with_conditional_when_with_conditional_else">
        <multi-table-insert-type value="ALL"/>
        <multi-table-conditional-into start-index="7" stop-index="158">
            <conditional-into-when-then start-index="11" stop-index="59">
                <when start-index="16" stop-index="36">
                    <binary-operation-expression start-index="16" stop-index="36">
                        <left>
                            <column name="order_total" start-index="16" stop-index="26"/>
                        </left>
                        <operator>&lt;=</operator>
                        <right>
                            <literal-expression value="100000" start-index="31" stop-index="36"/>
                        </right>
                    </binary-operation-expression>
                </when>
                <then start-index="43" stop-index="59">
                    <insert-statement>
                        <table name="small_orders" start-index="48" stop-index="59"/>
                        <columns start-index="60" stop-index="60"/>
                    </insert-statement>
                </then>
            </conditional-into-when-then>
            <conditional-into-when-then start-index="61" stop-index="135">
                <when start-index="66" stop-index="111">
                    <binary-operation-expression start-index="66" stop-index="111">
                        <left>
                            <binary-operation-expression start-index="66" stop-index="85">
                                <left>
                                    <column name="order_total" start-index="66" stop-index="76"/>
                                </left>
                                <operator>&gt;</operator>
                                <right>
                                    <literal-expression value="100000" start-index="80" stop-index="85"/>
                                </right>
                            </binary-operation-expression>
                        </left>
                        <operator>AND</operator>
                        <right>
                            <binary-operation-expression start-index="91" stop-index="111">
                                <left>
                                    <column name="order_total" start-index="91" stop-index="101"/>
                                </left>
                                <operator>&lt;=</operator>
                                <right>
                                    <literal-expression value="200000" start-index="106" stop-index="111"/>
                                </right>
                            </binary-operation-expression>
                        </right>
                    </binary-operation-expression>
                </when>
                <then start-index="118" stop-index="135">
                    <insert-statement>
                        <table name="medium_orders" start-index="123" stop-index="135"/>
                        <columns start-index="136" stop-index="136"/>
                    </insert-statement>
                </then>
            </conditional-into-when-then>
            <conditional-into-else start-index="137" stop-index="158">
                <insert-statement>
                    <table name="large_orders" start-index="147" stop-index="158"/>
                    <columns start-index="159" stop-index="159"/>
                </insert-statement>
            </conditional-into-else>
        </multi-table-conditional-into>
        <select>
            <from>
                <simple-table name="orders" start-index="221" stop-index="226"/>
            </from>
            <projections start-index="167" stop-index="214">
                <column-projection name="order_id" start-index="167" stop-index="174"/>
                <column-projection name="order_total" start-index="177" stop-index="187"/>
                <column-projection name="sales_rep_id" start-index="190" stop-index="201"/>
                <column-projection name="customer_id" start-index="204" stop-index="214"/>
            </projections>
        </select>
    </insert>

    <insert sql-case-id="insert_with_rank_column">
        <table name="sales" start-index="12" stop-index="16"/>
        <columns start-index="18" stop-index="23">
            <column name="rank" start-index="19" stop-index="22"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="1" start-index="33" stop-index="33"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_schema">
        <table name="t_order" start-index="12" stop-index="22">
            <owner name="db1" start-index="12" stop-index="14"/>
        </table>
        <columns start-index="23" stop-index="23"/>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="1" start-index="32" stop-index="32"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="2" start-index="34" stop-index="34"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="3" start-index="36" stop-index="36"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_on_duplicate_key_update_nothing">
        <table name="t_order" start-index="12" stop-index="18"/>
        <columns start-index="20" stop-index="46">
            <column name="order_id" start-index="21" stop-index="28"/>
            <column name="user_id" start-index="31" stop-index="37"/>
            <column name="status" start-index="40" stop-index="45"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="1" start-index="56" stop-index="56"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="1" start-index="59" stop-index="59"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="insert" start-index="62" stop-index="69"/>
                </assignment-value>
            </value>
        </values>
        <on-duplicate-key-columns start-index="72" stop-index="102"/>
    </insert>

    <insert sql-case-id="insert_on_duplicate_key_update_multi_column">
        <table name="t_order" start-index="12" stop-index="18"/>
        <columns start-index="20" stop-index="46">
            <column name="order_id" start-index="21" stop-index="28"/>
            <column name="user_id" start-index="31" stop-index="37"/>
            <column name="status" start-index="40" stop-index="45"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0"/>
                    <literal-expression value="1" start-index="56" stop-index="56"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1"/>
                    <literal-expression value="1" start-index="59" stop-index="59"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2"/>
                    <literal-expression value="insert" start-index="62" stop-index="69"/>
                </assignment-value>
            </value>
        </values>
        <on-duplicate-key-columns start-index="72" stop-index="133">
            <assignment start-index="96" stop-index="116">
                <column name="user_id" start-index="96" stop-index="102"/>
                <assignment-value>
                    <binary-operation-expression start-index="106" stop-index="116" literal-start-index="106"
                                                 literal-stop-index="116">
                        <left>
                            <column name="user_id" start-index="106" stop-index="112" literal-start-index="106"
                                    literal-stop-index="112"/>
                        </left>
                        <operator>+</operator>
                        <right>
                            <literal-expression value="1" start-index="116" stop-index="116" literal-start-index="116"
                                                literal-stop-index="116"/>
                        </right>
                    </binary-operation-expression>
                </assignment-value>
            </assignment>
            <assignment start-index="119" stop-index="133">
                <column name="status" start-index="119" stop-index="124"/>
                <assignment-value>
                    <literal-expression value="update" start-index="126" stop-index="133"/>
                </assignment-value>
            </assignment>
        </on-duplicate-key-columns>
    </insert>

    <insert sql-case-id="insert_with_negative_value" parameters="1, -1, 'init'">
        <table name="t_order" start-index="12" stop-index="18"/>
        <columns start-index="20" stop-index="46">
            <column name="order_id" start-index="21" stop-index="28"/>
            <column name="user_id" start-index="31" stop-index="37"/>
            <column name="status" start-index="40" stop-index="45"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="56" stop-index="56"/>
                    <literal-expression value="1" start-index="56" stop-index="56"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="59" stop-index="59"/>
                    <literal-expression value="-1" start-index="59" stop-index="60"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2" start-index="62" stop-index="62"/>
                    <literal-expression value="init" start-index="63" stop-index="68"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_datetime_literals">
        <table name="date_tab" start-index="12" stop-index="19"/>
        <columns start-index="20" stop-index="20"/>
        <values>
            <value>
                <assignment-value>
                    <common-expression text="TIMESTAMP'1999-12-01 10:00:00'" start-index="30" stop-index="59"/>
                </assignment-value>
                <assignment-value>
                    <common-expression text="TIMESTAMP'1999-12-01 10:00:00'" start-index="62" stop-index="91"/>
                </assignment-value>
                <assignment-value>
                    <common-expression text="TIMESTAMP'1999-12-01 10:00:00'" start-index="94" stop-index="123"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_content_keyword">
        <table name="SYS_MQ_MSG" start-index="12" stop-index="21"/>
        <columns start-index="23" stop-index="35">
            <column name="ID" start-index="24" stop-index="25"/>
            <column name="CONTENT" start-index="28" stop-index="34"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="1" start-index="45" stop-index="45"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="test" start-index="48" stop-index="53"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_connect_by_and_prior">
        <table name="t" start-index="12" stop-index="12" literal-start-index="12" literal-stop-index="12"/>
        <columns start-index="14" stop-index="29" literal-start-index="14" literal-stop-index="29">
            <column name="c1" start-index="15" stop-index="16" literal-start-index="15" literal-stop-index="16"/>
            <column name="c2" start-index="18" stop-index="19" literal-start-index="18" literal-stop-index="19"/>
            <column name="c3" start-index="21" stop-index="22" literal-start-index="21" literal-stop-index="22"/>
            <column name="c4" start-index="24" stop-index="25" literal-start-index="24" literal-stop-index="25"/>
            <column name="c5" start-index="27" stop-index="28" literal-start-index="27" literal-stop-index="28"/>
        </columns>
        <select>
            <projections start-index="38" stop-index="84" literal-start-index="38" literal-stop-index="84">
                <column-projection name="c1" start-index="38" stop-index="39" literal-start-index="38"
                                   literal-stop-index="39"/>
                <column-projection name="c2" start-index="41" stop-index="42" literal-start-index="41"
                                   literal-stop-index="42"/>
                <column-projection name="c4" start-index="80" stop-index="81" literal-start-index="80"
                                   literal-stop-index="81"/>
                <column-projection name="c5" start-index="83" stop-index="84" literal-start-index="83"
                                   literal-stop-index="84"/>
                <expression-projection text="regexp_substr(c3, '[^,]+', 1, l)" alias="c3" start-index="44"
                                       stop-index="78" literal-start-index="44" literal-stop-index="78">
                    <literalText>regexp_substr(c3, '[^,]+', 1, l)</literalText>
                    <expr>
                        <function function-name="regexp_substr" text="regexp_substr(c3, '[^,]+', 1, l)" start-index="44"
                                  stop-index="75" literal-start-index="44" literal-stop-index="75">
                            <parameter>
                                <column name="c3" start-index="58" stop-index="59" literal-start-index="58"
                                        literal-stop-index="59"/>
                            </parameter>
                            <parameter>
                                <literal-expression value="[^,]+" start-index="62" stop-index="68"
                                                    literal-start-index="62" literal-stop-index="68"/>
                            </parameter>
                            <parameter>
                                <literal-expression value="1" start-index="71" stop-index="71" literal-start-index="71"
                                                    literal-stop-index="71"/>
                            </parameter>
                            <parameter>
                                <column name="l" start-index="74" stop-index="74" literal-start-index="74"
                                        literal-stop-index="74"/>
                            </parameter>
                            <literalText>regexp_substr(c3, '[^,]+', 1, l)</literalText>
                        </function>
                    </expr>
                </expression-projection>
            </projections>
            <from>
                <simple-table name="t" start-index="91" stop-index="91" literal-start-index="91"
                              literal-stop-index="91"/>
            </from>
            <where start-index="93" stop-index="102" literal-start-index="93" literal-stop-index="102">
                <expr>
                    <binary-operation-expression start-index="99" stop-index="102" literal-start-index="99"
                                                 literal-stop-index="102">
                        <left>
                            <column name="id" start-index="99" stop-index="100" literal-start-index="99"
                                    literal-stop-index="100"/>
                        </left>
                        <operator>=</operator>
                        <right>
                            <literal-expression value="1" start-index="102" stop-index="102" literal-start-index="102"
                                                literal-stop-index="102"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </where>
        </select>
    </insert>

    <insert sql-case-id="insert_with_interval">
        <table name="test" start-index="12" stop-index="15" literal-start-index="12" literal-stop-index="15"/>
        <columns start-index="16" stop-index="16" literal-start-index="16" literal-stop-index="16"/>
        <values>
            <value>
                <assignment-value>
                    <common-expression literal-text="INTERVAL '123-2' YEAR(3) TO MONTH"
                                       text="INTERVAL '123-2' YEAR(3) TO MONTH" start-index="25" stop-index="57"
                                       literal-start-index="25" literal-stop-index="57"/>
                </assignment-value>
                <assignment-value>
                    <common-expression literal-text="INTERVAL '123' YEAR(3)" text="INTERVAL '123' YEAR(3)"
                                       start-index="60" stop-index="81" literal-start-index="60"
                                       literal-stop-index="81"/>
                </assignment-value>
                <assignment-value>
                    <common-expression literal-text="INTERVAL '300' MONTH(3)" text="INTERVAL '300' MONTH(3)"
                                       start-index="84" stop-index="106" literal-start-index="84"
                                       literal-stop-index="106"/>
                </assignment-value>
                <assignment-value>
                    <common-expression literal-text="INTERVAL '4' YEAR" text="INTERVAL '4' YEAR" start-index="109"
                                       stop-index="125" literal-start-index="109" literal-stop-index="125"/>
                </assignment-value>
                <assignment-value>
                    <common-expression literal-text="INTERVAL '50' MONTH" text="INTERVAL '50' MONTH" start-index="128"
                                       stop-index="146" literal-start-index="128" literal-stop-index="146"/>
                </assignment-value>
                <assignment-value>
                    <common-expression literal-text="INTERVAL '4 5:12:10.222' DAY TO SECOND(3)"
                                       text="INTERVAL '4 5:12:10.222' DAY TO SECOND(3)" start-index="149"
                                       stop-index="189" literal-start-index="149" literal-stop-index="189"/>
                </assignment-value>
                <assignment-value>
                    <common-expression literal-text="INTERVAL '4 5:12' DAY TO MINUTE"
                                       text="INTERVAL '4 5:12' DAY TO MINUTE" start-index="192" stop-index="222"
                                       literal-start-index="192" literal-stop-index="222"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_national_character_set">
        <table name="customers" start-index="12" stop-index="20" literal-start-index="12" literal-stop-index="20"/>
        <columns start-index="21" stop-index="21" literal-start-index="21" literal-stop-index="21"/>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="1000" start-index="30" stop-index="33" literal-start-index="30"
                                        literal-stop-index="33"/>
                </assignment-value>
                <assignment-value>
                    <function function-name="TO_NCHAR" text="TO_NCHAR('John Smith')" start-index="36" stop-index="57"
                              literal-start-index="36" literal-stop-index="57">
                        <parameter>
                            <literal-expression value="John Smith" start-index="45" stop-index="56"
                                                literal-start-index="45" literal-stop-index="56"/>
                        </parameter>
                        <literalText>TO_NCHAR('John Smith')</literalText>
                    </function>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="500 Oracle Parkway" start-index="60" stop-index="79"
                                        literal-start-index="59" literal-stop-index="79"/>
                </assignment-value>
                <assignment-value>
                    <column name="sysdate" start-index="81" stop-index="87" literal-start-index="81"
                            literal-stop-index="87"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_oracle_datetime_type">
        <table name="t_order" start-index="12" stop-index="18"/>
        <columns start-index="20" stop-index="93">
            <column name="create_date" start-index="21" stop-index="31"/>
            <column name="create_timestamp" start-index="34" stop-index="49"/>
            <column name="create_interval_year" start-index="52" stop-index="71"/>
            <column name="create_interval_day" start-index="74" stop-index="92"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <function function-name="TO_DATE" text="TO_DATE('2009', 'YYYY')" start-index="103" stop-index="125"
                              literal-start-index="103" literal-stop-index="125">
                        <parameter>
                            <literal-expression value="'2009'" start-index="111" stop-index="116"
                                                literal-start-index="111" literal-stop-index="116"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="'YYYY'" start-index="119" stop-index="124"
                                                literal-start-index="119" literal-stop-index="124"/>
                        </parameter>
                        <literalText>TO_DATE('2009', 'YYYY')</literalText>
                    </function>
                </assignment-value>
                <assignment-value>
                    <function function-name="TO_DATE" text="TO_DATE('2009', 'YYYY')" start-index="128" stop-index="150"
                              literal-start-index="128" literal-stop-index="150">
                        <parameter>
                            <literal-expression value="'2009'" start-index="136" stop-index="141"
                                                literal-start-index="136" literal-stop-index="141"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="'YYYY'" start-index="144" stop-index="149"
                                                literal-start-index="144" literal-stop-index="149"/>
                        </parameter>
                        <literalText>TO_DATE('2009', 'YYYY')</literalText>
                    </function>
                </assignment-value>
                <assignment-value>
                    <interval-expression>
                        <left>
                            <function function-name="TO_DATE" text="TO_DATE('2009', 'YYYY')" start-index="154"
                                      stop-index="176" literal-start-index="154" literal-stop-index="176">
                                <parameter>
                                    <literal-expression value="'2009'" start-index="162" stop-index="167"
                                                        literal-start-index="162" literal-stop-index="167"/>
                                </parameter>
                                <parameter>
                                    <literal-expression value="'YYYY'" start-index="170" stop-index="175"
                                                        literal-start-index="170" literal-stop-index="175"/>
                                </parameter>
                                <literalText>TO_DATE('2009', 'YYYY')</literalText>
                            </function>
                        </left>
                        <right>
                            <function function-name="TO_DATE" text="TO_DATE('2009', 'YYYY')" start-index="180"
                                      stop-index="202" literal-start-index="180" literal-stop-index="202">
                                <parameter>
                                    <literal-expression value="'2009'" start-index="188" stop-index="193"
                                                        literal-start-index="188" literal-stop-index="193"/>
                                </parameter>
                                <parameter>
                                    <literal-expression value="'YYYY'" start-index="196" stop-index="201"
                                                        literal-start-index="196" literal-stop-index="201"/>
                                </parameter>
                                <literalText>TO_DATE('2009', 'YYYY')</literalText>
                            </function>
                        </right>
                    </interval-expression>
                </assignment-value>
                <assignment-value>
                    <interval-expression>
                        <left>
                            <function function-name="TO_DATE" text="TO_DATE('2009', 'YYYY')" start-index="221"
                                      stop-index="243" literal-start-index="221" literal-stop-index="243">
                                <parameter>
                                    <literal-expression value="'2009'" start-index="229" stop-index="234"
                                                        literal-start-index="229" literal-stop-index="234"/>
                                </parameter>
                                <parameter>
                                    <literal-expression value="'YYYY'" start-index="237" stop-index="242"
                                                        literal-start-index="237" literal-stop-index="242"/>
                                </parameter>
                                <literalText>TO_DATE('2009', 'YYYY')</literalText>
                            </function>
                        </left>
                        <right>
                            <function function-name="TO_DATE" text="TO_DATE('2009', 'YYYY')" start-index="247"
                                      stop-index="269" literal-start-index="247" literal-stop-index="269">
                                <parameter>
                                    <literal-expression value="'2009'" start-index="255" stop-index="260"
                                                        literal-start-index="255" literal-stop-index="260"/>
                                </parameter>
                                <parameter>
                                    <literal-expression value="'YYYY'" start-index="263" stop-index="268"
                                                        literal-start-index="263" literal-stop-index="268"/>
                                </parameter>
                                <literalText>TO_DATE('2009', 'YYYY')</literalText>
                            </function>
                        </right>
                    </interval-expression>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_all_into">
        <multi-table-insert-type value="ALL"/>
        <multi-table-insert-into start-index="11" stop-index="213">
            <insert-statement>
                <table name="T_MASK" start-index="16" stop-index="21"/>
                <columns start-index="22" stop-index="50">
                    <column name="ID" start-index="23" stop-index="24"/>
                    <column name="EMAIL" start-index="26" stop-index="30"/>
                    <column name="NAME" start-index="32" stop-index="35"/>
                    <column name="PHONE" start-index="37" stop-index="41"/>
                    <column name="ADDRESS" start-index="43" stop-index="49"/>
                </columns>
                <values>
                    <value>
                        <assignment-value>
                            <literal-expression value="1" start-index="60" stop-index="60"/>
                        </assignment-value>
                        <assignment-value>
                            <literal-expression value="2" start-index="62" stop-index="64"/>
                        </assignment-value>
                        <assignment-value>
                            <literal-expression value="3" start-index="66" stop-index="68"/>
                        </assignment-value>
                        <assignment-value>
                            <literal-expression value="4" start-index="70" stop-index="72"/>
                        </assignment-value>
                        <assignment-value>
                            <literal-expression value="5" start-index="74" stop-index="76"/>
                        </assignment-value>
                    </value>
                </values>
            </insert-statement>
            <insert-statement>
                <table name="T_MASK" start-index="84" stop-index="89"/>
                <columns start-index="90" stop-index="118">
                    <column name="ID" start-index="91" stop-index="92"/>
                    <column name="EMAIL" start-index="94" stop-index="98"/>
                    <column name="NAME" start-index="100" stop-index="103"/>
                    <column name="PHONE" start-index="105" stop-index="109"/>
                    <column name="ADDRESS" start-index="111" stop-index="117"/>
                </columns>
                <values>
                    <value>
                        <assignment-value>
                            <literal-expression value="2" start-index="128" stop-index="128"/>
                        </assignment-value>
                        <assignment-value>
                            <literal-expression value="2" start-index="130" stop-index="132"/>
                        </assignment-value>
                        <assignment-value>
                            <literal-expression value="3" start-index="134" stop-index="136"/>
                        </assignment-value>
                        <assignment-value>
                            <literal-expression value="4" start-index="138" stop-index="140"/>
                        </assignment-value>
                        <assignment-value>
                            <literal-expression value="5" start-index="142" stop-index="144"/>
                        </assignment-value>
                    </value>
                </values>
            </insert-statement>
            <insert-statement>
                <table name="T_MASK" start-index="152" stop-index="157"/>
                <columns start-index="158" stop-index="186">
                    <column name="ID" start-index="159" stop-index="160"/>
                    <column name="EMAIL" start-index="162" stop-index="166"/>
                    <column name="NAME" start-index="168" stop-index="171"/>
                    <column name="PHONE" start-index="173" stop-index="177"/>
                    <column name="ADDRESS" start-index="179" stop-index="185"/>
                </columns>
                <values>
                    <value>
                        <assignment-value>
                            <literal-expression value="3" start-index="196" stop-index="196"/>
                        </assignment-value>
                        <assignment-value>
                            <literal-expression value="2" start-index="198" stop-index="200"/>
                        </assignment-value>
                        <assignment-value>
                            <literal-expression value="3" start-index="202" stop-index="204"/>
                        </assignment-value>
                        <assignment-value>
                            <literal-expression value="4" start-index="206" stop-index="208"/>
                        </assignment-value>
                        <assignment-value>
                            <literal-expression value="5" start-index="210" stop-index="212"/>
                        </assignment-value>
                    </value>
                </values>
            </insert-statement>
        </multi-table-insert-into>
        <select>
            <projections start-index="222" stop-index="222">
                <expression-projection start-index="222" stop-index="222" text="1">
                    <expr>
                        <literal-expression start-index="222" stop-index="222" value="1"/>
                    </expr>
                </expression-projection>
            </projections>
            <from>
                <simple-table name="DUAL" start-index="229" stop-index="232"/>
            </from>
        </select>
    </insert>

    <insert sql-case-id="insert_with_nchar_1">
        <table name="T1" start-index="12" stop-index="17">
            <owner name="dbo" start-index="12" stop-index="14"/>
        </table>
        <columns start-index="18" stop-index="18"/>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="1" start-index="27" stop-index="27"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="Natalia" start-index="30" stop-index="39"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_nchar_2">
        <table name="T1" start-index="12" stop-index="17">
            <owner name="dbo" start-index="12" stop-index="14"/>
        </table>
        <columns start-index="18" stop-index="18"/>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="2" start-index="27" stop-index="27"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="Mark" start-index="30" stop-index="36"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_nchar_3">
        <table name="T1" start-index="12" stop-index="17">
            <owner name="dbo" start-index="12" stop-index="14"/>
        </table>
        <columns start-index="18" stop-index="18"/>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="3" start-index="27" stop-index="27"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="Randolph" start-index="30" stop-index="40"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_nchar_4">
        <table name="@table1" start-index="7" stop-index="13"/>
        <columns start-index="15" stop-index="32">
            <column name="c2" start-index="16" stop-index="17"/>
            <column name="is_transient" start-index="20" stop-index="31"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="sample durable" start-index="42" stop-index="58"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="0" start-index="61" stop-index="61"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_nchar_5">
        <table name="@table1" start-index="7" stop-index="13"/>
        <columns start-index="15" stop-index="32">
            <column name="c2" start-index="16" stop-index="17"/>
            <column name="is_transient" start-index="20" stop-index="31"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="sample non-durable" start-index="42" stop-index="62"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="1" start-index="65" stop-index="65"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_data_base_name">
        <table name="VariableTest" start-index="12" stop-index="46">
            <owner name="dbo" start-index="31" stop-index="33">
                <owner name="AdventureWorks2022" start-index="12" stop-index="29"/>
            </owner>
        </table>
        <columns start-index="47" stop-index="52">
            <column name="Col1" start-index="48" stop-index="51"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="$(tablename)" start-index="61" stop-index="74"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_batch_nchar">
        <table name="Employees" start-index="12" stop-index="31">
            <owner name="TestSchema" start-index="12" stop-index="21"/>
        </table>
        <columns start-index="33" stop-index="48">
            <column name="Name" start-index="34" stop-index="37"/>
            <column name="Location" start-index="40" stop-index="47"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="Jared" start-index="58" stop-index="65"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="Australia" start-index="69" stop-index="80"/>
                </assignment-value>
            </value>
            <value>
                <assignment-value>
                    <literal-expression value="Nikita" start-index="85" stop-index="93"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="India" start-index="96" stop-index="103"/>
                </assignment-value>
            </value>
            <value>
                <assignment-value>
                    <literal-expression value="Tom" start-index="108" stop-index="113"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="Germany" start-index="116" stop-index="125"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_db_schema_name">
        <table name="Affiliation" start-index="12" stop-index="43">
            <owner name="dbo" start-index="29" stop-index="31">
                <owner name="ContosoWarehouse" start-index="12" stop-index="27"/>
            </owner>
        </table>
        <columns start-index="44" stop-index="44"/>
        <select>
            <projections start-index="52" stop-index="52">
                <shorthand-projection start-index="52" stop-index="52"/>
            </projections>
            <from>
                <simple-table name="Affiliation" start-index="59" stop-index="86">
                    <owner name="dbo" start-index="72" stop-index="74">
                        <owner name="My_Lakehouse" start-index="59" stop-index="70"/>
                    </owner>
                </simple-table>
            </from>
        </select>
    </insert>

    <insert sql-case-id="insert_with_exec">
        <table name="iris_rx_data" start-index="12" stop-index="23"/>
        <columns start-index="25" stop-index="98">
            <column name="Sepal.Length" start-delimiter="&quot;" end-delimiter="&quot;" start-index="26"
                    stop-index="39"/>
            <column name="Sepal.Width" start-delimiter="&quot;" end-delimiter="&quot;" start-index="42"
                    stop-index="54"/>
            <column name="Petal.Length" start-delimiter="&quot;" end-delimiter="&quot;" start-index="57"
                    stop-index="70"/>
            <column name="Petal.Width" start-delimiter="&quot;" end-delimiter="&quot;" start-index="73"
                    stop-index="85"/>
            <column name="Species" start-delimiter="&quot;" end-delimiter="&quot;" start-index="89" stop-index="97"/>
        </columns>
        <exec name="sp_execute_external_script" start-index="100" stop-index="183">
            <parameter>
                <binary-operation-expression start-index="135" stop-index="150">
                    <left>
                        <column start-index="135" stop-index="143" name="@language"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="R" start-index="147" stop-index="150"/>
                    </right>
                </binary-operation-expression>
            </parameter>
            <parameter>
                <binary-operation-expression start-index="154" stop-index="183">
                    <left>
                        <column start-index="154" stop-index="160" name="@script"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="iris_data &lt;- iris" start-index="164" stop-index="183"/>
                    </right>
                </binary-operation-expression>
            </parameter>
        </exec>
    </insert>

    <insert sql-case-id="insert_into_temp_table">
        <table name="#NonExistentTable" start-index="12" stop-index="28"/>
        <columns start-index="29" stop-index="29"/>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="10" start-index="38" stop-index="39"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_into_temp_table_with_null_value">
        <columns start-index="28" stop-index="28"/>
        <table name="#SampleTempTable" start-index="12" stop-index="27"/>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="10" start-index="37" stop-index="38"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="null" start-index="41" stop-index="44"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_into_temp_table_with_all_null_value">
        <columns start-index="28" stop-index="28"/>
        <table name="#SampleTempTable" start-index="12" stop-index="27"/>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="null" start-index="37" stop-index="40"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="null" start-index="43" stop-index="46"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_exec_mtcars">
        <table name="MTCars" start-index="12" stop-index="21">
            <owner name="dbo" start-index="12" stop-index="14"/>
        </table>
        <columns start-index="22" stop-index="22"/>
        <exec name="sp_execute_external_script" start-index="23" stop-index="156">
            <parameter>
                <binary-operation-expression start-index="55" stop-index="70">
                    <left>
                        <column name="@language" start-index="55" stop-index="63"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="R" start-index="67" stop-index="70"/>
                    </right>
                </binary-operation-expression>
            </parameter>
            <parameter>
                <binary-operation-expression start-index="73" stop-index="101">
                    <left>
                        <column name="@script" start-index="73" stop-index="79"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="MTCars &lt;- mtcars" start-index="83" stop-index="101"/>
                    </right>
                </binary-operation-expression>
            </parameter>
            <parameter>
                <binary-operation-expression start-index="105" stop-index="123">
                    <left>
                        <column name="@input_data_1" start-index="105" stop-index="117"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="" start-index="121" stop-index="123"/>
                    </right>
                </binary-operation-expression>
            </parameter>
            <parameter>
                <binary-operation-expression start-index="126" stop-index="156">
                    <left>
                        <column name="@output_data_1_name" start-index="126" stop-index="144"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="MTCars" start-index="148" stop-index="156"/>
                    </right>
                </binary-operation-expression>
            </parameter>
        </exec>
    </insert>

    <insert sql-case-id="insert_with_exec_model">
        <table name="GLM_models" start-index="12" stop-index="21"/>
        <columns start-index="22" stop-index="28">
            <column name="model" start-index="23" stop-index="27"/>
        </columns>
        <exec name="generate_GLM" start-index="30" stop-index="46"/>
    </insert>

    <insert sql-case-id="insert_with_table_hint">
        <table name="cci_target" start-index="12" stop-index="21"/>
        <columns start-index="37" stop-index="37"/>
        <table-hints start-index="23" stop-index="36">
            <table-hint value="TABLOCK" start-index="29" stop-index="35"/>
        </table-hints>
        <select>
            <projections start-index="45" stop-index="56">
                <top-projection start-index="45" stop-index="54">
                    <top-value value="300000" start-index="49" stop-index="54"/>
                </top-projection>
                <shorthand-projection start-index="56" stop-index="56"/>
            </projections>
            <from>
                <simple-table name="staging" start-index="63" stop-index="69"/>
            </from>
        </select>
    </insert>

    <insert sql-case-id="insert_sales_with_nchar">
        <table name="sales" start-index="12" stop-index="16"/>
        <columns start-index="17" stop-index="17"/>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="Canada" start-index="26" stop-index="34"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="British Columbia" start-index="37" stop-index="55"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="300" start-index="58" stop-index="60"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_sales_with_nchar_2">
        <table name="sales" start-index="12" stop-index="16"/>
        <columns start-index="17" stop-index="17"/>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="United States" start-index="26" stop-index="41"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="Montana" start-index="44" stop-index="53"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="100" start-index="56" stop-index="58"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_currency_value">
        <table name="Employees" start-index="12" stop-index="27" start-delimiter="[" end-delimiter="]">
            <owner name="HR" start-index="12" stop-index="15" start-delimiter="[" end-delimiter="]"/>
        </table>
        <columns start-index="28" stop-index="69">
            <column name="SSN" start-index="29" stop-index="33" start-delimiter="[" end-delimiter="]"/>
            <column name="FirstName" start-index="36" stop-index="46" start-delimiter="[" end-delimiter="]"/>
            <column name="LastName" start-index="49" stop-index="58" start-delimiter="[" end-delimiter="]"/>
            <column name="Salary" start-index="61" stop-index="68" start-delimiter="[" end-delimiter="]"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="***********" start-index="79" stop-index="91"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="Catherine" start-index="94" stop-index="105"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="Abel" start-index="108" stop-index="114"/>
                </assignment-value>
                <assignment-value>
                    <common-expression text="$31692" start-index="117" stop-index="122"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_select_from_open_row_1">
        <table name="myTable" start-index="12" stop-index="18"/>
        <columns start-index="19" stop-index="48">
            <column name="FileName" start-index="20" stop-index="27"/>
            <column name="FileType" start-index="30" stop-index="37"/>
            <column name="Document" start-index="40" stop-index="47"/>
        </columns>
        <select>
            <projections start-index="57" stop-index="102">
                <expression-projection text="Text1.txt" start-index="57" stop-index="79" alias="FileName"/>
                <expression-projection text=".txt" start-index="82" stop-index="99" alias="FileType"/>
                <shorthand-projection start-index="102" stop-index="102"/>
            </projections>
            <from>
                <function-table start-index="109" stop-index="153" table-alias="Document">
                    <table-function text="OPENROWSET(BULK N'C:\Text1.txt', SINGLE_BLOB)" function-name="OPENROWSET">
                        <parameter>
                            <literal-expression value="C:\Text1.txt" start-index="125" stop-index="139"/>
                        </parameter>
                        <parameter>
                            <column name="SINGLE_BLOB" start-index="142" stop-index="152"/>
                        </parameter>
                    </table-function>
                </function-table>
            </from>
        </select>
    </insert>

    <insert sql-case-id="insert_with_select_from_open_row_2">
        <table name="achievements" start-index="12" stop-index="23"/>
        <table-hints start-index="25" stop-index="38">
            <table-hint value="TABLOCK" start-index="31" stop-index="37"/>
        </table-hints>
        <columns start-index="40" stop-index="56">
            <column name="id" start-index="41" stop-index="42"/>
            <column name="description" start-index="45" stop-index="55"/>
        </columns>
        <select>
            <projections start-index="65" stop-index="65">
                <shorthand-projection start-index="65" stop-index="65"/>
            </projections>
            <from>
                <function-table start-index="72" stop-index="245" table-alias="DataFile">
                    <table-function function-name="OPENROWSET"
                                    text="OPENROWSET(BULK 'csv/achievements.csv', DATA_SOURCE = 'MyAzureBlobStorage', FORMAT ='CSV', FORMATFILE='csv/achievements-c.xml', FORMATFILE_DATA_SOURCE = 'MyAzureBlobStorage')">
                        <parameter>
                            <literal-expression value="csv/achievements.csv" start-index="88" stop-index="109"/>
                        </parameter>
                        <parameter>
                            <binary-operation-expression start-index="112" stop-index="145">
                                <left>
                                    <column name="DATA_SOURCE" start-index="112" stop-index="122"/>
                                </left>
                                <right>
                                    <literal-expression value="MyAzureBlobStorage" start-index="126" stop-index="145"/>
                                </right>
                                <operator>=</operator>
                            </binary-operation-expression>
                        </parameter>
                        <parameter>
                            <binary-operation-expression start-index="148" stop-index="160">
                                <left>
                                    <column name="FORMAT" start-index="148" stop-index="153"/>
                                </left>
                                <right>
                                    <literal-expression value="CSV" start-index="156" stop-index="140"/>
                                </right>
                                <operator>=</operator>
                            </binary-operation-expression>
                        </parameter>
                        <parameter>
                            <binary-operation-expression start-index="163" stop-index="197">
                                <left>
                                    <column name="FORMATFILE" start-index="163" stop-index="172"/>
                                </left>
                                <right>
                                    <literal-expression value="csv/achievements-c.xml" start-index="174"
                                                        stop-index="197"/>
                                </right>
                                <operator>=</operator>
                            </binary-operation-expression>
                        </parameter>
                        <parameter>
                            <binary-operation-expression start-index="200" stop-index="244">
                                <left>
                                    <column name="FORMATFILE_DATA_SOURCE" start-index="200" stop-index="221"/>
                                </left>
                                <right>
                                    <literal-expression value="MyAzureBlobStorage" start-index="225" stop-index="244"/>
                                </right>
                                <operator>=</operator>
                            </binary-operation-expression>
                        </parameter>
                    </table-function>
                </function-table>
            </from>
        </select>
    </insert>

    <insert sql-case-id="insert_into_table_get_date">
        <table name="#Test" start-index="12" stop-index="16"/>
        <columns start-index="17" stop-index="17"/>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="OC" start-index="26" stop-index="30"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="Ounces" start-index="33" stop-index="41"/>
                </assignment-value>
                <assignment-value>
                    <function function-name="GETDATE" text="GETDATE()" start-index="44" stop-index="52"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_into_table_from_table">
        <table name="#Test" start-index="12" stop-index="16"/>
        <columns start-index="17" stop-index="17"/>
        <select>
            <projections start-index="25" stop-index="25">
                <shorthand-projection start-index="25" stop-index="25"/>
            </projections>
            <from>
                <simple-table name="UnitMeasure" start-index="32" stop-index="53">
                    <owner name="Production" start-index="32" stop-index="41"/>
                </simple-table>
            </from>
        </select>
    </insert>

    <insert sql-case-id="insert_into_production_location">
        <table name="Location" start-index="12" stop-index="49">
            <owner name="Production" start-index="31" stop-index="40">
                <owner name="AdventureWorks2022" start-index="12" stop-index="29"/>
            </owner>
        </table>
        <columns start-index="50" stop-index="93">
            <column name="Name" start-index="51" stop-index="54"/>
            <column name="CostRate" start-index="57" stop-index="64"/>
            <column name="Availability" start-index="67" stop-index="78"/>
            <column name="ModifiedDate" start-index="81" stop-index="92"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <function function-name="NEWID" text="NEWID()" start-index="103" stop-index="109"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="0.5" start-index="112" stop-index="113"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="5.2" start-index="116" stop-index="118"/>
                </assignment-value>
                <assignment-value>
                    <function function-name="GETDATE" text="GETDATE()" start-index="121" stop-index="129"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_exec_prediction_results">
        <table name="prediction_results" start-index="12" stop-index="29"/>
        <columns start-index="31" stop-index="127">
            <column name="tipped_Pred" start-index="32" stop-index="42"/>
            <column name="payment_type" start-index="44" stop-index="55"/>
            <column name="tipped" start-index="57" stop-index="62"/>
            <column name="passenger_count" start-index="64" stop-index="78"/>
            <column name="trip_distance" start-index="80" stop-index="92"/>
            <column name="trip_time_in_secs" start-index="94" stop-index="110"/>
            <column name="direct_distance" start-index="112" stop-index="126"/>
        </columns>
        <exec name="predict_per_partition" start-index="129" stop-index="159" start-delimiter="[" end-delimiter="]"/>
    </insert>

    <insert sql-case-id="insert_into_sample_temp_table">
        <table name="#SampleTempTable" start-index="12" stop-index="27"/>
        <columns start-index="28" stop-index="28"/>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="null" start-index="37" stop-index="40"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="hello" start-index="43" stop-index="49"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_into_sample_temp_table_2">
        <table name="#SampleTempTable" start-index="12" stop-index="27"/>
        <columns start-index="28" stop-index="28"/>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="10" start-index="37" stop-index="38"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="null" start-index="41" stop-index="44"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_into_sample_temp_table_3">
        <table name="#SampleTempTable" start-index="12" stop-index="27"/>
        <columns start-index="28" stop-index="28"/>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="17" start-index="37" stop-index="38"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="abc" start-index="41" stop-index="45"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_into_sample_temp_table_4">
        <table name="#SampleTempTable" start-index="12" stop-index="27"/>
        <columns start-index="28" stop-index="28"/>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="17" start-index="37" stop-index="38"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="yes" start-index="41" stop-index="45"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_table_t">
        <table name="#t" start-index="12" stop-index="13"/>
        <columns start-index="14" stop-index="14"/>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="99" start-index="23" stop-index="24"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_table_test">
        <table name="##test" start-index="12" stop-index="17"/>
        <columns start-index="18" stop-index="18"/>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="1" start-index="27" stop-index="27"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="1" start-index="30" stop-index="30"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_into_with_select_number">
        <table name="#test" start-index="12" stop-index="16"/>
        <columns start-index="17" stop-index="17"/>
        <select>
            <projections start-index="25" stop-index="25">
                <expression-projection start-index="25" stop-index="25" text="1"/>
            </projections>
        </select>
    </insert>

    <insert sql-case-id="insert_sales_with_nchar_3">
        <table name="sales" start-index="12" stop-index="16"/>
        <columns start-index="17" stop-index="17"/>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="Canada" start-index="26" stop-index="34"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="Alberta" start-index="37" stop-index="46"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="100" start-index="49" stop-index="51"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_into_tmp_with_exec">
        <table name="#temp" start-index="12" stop-index="16"/>
        <columns start-index="18" stop-index="325">
            <column name="id" start-index="19" stop-index="20"/>
            <column name="job_name" start-index="23" stop-index="30"/>
            <column name="job_id" start-index="33" stop-index="38"/>
            <column name="dynamic_filter_login" start-index="41" stop-index="60"/>
            <column name="dynamic_filter_hostname" start-index="62" stop-index="84"/>
            <column name="dynamic_snapshot_location" start-index="87" stop-index="111"/>
            <column name="frequency_type" start-index="113" stop-index="126"/>
            <column name="frequency_interval" start-index="129" stop-index="146"/>
            <column name="frequency_subday_type" start-index="149" stop-index="169"/>
            <column name="frequency_subday_interval" start-index="171" stop-index="195"/>
            <column name="frequency_relative_interval" start-index="198" stop-index="224"/>
            <column name="frequency_recurrence_factor" start-index="227" stop-index="253"/>
            <column name="active_start_date" start-index="256" stop-index="272"/>
            <column name="active_end_date" start-index="275" stop-index="289"/>
            <column name="active_start_time" start-index="292" stop-index="308"/>
            <column name="active_end_time" start-index="310" stop-index="324"/>
        </columns>
        <exec name="sp_helpdynamicsnapshot_job" start-index="327" stop-index="357"/>
    </insert>

    <insert sql-case-id="insert_into_player_with_compress_function">
        <table name="player" start-index="12" stop-index="17"/>
        <columns start-index="19" stop-index="38">
            <column name="name" start-index="21" stop-index="24"/>
            <column name="surname" start-index="26" stop-index="32"/>
            <column name="info" start-index="34" stop-index="37"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="Ovidiu" start-index="48" stop-index="56"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="Cracium" start-index="59" stop-index="68"/>
                </assignment-value>
                <assignment-value>
                    <function function-name="COMPRESS"
                              text="COMPRESS(N'{&quot;sport&quot;:&quot;Tennis&quot;,&quot;age&quot;: 28,&quot;rank&quot;:1,&quot;points&quot;:15258, &quot;turn&quot;:17}')"
                              start-index="70" stop-index="145">
                        <parameter>
                            <literal-expression
                                    value="{&quot;sport&quot;:&quot;Tennis&quot;,&quot;age&quot;: 28,&quot;rank&quot;:1,&quot;points&quot;:15258, &quot;turn&quot;:17}"
                                    start-index="79" stop-index="144"/>
                        </parameter>
                    </function>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_into_player_with_compress_function_2">
        <table name="player" start-index="12" stop-index="17"/>
        <columns start-index="19" stop-index="37">
            <column name="name" start-index="20" stop-index="23"/>
            <column name="surname" start-index="25" stop-index="31"/>
            <column name="info" start-index="33" stop-index="36"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="Michael" start-index="47" stop-index="56"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="Raheem" start-index="58" stop-index="66"/>
                </assignment-value>
                <assignment-value>
                    <function function-name="COMPRESS" text="COMPRESS(@info)" start-index="68" stop-index="82">
                        <parameter>
                            <column name="@info" start-index="77" stop-index="81"/>
                        </parameter>
                    </function>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_currency_value_2">
        <table name="Employees" start-index="12" stop-index="27" start-delimiter="[" end-delimiter="]">
            <owner name="HR" start-index="12" stop-index="15" start-delimiter="[" end-delimiter="]"/>
        </table>
        <columns start-index="28" stop-index="69">
            <column name="SSN" start-index="29" stop-index="33" start-delimiter="[" end-delimiter="]"/>
            <column name="FirstName" start-index="36" stop-index="46" start-delimiter="[" end-delimiter="]"/>
            <column name="LastName" start-index="49" stop-index="58" start-delimiter="[" end-delimiter="]"/>
            <column name="Salary" start-index="61" stop-index="68" start-delimiter="[" end-delimiter="]"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="***********" start-index="79" stop-index="91"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="Kim" start-index="94" stop-index="99"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="Abercrombie" start-index="102" stop-index="115"/>
                </assignment-value>
                <assignment-value>
                    <common-expression text="$55415" start-index="118" stop-index="123"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_cross_database_values">
        <table name="SourceTable" start-index="7" stop-index="42" start-delimiter="[" end-delimiter="]">
            <owner name="dbo" start-index="24" stop-index="28" start-delimiter="[" end-delimiter="]">
                <owner name="SourceDatabase" start-index="7" stop-index="22" start-delimiter="[" end-delimiter="]"/>
            </owner>
        </table>
        <columns start-index="43" stop-index="43"/>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="1" start-index="52" stop-index="52"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="Bob" start-index="55" stop-index="60"/>
                </assignment-value>
            </value>
            <value>
                <assignment-value>
                    <literal-expression value="2" start-index="64" stop-index="64"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="Susan" start-index="67" stop-index="74"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_cross_database_select">
        <table name="DestTable_InMem" start-index="7" stop-index="51" start-delimiter="[" end-delimiter="]">
            <owner name="dbo" start-index="29" stop-index="33" start-delimiter="[" end-delimiter="]">
                <owner name="DestinationDatabase" start-index="7" stop-index="27" start-delimiter="["
                       end-delimiter="]"/>
            </owner>
        </table>
        <columns start-index="52" stop-index="52"/>
        <select>
            <projections start-index="60" stop-index="60">
                <shorthand-projection start-index="60" stop-index="60"/>
            </projections>
            <from>
                <simple-table name="SourceTable" start-index="67" stop-index="102" start-delimiter="["
                              end-delimiter="]">
                    <owner name="dbo" start-index="84" stop-index="88" start-delimiter="[" end-delimiter="]">
                        <owner name="SourceDatabase" start-index="67" stop-index="82" start-delimiter="["
                               end-delimiter="]"/>
                    </owner>
                </simple-table>
            </from>
        </select>
    </insert>

    <insert sql-case-id="insert_with_select_with_table_hint_no_with_keyword">
        <table name="#tmpdbs" start-index="12" stop-index="18"/>
        <columns start-index="20" stop-index="47">
            <column name="dbid" start-index="21" stop-index="26" start-delimiter="[" end-delimiter="]"/>
            <column name="dbname" start-index="29" stop-index="36" start-delimiter="[" end-delimiter="]"/>
            <column name="isdone" start-index="39" stop-index="46" start-delimiter="[" end-delimiter="]"/>
        </columns>
        <select>
            <projections start-index="56" stop-index="75">
                <column-projection name="database_id" start-index="56" stop-index="66"/>
                <column-projection name="name" start-index="69" stop-index="72"/>
                <expression-projection text="0" start-index="75" stop-index="75">
                    <expr>
                        <literal-expression value="0" start-index="75" stop-index="75"/>
                    </expr>
                </expression-projection>
            </projections>
            <from>
                <simple-table name="databases" start-index="82" stop-index="101">
                    <owner name="sys" start-index="89" stop-index="91">
                        <owner name="master" start-index="82" stop-index="87"/>
                    </owner>
                </simple-table>
            </from>
            <table-hints start-index="103" stop-index="110">
                <table-hint value="NOLOCK" start-index="104" stop-index="109"/>
            </table-hints>
            <where start-index="112" stop-index="147">
                <expr>
                    <binary-operation-expression start-index="118" stop-index="147">
                        <left>
                            <binary-operation-expression start-index="118" stop-index="133">
                                <left>
                                    <column name="is_read_only" start-index="118" stop-index="129"/>
                                </left>
                                <right>
                                    <literal-expression value="0" start-index="133" stop-index="133"/>
                                </right>
                                <operator>=</operator>
                            </binary-operation-expression>
                        </left>
                        <right>
                            <binary-operation-expression start-index="139" stop-index="147">
                                <left>
                                    <column name="state" start-index="139" stop-index="143"/>
                                </left>
                                <right>
                                    <literal-expression value="0" start-index="147" stop-index="147"/>
                                </right>
                                <operator>=</operator>
                            </binary-operation-expression>
                        </right>
                        <operator>AND</operator>
                    </binary-operation-expression>
                </expr>
            </where>
        </select>
    </insert>

    <insert sql-case-id="insert_with_open_query_function_limit">
        <rowset-function
                text="OPENQUERY (MyLinkServer, 'SELECT Name, GroupName FROM AdventureWorks2022.HumanResources.Department')"
                function-name="OPENQUERY" start-index="7" stop-index="106">
            <parameter>
                <column name="MyLinkServer" start-index="18" stop-index="29"/>
            </parameter>
            <parameter>
                <literal-expression value="SELECT Name, GroupName FROM AdventureWorks2022.HumanResources.Department"
                                    start-index="32" stop-index="105"/>
            </parameter>
        </rowset-function>
        <columns start-index="107" stop-index="107"/>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="Environmental Impact" start-index="116" stop-index="137"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="Engineering" start-index="140" stop-index="152"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_hint_and_open_row_set_function">
        <table name="Department" start-index="12" stop-index="36">
            <owner name="HumanResources" start-index="12" stop-index="25"/>
        </table>
        <columns start-index="61" stop-index="77">
            <column name="Name" start-index="62" stop-index="65"/>
            <column name="GroupName" start-index="68" stop-index="76"/>
        </columns>
        <table-hints start-index="38" stop-index="59">
            <table-hint value="IGNORE_TRIGGERS" start-index="44" stop-index="58"/>
        </table-hints>
        <select>
            <projections start-index="86" stop-index="104">
                <column-projection name="Name" start-index="86" stop-index="91">
                    <owner name="b" start-index="86" stop-index="86"/>
                </column-projection>
                <column-projection name="GroupName" start-index="94" stop-index="104">
                    <owner name="b" start-index="94" stop-index="94"/>
                </column-projection>
            </projections>
            <from>
                <function-table start-index="111" stop-index="231" table-alias="b">
                    <table-function function-name="OPENROWSET"
                                    text="OPENROWSET (BULK 'C:SQLFilesDepartmentData.txt', FORMATFILE = 'C:SQLFilesBulkloadFormatFile.xml', ROWS_PER_BATCH = 15000)">
                        <parameter>
                            <literal-expression value="C:SQLFilesDepartmentData.txt" start-index="128"
                                                stop-index="157"/>
                        </parameter>
                        <parameter>
                            <binary-operation-expression start-index="160" stop-index="206">
                                <left>
                                    <column name="FORMATFILE" start-index="160" stop-index="169"/>
                                </left>
                                <right>
                                    <literal-expression value="C:SQLFilesBulkloadFormatFile.xml" start-index="173"
                                                        stop-index="206"/>
                                </right>
                                <operator>=</operator>
                            </binary-operation-expression>
                        </parameter>
                        <parameter>
                            <binary-operation-expression start-index="209" stop-index="230">
                                <left>
                                    <column name="ROWS_PER_BATCH" start-index="209" stop-index="222"/>
                                </left>
                                <right>
                                    <literal-expression value="15000" start-index="226" stop-index="230"/>
                                </right>
                                <operator>=</operator>
                            </binary-operation-expression>
                        </parameter>
                    </table-function>
                </function-table>
            </from>
        </select>
    </insert>

    <insert sql-case-id="insert_with_xlock_hint">
        <table name="Location" start-index="12" stop-index="30">
            <owner name="Production" start-index="12" stop-index="21"/>
        </table>
        <table-hints start-index="32" stop-index="43">
            <table-hint value="XLOCK" start-index="38" stop-index="42"/>
        </table-hints>
        <columns start-index="45" stop-index="74">
            <column name="Name" start-index="46" stop-index="49"/>
            <column name="CostRate" start-index="52" stop-index="59"/>
            <column name="Availability" start-index="62" stop-index="73"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="Final Inventory" start-index="85" stop-index="102"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="15.00" start-index="105" stop-index="109"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="80.00" start-index="112" stop-index="116"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_with_output_input">
        <table name="ScrapReason" start-index="7" stop-index="28">
            <owner name="Production" start-index="7" stop-index="16"/>
        </table>
        <columns start-index="29" stop-index="29"/>
        <output start-index="30" stop-index="113">
            <output-columns start-index="37" stop-index="96">
                <column-projection name="ScrapReasonID" start-index="37" stop-index="58"/>
                <column-projection name="Name" start-index="61" stop-index="73"/>
                <column-projection name="ModifiedDate" start-index="76" stop-index="96"/>
            </output-columns>
            <output-table name="@MyTableVar" start-index="103" stop-index="113"/>
        </output>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="Operator error" start-index="123" stop-index="139"/>
                </assignment-value>
                <assignment-value>
                    <function text="GETDATE()" start-index="142" stop-index="150" function-name="GETDATE"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_into_with_multi_nchar">
        <table name="UnitMeasure" start-index="12" stop-index="33">
            <owner name="Production" start-index="12" stop-index="21"/>
        </table>
        <columns start-index="34" stop-index="34"/>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="FT2" start-index="43" stop-index="48"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="Square Feet " start-index="51" stop-index="65"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="20080923" start-index="68" stop-index="77"/>
                </assignment-value>
            </value>
            <value>
                <assignment-value>
                    <literal-expression value="Y" start-index="82" stop-index="85"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="Yards" start-index="88" stop-index="95"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="20080923" start-index="98" stop-index="107"/>
                </assignment-value>
            </value>
            <value>
                <assignment-value>
                    <literal-expression value="Y3" start-index="112" stop-index="116"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="Cubic Yards" start-index="119" stop-index="132"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="20080923" start-index="135" stop-index="144"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_into_with_select_with_merge">
        <table name="ZeroInventory" start-index="12" stop-index="35">
            <owner name="Production" start-index="12" stop-index="21"/>
        </table>
        <columns start-index="37" stop-index="69">
            <column name="DeletedProductID" start-index="38" stop-index="53"/>
            <column name="RemovedOnDate" start-index="56" stop-index="68"/>
        </columns>
        <select>
            <projections start-index="78" stop-index="97">
                <column-projection name="ProductID" start-index="78" stop-index="86"/>
                <expression-projection text="GETDATE()" start-index="89" stop-index="97">
                    <expr>
                        <function function-name="GETDATE" start-index="89" stop-index="97" text="GETDATE()"/>
                    </expr>
                </expression-projection>
            </projections>
            <from>
                <subquery-table alias="Changes" start-index="104" stop-index="582">
                    <subquery>
                        <merge>
                            <source>
                                <subquery-table alias="src" start-index="151" stop-index="349">
                                    <subquery>
                                        <select>
                                            <projections start-index="159" stop-index="182">
                                                <column-projection name="ProductID" start-index="159" stop-index="167"/>
                                                <aggregation-projection type="SUM" expression="SUM(OrderQty)"
                                                                        start-index="170" stop-index="182">
                                                    <paramters>
                                                        <column name="OrderQty" start-index="174" stop-index="181"/>
                                                    </paramters>
                                                </aggregation-projection>
                                            </projections>
                                            <from>
                                                <join-table join-type="INNER">
                                                    <left>
                                                        <simple-table name="SalesOrderDetail" alias="sod"
                                                                      start-index="189" stop-index="217">
                                                            <owner name="Sales" start-index="189" stop-index="193"/>
                                                        </simple-table>
                                                    </left>
                                                    <right>
                                                        <simple-table name="SalesOrderHeader" alias="soh"
                                                                      start-index="224" stop-index="252">
                                                            <owner name="Sales" start-index="224" stop-index="228"/>
                                                        </simple-table>
                                                    </right>
                                                    <on-condition>
                                                        <binary-operation-expression start-index="257" stop-index="322">
                                                            <left>
                                                                <binary-operation-expression start-index="257"
                                                                                             stop-index="291">
                                                                    <left>
                                                                        <column name="SalesOrderID" start-index="257"
                                                                                stop-index="272">
                                                                            <owner name="sod" start-index="257"
                                                                                   stop-index="259"/>
                                                                        </column>
                                                                    </left>
                                                                    <right>
                                                                        <column name="SalesOrderID" start-index="276"
                                                                                stop-index="291">
                                                                            <owner name="soh" start-index="276"
                                                                                   stop-index="278"/>
                                                                        </column>
                                                                    </right>
                                                                    <operator>=</operator>
                                                                </binary-operation-expression>
                                                            </left>
                                                            <right>
                                                                <binary-operation-expression start-index="297"
                                                                                             stop-index="322">
                                                                    <left>
                                                                        <column name="OrderDate" start-index="297"
                                                                                stop-index="309">
                                                                            <owner name="soh" start-index="297"
                                                                                   stop-index="299"/>
                                                                        </column>
                                                                    </left>
                                                                    <right>
                                                                        <literal-expression value="20070401"
                                                                                            start-index="313"
                                                                                            stop-index="322"/>
                                                                    </right>
                                                                    <operator>=</operator>
                                                                </binary-operation-expression>
                                                            </right>
                                                            <operator>AND</operator>
                                                        </binary-operation-expression>
                                                    </on-condition>
                                                </join-table>
                                            </from>
                                            <group-by>
                                                <column-item name="ProductID" order-direction="ASC" start-index="333"
                                                             stop-index="341"/>
                                            </group-by>
                                        </select>
                                    </subquery>
                                </subquery-table>
                            </source>
                            <target>
                                <simple-table name="ProductInventory" alias="pi" start-index="111" stop-index="143">
                                    <owner name="Production" start-index="111" stop-index="120"/>
                                </simple-table>
                            </target>
                            <expr>
                                <binary-operation-expression start-index="377" stop-index="404">
                                    <left>
                                        <column name="ProductID" start-index="377" stop-index="388">
                                            <owner name="pi" start-index="377" stop-index="378"/>
                                        </column>
                                    </left>
                                    <right>
                                        <column name="ProductID" start-index="392" stop-index="404">
                                            <owner name="src" start-index="392" stop-index="394"/>
                                        </column>
                                    </right>
                                    <operator>=</operator>
                                </binary-operation-expression>
                            </expr>
                            <merge-items start-index="407" stop-index="466"
                                         text="WHEN MATCHED AND pi.Quantity - src.OrderQty &lt;= 0 THEN DELETE">
                                <expr>
                                    <binary-operation-expression start-index="424" stop-index="454">
                                        <left>
                                            <binary-operation-expression start-index="424" stop-index="449">
                                                <left>
                                                    <column name="Quantity" start-index="424" stop-index="434">
                                                        <owner name="pi" start-index="424" stop-index="425"/>
                                                    </column>
                                                </left>
                                                <right>
                                                    <column name="OrderQty" start-index="438" stop-index="449">
                                                        <owner name="src" start-index="438" stop-index="440"/>
                                                    </column>
                                                </right>
                                                <operator>-</operator>
                                            </binary-operation-expression>
                                        </left>
                                        <right>
                                            <literal-expression value="0" start-index="454" stop-index="454"/>
                                        </right>
                                        <operator>&lt;=</operator>
                                    </binary-operation-expression>
                                </expr>
                            </merge-items>
                            <merge-items text="WHEN MATCHED THEN UPDATE SET pi.Quantity = pi.Quantity - src.OrderQty"
                                         start-index="468" stop-index="536">
                                <update>
                                    <set start-index="493" stop-index="536">
                                        <assignment start-index="497" stop-index="536">
                                            <column name="Quantity" start-index="497" stop-index="507">
                                                <owner name="pi" start-index="497" stop-index="498"/>
                                            </column>
                                            <assignment-value>
                                                <binary-operation-expression start-index="511" stop-index="536">
                                                    <left>
                                                        <column name="Quantity" start-index="511" stop-index="521">
                                                            <owner name="pi" start-index="511" stop-index="512"/>
                                                        </column>
                                                    </left>
                                                    <right>
                                                        <column name="OrderQty" start-index="525" stop-index="536">
                                                            <owner name="src" start-index="525" stop-index="527"/>
                                                        </column>
                                                    </right>
                                                    <operator>-</operator>
                                                </binary-operation-expression>
                                            </assignment-value>
                                        </assignment>
                                    </set>
                                </update>
                            </merge-items>
                            <output start-index="538" stop-index="570">
                                <output-columns start-index="545" stop-index="570">
                                    <column-projection name="ProductID" start-index="554" stop-index="570"/>
                                    <expression-projection text="$action" start-index="545" stop-index="551">
                                        <expr>
                                            <common-expression text="$action" start-index="545" stop-index="551"/>
                                        </expr>
                                    </expression-projection>
                                </output-columns>
                            </output>
                        </merge>
                    </subquery>
                </subquery-table>
            </from>
            <where start-index="604" stop-index="626">
                <expr>
                    <binary-operation-expression start-index="610" stop-index="626">
                        <left>
                            <column name="Action" start-index="610" stop-index="615"/>
                        </left>
                        <right>
                            <literal-expression value="DELETE" start-index="619" stop-index="626"/>
                        </right>
                        <operator>=</operator>
                    </binary-operation-expression>
                </expr>
            </where>
        </select>
    </insert>

    <insert sql-case-id="insert_into_sample_temp_table_5">
        <table name="#MyTempTable" start-index="12" stop-index="23"/>
        <columns start-index="24" stop-index="24"/>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="1" start-index="33" stop-index="33"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_into_with_conflict_action_do_nothing" parameters="1">
        <table name="sj_event" start-index="12" stop-index="19"/>
        <columns start-index="20" stop-index="29">
            <column name="event_id" start-index="21" stop-index="28"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="39" stop-index="39"/>
                    <literal-expression value="1" start-index="39" stop-index="39"/>
                </assignment-value>
            </value>
        </values>
        <on-duplicate-key-columns start-index="42" stop-index="73"/>
    </insert>

    <insert sql-case-id="insert_on_duplicate_key_with_column_aliases">
        <table name="t" start-index="12" stop-index="12"/>
        <set start-index="14" stop-index="24">
            <assignment>
                <column name="a" start-index="18" stop-index="18"/>
                <assignment-value>
                    <literal-expression value="9" literal-start-index="20" literal-stop-index="20"/>
                </assignment-value>
            </assignment>
            <assignment>
                <column name="b" start-index="22" stop-index="22"/>
                <assignment-value>
                    <literal-expression value="5" start-index="24" stop-index="24"/>
                </assignment-value>
            </assignment>
        </set>
        <on-duplicate-key-columns start-index="26" stop-index="66">
            <assignment start-index="62" stop-index="66">
                <column name="a" start-index="62" stop-index="62"/>
                <assignment-value>
                    <binary-operation-expression start-index="64" stop-index="66">
                        <operator>+</operator>
                        <left>
                            <column name="m" start-index="64" stop-index="64"/>
                        </left>
                        <right>
                            <column name="n" start-index="66" stop-index="66"/>
                        </right>
                    </binary-operation-expression>
                </assignment-value>
            </assignment>
        </on-duplicate-key-columns>
    </insert>

    <insert sql-case-id="insert_with_aes_encrypt">
        <table name="t_order_item" start-index="12" stop-index="23"/>
        <columns start-index="24" stop-index="40">
            <column name="item_id" start-index="25" stop-index="31"/>
            <column name="encrypt" start-index="33" stop-index="39"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="1" start-index="50" stop-index="50"/>
                </assignment-value>
                <assignment-value>
                    <function function-name="AES_ENCRYPT" text="AES_ENCRYPT('text','key_str')" start-index="52"
                              stop-index="80">
                        <parameter>
                            <literal-expression value="text" start-index="64" stop-index="69"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="key_str" start-index="71" stop-index="79"/>
                        </parameter>
                    </function>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="insert_on_duplicate_key_update_with_values">
        <table name="table" start-index="12" stop-index="16"/>
        <columns start-index="18" stop-index="24">
            <column name="a" start-index="19" stop-index="19"/>
            <column name="b" start-index="21" stop-index="21"/>
            <column name="c" start-index="23" stop-index="23"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="1" start-index="34" stop-index="34"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="2" start-index="36" stop-index="36"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="3" start-index="38" stop-index="38"/>
                </assignment-value>
            </value>
            <value>
                <assignment-value>
                    <literal-expression value="4" start-index="42" stop-index="42"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="5" start-index="44" stop-index="44"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="6" start-index="46" stop-index="46"/>
                </assignment-value>
            </value>
        </values>
        <on-duplicate-key-columns start-index="51" stop-index="93" literal-start-index="49" literal-stop-index="93">
            <assignment start-index="73" stop-index="93">
                <column name="c" start-index="73" stop-index="73"/>
                <assignment-value>
                    <binary-operation-expression start-index="75" stop-index="93" literal-start-index="75"
                                                 literal-stop-index="93">
                        <left>
                            <function function-name="VALUES" text="VALUES(a)" start-index="75" stop-index="83">
                                <parameter>
                                    <column name="a" start-index="82" stop-index="82" literal-start-index="82"
                                            literal-stop-index="82"/>
                                </parameter>
                            </function>
                        </left>
                        <operator>+</operator>
                        <right>
                            <function function-name="VALUES" text="VALUES(b)" start-index="85" stop-index="93">
                                <parameter>
                                    <column name="b" start-index="92" stop-index="92" literal-start-index="92"
                                            literal-stop-index="92"/>
                                </parameter>
                            </function>
                        </right>
                    </binary-operation-expression>
                </assignment-value>
            </assignment>
        </on-duplicate-key-columns>
    </insert>
    <insert sql-case-id="insert_returning_expressions">
        <table name="t2" start-index="12" stop-index="13"/>
        <columns start-index="15" stop-index="18">
            <column name="id" start-index="16" stop-index="17"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="2" start-index="28" stop-index="28"/>
                </assignment-value>
            </value>
            <value>
                <assignment-value>
                    <literal-expression value="3" start-index="32" stop-index="32"/>
                </assignment-value>
            </value>
        </values>
        <returning start-index="35" stop-index="50">
            <projections start-index="45" stop-index="50">
                <column-projection name="id" start-index="45" stop-index="46"/>
                <expression-projection start-index="48" stop-index="50" text="t&amp;t">
                    <left>
                        <column name="t" start-index="48" stop-index="48"/>
                    </left>
                    <operator>&amp;</operator>
                    <right>
                        <column name="t" start-index="50" stop-index="50"/>
                    </right>
                </expression-projection>
            </projections>
        </returning>
    </insert>
</sql-parser-test-cases>
