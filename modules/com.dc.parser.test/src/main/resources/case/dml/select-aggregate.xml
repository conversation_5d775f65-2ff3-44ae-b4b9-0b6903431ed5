<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <select sql-case-id="select_sum">
        <from>
            <simple-table name="t_order" start-index="40" stop-index="46"/>
        </from>
        <projections start-index="7" stop-index="33">
            <aggregation-projection type="SUM" expression="SUM(user_id)" alias="user_id_sum" start-index="7"
                                    stop-index="18"/>
        </projections>
    </select>

    <select sql-case-id="select_sum_column">
        <from>
            <simple-table name="t_order" start-index="48" stop-index="54"/>
        </from>
        <projections start-index="7" stop-index="41">
            <aggregation-projection type="SUM" expression="SUM(t_order.user_id)" alias="user_id_sum" start-index="7"
                                    stop-index="26">
                <paramters>
                    <column name="user_id" start-index="17" stop-index="25">
                        <owner name="t_order" start-index="8" stop-index="16"/>
                    </column>
                </paramters>
            </aggregation-projection>
        </projections>
    </select>

    <select sql-case-id="select_count">
        <from>
            <simple-table name="t_order" start-index="37" stop-index="43"/>
        </from>
        <projections start-index="7" stop-index="30">
            <aggregation-projection type="COUNT" alias="orders_count" expression="COUNT(*)" start-index="7"
                                    stop-index="14"/>
        </projections>
    </select>

    <select sql-case-id="select_count_with_sub">
        <from>
            <simple-table name="t_order" start-index="37" stop-index="43"/>
        </from>
        <projections start-index="7" stop-index="30">
            <aggregation-projection type="COUNT" alias="orders_count" expression="COUNT(*)" start-index="7"
                                    stop-index="14"/>
        </projections>
        <where start-index="45" stop-index="64">
            <expr>
                <binary-operation-expression start-index="51" stop-index="64">
                    <left>
                        <column name="order_id" start-index="51" stop-index="58"/>
                    </left>
                    <operator>&gt;</operator>
                    <right>
                        <binary-operation-expression start-index="62" stop-index="64">
                            <left>
                                <literal-expression value="1" start-index="62" stop-index="62"/>
                            </left>
                            <operator>-</operator>
                            <right>
                                <literal-expression value="1" start-index="64" stop-index="64"/>
                            </right>
                        </binary-operation-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_count_with_sub_with_whitespace">
        <from>
            <simple-table name="t_order" start-index="37" stop-index="43"/>
        </from>
        <projections start-index="7" stop-index="30">
            <aggregation-projection type="COUNT" alias="orders_count" expression="COUNT(*)" start-index="7"
                                    stop-index="14"/>
        </projections>
        <where start-index="45" stop-index="66">
            <expr>
                <binary-operation-expression start-index="51" stop-index="66">
                    <left>
                        <column name="order_id" start-index="51" stop-index="58"/>
                    </left>
                    <operator>&gt;</operator>
                    <right>
                        <binary-operation-expression start-index="62" stop-index="66">
                            <left>
                                <literal-expression value="1" start-index="62" stop-index="62"/>
                            </left>
                            <operator>-</operator>
                            <right>
                                <literal-expression value="1" start-index="66" stop-index="66"/>
                            </right>
                        </binary-operation-expression>
                        <common-expression text="1 - 1" start-index="62" stop-index="66"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_max">
        <from>
            <simple-table name="t_order" start-index="40" stop-index="46"/>
        </from>
        <projections start-index="7" stop-index="33">
            <aggregation-projection type="MAX" expression="MAX(user_id)" alias="max_user_id" start-index="7"
                                    stop-index="18"/>
        </projections>
    </select>

    <select sql-case-id="select_min">
        <from>
            <simple-table name="t_order" start-index="40" stop-index="46"/>
        </from>
        <projections start-index="7" stop-index="33">
            <aggregation-projection type="MIN" expression="MIN(user_id)" alias="min_user_id" start-index="7"
                                    stop-index="18"/>
        </projections>
    </select>

    <select sql-case-id="select_avg">
        <from>
            <simple-table name="t_order" start-index="40" stop-index="46"/>
        </from>
        <projections start-index="7" stop-index="33">
            <aggregation-projection type="AVG" expression="AVG(user_id)" alias="user_id_avg" start-index="7"
                                    stop-index="18"/>
        </projections>
    </select>

    <select sql-case-id="select_count_with_binding_tables_without_join" parameters="1, 2, 9, 10">
        <from>
            <join-table join-type="COMMA">
                <left>
                    <simple-table name="t_order" alias="o" start-index="36" stop-index="44"/>
                </left>
                <right>
                    <simple-table name="t_order_item" alias="i" start-index="47" stop-index="60"/>
                </right>
            </join-table>
        </from>
        <projections start-index="7" stop-index="29">
            <aggregation-projection type="COUNT" expression="COUNT(*)" alias="items_count" start-index="7"
                                    stop-index="14"/>
        </projections>
        <where start-index="62" stop-index="171" literal-stop-index="172">
            <expr>
                <binary-operation-expression start-index="68" stop-index="171" literal-stop-index="172">
                    <left>
                        <binary-operation-expression start-index="68" stop-index="140">
                            <left>
                                <binary-operation-expression start-index="68" stop-index="116">
                                    <left>
                                        <binary-operation-expression start-index="68" stop-index="88">
                                            <left>
                                                <column name="user_id" start-index="68" stop-index="76">
                                                    <owner name="o" start-index="68" stop-index="68"/>
                                                </column>
                                            </left>
                                            <operator>=</operator>
                                            <right>
                                                <column name="user_id" start-index="80" stop-index="88">
                                                    <owner name="i" start-index="80" stop-index="80"/>
                                                </column>
                                            </right>
                                        </binary-operation-expression>
                                    </left>
                                    <operator>AND</operator>
                                    <right>
                                        <binary-operation-expression start-index="94" stop-index="116">
                                            <left>
                                                <column name="order_id" start-index="94" stop-index="103">
                                                    <owner name="o" start-index="94" stop-index="94"/>
                                                </column>
                                            </left>
                                            <operator>=</operator>
                                            <right>
                                                <column name="order_id" start-index="107" stop-index="116">
                                                    <owner name="i" start-index="107" stop-index="107"/>
                                                </column>
                                            </right>
                                        </binary-operation-expression>
                                    </right>
                                </binary-operation-expression>
                            </left>
                            <operator>AND</operator>
                            <right>
                                <in-expression start-index="122" stop-index="140">
                                    <not>false</not>
                                    <left>
                                        <column name="user_id" start-index="122" stop-index="130">
                                            <owner name="o" start-index="122" stop-index="122"/>
                                        </column>
                                    </left>
                                    <right>
                                        <list-expression start-index="135" stop-index="140">
                                            <items>
                                                <literal-expression value="1" start-index="136" stop-index="136"/>
                                                <parameter-marker-expression parameter-index="0" start-index="136"
                                                                             stop-index="136"/>
                                            </items>
                                            <items>
                                                <literal-expression value="2" start-index="139" stop-index="139"/>
                                                <parameter-marker-expression parameter-index="1" start-index="139"
                                                                             stop-index="139"/>
                                            </items>
                                        </list-expression>
                                    </right>
                                </in-expression>
                            </right>
                        </binary-operation-expression>
                    </left>
                    <operator>AND</operator>
                    <right>
                        <between-expression start-index="146" stop-index="171" literal-stop-index="172">
                            <not>false</not>
                            <left>
                                <column name="order_id" start-index="146" stop-index="155">
                                    <owner name="o" start-index="146" stop-index="146"/>
                                </column>
                            </left>
                            <between-expr>
                                <literal-expression value="9" start-index="165" stop-index="165"/>
                                <parameter-marker-expression parameter-index="2" start-index="165" stop-index="165"/>
                            </between-expr>
                            <and-expr>
                                <literal-expression value="10" start-index="171" stop-index="172"/>
                                <parameter-marker-expression parameter-index="3" start-index="171" stop-index="171"/>
                            </and-expr>
                        </between-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_count_with_binding_tables_with_join" parameters="1, 2, 9, 10">
        <from>
            <join-table join-type="INNER">
                <left>
                    <simple-table name="t_order" alias="o" start-index="36" stop-index="44"/>
                </left>
                <right>
                    <simple-table name="t_order_item" alias="i" start-index="51" stop-index="64"/>
                </right>
                <on-condition>
                    <binary-operation-expression start-index="69" stop-index="117">
                        <left>
                            <binary-operation-expression start-index="69" stop-index="89">
                                <left>
                                    <column name="user_id" start-index="69" stop-index="77">
                                        <owner name="o" start-index="69" stop-index="69"/>
                                    </column>
                                </left>
                                <operator>=</operator>
                                <right>
                                    <column name="user_id" start-index="81" stop-index="89">
                                        <owner name="i" start-index="81" stop-index="81"/>
                                    </column>
                                </right>
                            </binary-operation-expression>
                        </left>
                        <operator>AND</operator>
                        <right>
                            <binary-operation-expression start-index="95" stop-index="117">
                                <left>
                                    <column name="order_id" start-index="95" stop-index="104">
                                        <owner name="o" start-index="95" stop-index="95"/>
                                    </column>
                                </left>
                                <operator>=</operator>
                                <right>
                                    <column name="order_id" start-index="108" stop-index="117">
                                        <owner name="i" start-index="108" stop-index="108"/>
                                    </column>
                                </right>
                            </binary-operation-expression>
                        </right>
                    </binary-operation-expression>
                </on-condition>
            </join-table>
        </from>
        <projections start-index="7" stop-index="29">
            <aggregation-projection type="COUNT" expression="COUNT(*)" alias="items_count" start-index="7"
                                    stop-index="14"/>
        </projections>
        <where start-index="119" stop-index="174" literal-stop-index="175">
            <expr>
                <binary-operation-expression start-index="125" stop-index="174" literal-stop-index="175">
                    <left>
                        <in-expression start-index="125" stop-index="143">
                            <not>false</not>
                            <left>
                                <column name="user_id" start-index="125" stop-index="133">
                                    <owner name="o" start-index="125" stop-index="125"/>
                                </column>
                            </left>
                            <right>
                                <list-expression start-index="138" stop-index="143">
                                    <items>
                                        <literal-expression value="1" start-index="139" stop-index="139"/>
                                        <parameter-marker-expression parameter-index="0" start-index="139"
                                                                     stop-index="139"/>
                                    </items>
                                    <items>
                                        <literal-expression value="2" start-index="142" stop-index="142"/>
                                        <parameter-marker-expression parameter-index="1" start-index="142"
                                                                     stop-index="142"/>
                                    </items>
                                </list-expression>
                            </right>
                        </in-expression>
                    </left>
                    <operator>AND</operator>
                    <right>
                        <between-expression start-index="149" stop-index="174" literal-stop-index="175">
                            <not>false</not>
                            <left>
                                <column name="order_id" start-index="149" stop-index="158">
                                    <owner name="o" start-index="149" stop-index="149"/>
                                </column>
                            </left>
                            <between-expr>
                                <literal-expression value="9" start-index="168" stop-index="168"/>
                                <parameter-marker-expression parameter-index="2" start-index="168" stop-index="168"/>
                            </between-expr>
                            <and-expr>
                                <literal-expression value="10" start-index="174" stop-index="175"/>
                                <parameter-marker-expression parameter-index="3" start-index="174" stop-index="174"/>
                            </and-expr>
                        </between-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_count_with_escape_character">
        <from>
            <simple-table name="t_order" start-index="46" stop-index="52"/>
        </from>
        <projections start-index="7" stop-index="39">
            <aggregation-projection type="COUNT" expression="COUNT(`order_id`)" alias="orders_count" start-index="7"
                                    stop-index="23"/>
        </projections>
    </select>

    <select sql-case-id="select_count_with_sample_clause">
        <from>
            <simple-table name="orders" start-index="26" stop-index="31"/>
        </from>
        <projections start-index="7" stop-index="19">
            <expression-projection text="COUNT(*) * 10" start-index="7" stop-index="19"/>
        </projections>
    </select>

    <select sql-case-id="select_count_with_sample_seed_clause">
        <from>
            <simple-table name="orders" start-index="26" stop-index="31"/>
        </from>
        <projections start-index="7" stop-index="19">
            <expression-projection text="COUNT(*) * 10" start-index="7" stop-index="19"/>
        </projections>
    </select>

    <select sql-case-id="select_count_with_in_clause" parameters="1, 2">
        <from>
            <simple-table name="t_order" start-index="21" stop-index="27"/>
        </from>
        <projections start-index="7" stop-index="14">
            <aggregation-projection type="COUNT" expression="COUNT(*)" start-index="7" stop-index="14"/>
        </projections>
        <where start-index="29" stop-index="54">
            <expr>
                <in-expression start-index="35" stop-index="54">
                    <not>false</not>
                    <left>
                        <column name="last_value" start-index="35" stop-index="44"/>
                    </left>
                    <right>
                        <list-expression start-index="49" stop-index="54">
                            <items>
                                <literal-expression value="1" start-index="50" stop-index="50"/>
                                <parameter-marker-expression parameter-index="0" start-index="50" stop-index="50"/>
                            </items>
                            <items>
                                <literal-expression value="2" start-index="53" stop-index="53"/>
                                <parameter-marker-expression parameter-index="1" start-index="53" stop-index="53"/>
                            </items>
                        </list-expression>
                    </right>
                </in-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_count_with_not_in_clause" parameters="1, 2, 7, 9">
        <from>
            <simple-table name="t_order" start-index="21" stop-index="27"/>
        </from>
        <projections start-index="7" stop-index="14">
            <aggregation-projection type="COUNT" expression="COUNT(*)" start-index="7" stop-index="14"/>
        </projections>
        <where start-index="29" stop-index="81">
            <expr>
                <binary-operation-expression start-index="35" stop-index="81">
                    <left>
                        <in-expression start-index="35" stop-index="52">
                            <not>false</not>
                            <left>
                                <column name="category" start-index="35" stop-index="42"/>
                            </left>
                            <right>
                                <list-expression start-index="47" stop-index="52">
                                    <items>
                                        <literal-expression value="1" start-index="48" stop-index="48"/>
                                        <parameter-marker-expression parameter-index="0" start-index="48"
                                                                     stop-index="48"/>
                                    </items>
                                    <items>
                                        <literal-expression value="2" start-index="51" stop-index="51"/>
                                        <parameter-marker-expression parameter-index="1" start-index="51"
                                                                     stop-index="51"/>
                                    </items>
                                </list-expression>
                            </right>
                        </in-expression>
                    </left>
                    <operator>AND</operator>
                    <right>
                        <in-expression start-index="58" stop-index="81">
                            <not>true</not>
                            <left>
                                <column name="last_value" start-index="58" stop-index="67"/>
                            </left>
                            <right>
                                <list-expression start-index="76" stop-index="81">
                                    <items>
                                        <literal-expression value="7" start-index="77" stop-index="77"/>
                                        <parameter-marker-expression parameter-index="2" start-index="77"
                                                                     stop-index="77"/>
                                    </items>
                                    <items>
                                        <literal-expression value="9" start-index="80" stop-index="80"/>
                                        <parameter-marker-expression parameter-index="3" start-index="80"
                                                                     stop-index="80"/>
                                    </items>
                                </list-expression>
                            </right>
                        </in-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_bit_and">
        <projections start-index="7" stop-index="16">
            <expression-projection start-index="7" stop-index="16" text="BIT_AND(1)">
                <expr>
                    <function function-name="BIT_AND" text="BIT_AND(1)" start-index="7" stop-index="16">
                        <parameter>
                            <literal-expression value="1" start-index="15" stop-index="15"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_bit_or">
        <projections start-index="7" stop-index="15">
            <expression-projection start-index="7" stop-index="15" text="BIT_OR(1)">
                <expr>
                    <function function-name="BIT_OR" text="BIT_OR(1)" start-index="7" stop-index="15">
                        <parameter>
                            <literal-expression value="1" start-index="14" stop-index="14"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_bit_xor">
        <from>
            <simple-table name="t_order" start-index="29" stop-index="35"/>
        </from>
        <projections start-index="7" stop-index="22">
            <aggregation-projection type="BIT_XOR" expression="BIT_XOR(user_id)" start-index="7" stop-index="22"/>
        </projections>
    </select>
    <select sql-case-id="select_approx_count">
        <projections start-index="7" stop-index="92">
            <column-projection name="owner" start-index="7" stop-index="11"/>
            <expression-projection text="approx_count(*)" start-index="14" stop-index="28"/>
            <expression-projection text="approx_rank(partition by owner order by approx_count(*) desc)" start-index="32"
                                   stop-index="92"/>
        </projections>
        <from>
            <simple-table name="t" start-index="99" stop-index="99"/>
        </from>
        <group-by>
            <column-item name="owner" start-index="110" stop-index="114"/>
        </group-by>
        <having start-index="116" stop-index="188">
            <expr>
                <binary-operation-expression start-index="123" stop-index="188">
                    <left>
                        <function function-name="approx_rank" start-index="123" stop-index="183"
                                  text="approx_rank(partition by owner order by approx_count(*) desc)"/>
                    </left>
                    <operator>&lt;=</operator>
                    <right>
                        <literal-expression start-index="188" stop-index="188" value="1"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </having>
        <order-by>
            <index-item index="1" start-index="199" stop-index="199"/>
        </order-by>
    </select>
    <select sql-case-id="select_group_concat">
        <projections start-index="7" stop-index="51">
            <aggregation-projection type="GROUP_CONCAT" expression="GROUP_CONCAT(user_id)" alias="user_id_group_concat"
                                    start-index="7" stop-index="27">
                <parameter>
                    <column name="user_id" start-index="20" stop-index="26"/>
                </parameter>
            </aggregation-projection>
        </projections>
        <from>
            <simple-table name="t_order" start-index="58" stop-index="64"/>
        </from>
    </select>
    <select sql-case-id="select_group_concat_with_distinct_with_separator">
        <projections start-index="7" stop-index="74">
            <aggregation-distinct-projection type="GROUP_CONCAT"
                                             expression="GROUP_CONCAT(distinct user_id SEPARATOR ' ')" separator=" "
                                             distinct-inner-expression="user_id" alias="user_id_group_concat"
                                             start-index="7" stop-index="50">
                <parameter>
                    <column name="user_id" start-index="29" stop-index="35"/>
                </parameter>
            </aggregation-distinct-projection>
        </projections>
        <from>
            <simple-table name="t_order" start-index="81" stop-index="87"/>
        </from>
    </select>
</sql-parser-test-cases>
