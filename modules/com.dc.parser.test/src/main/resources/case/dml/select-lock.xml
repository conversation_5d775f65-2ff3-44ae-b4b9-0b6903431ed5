<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <select sql-case-id="select_lock_with_lock_in" parameters="1">
        <where start-index="22" stop-index="39">
            <expr>
                <binary-operation-expression start-index="28" stop-index="39">
                    <left>
                        <column name="order_id" start-index="28" stop-index="35"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="1" start-index="39" stop-index="39"/>
                        <parameter-marker-expression parameter-index="0" start-index="39" stop-index="39"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <lock start-index="41" stop-index="58"/>
    </select>

    <select sql-case-id="select_lock_with_for_update" parameters="1">
        <where start-index="22" stop-index="39">
            <expr>
                <binary-operation-expression start-index="28" stop-index="39">
                    <left>
                        <column name="order_id" start-index="28" stop-index="35"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="1" start-index="39" stop-index="39"/>
                        <parameter-marker-expression parameter-index="0" start-index="39" stop-index="39"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <lock start-index="41" stop-index="50"/>
    </select>

    <select sql-case-id="select_lock_with_for_share" parameters="1">
        <where start-index="22" stop-index="39">
            <expr>
                <binary-operation-expression start-index="28" stop-index="39">
                    <left>
                        <column name="order_id" start-index="28" stop-index="35"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="1" start-index="39" stop-index="39"/>
                        <parameter-marker-expression parameter-index="0" start-index="39" stop-index="39"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <lock start-index="41" stop-index="49"/>
    </select>

    <select sql-case-id="select_lock_with_nowait" parameters="1">
        <where start-index="22" stop-index="39">
            <expr>
                <binary-operation-expression start-index="28" stop-index="39">
                    <left>
                        <column name="order_id" start-index="28" stop-index="35"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="1" start-index="39" stop-index="39"/>
                        <parameter-marker-expression parameter-index="0" start-index="39" stop-index="39"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <lock start-index="41" stop-index="57"/>
    </select>

    <select sql-case-id="select_lock_with_skip_locked" parameters="1">
        <where start-index="22" stop-index="39">
            <expr>
                <binary-operation-expression start-index="28" stop-index="39">
                    <left>
                        <column name="order_id" start-index="28" stop-index="35"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="1" start-index="39" stop-index="39"/>
                        <parameter-marker-expression parameter-index="0" start-index="39" stop-index="39"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <lock start-index="41" stop-index="62"/>
    </select>

    <select sql-case-id="select_lock_with_of" parameters="1">
        <where start-index="36" stop-index="106">
            <expr>
                <binary-operation-expression start-index="42" stop-index="106">
                    <left>
                        <binary-operation-expression start-index="42" stop-index="81">
                            <left>
                                <column name="order_id" start-index="42" stop-index="57">
                                    <owner name="t_order" start-index="42" stop-index="48"/>
                                </column>
                            </left>
                            <operator>=</operator>
                            <right>
                                <column name="order_id" start-index="61" stop-index="81">
                                    <owner name="t_order_item" start-index="61" stop-index="72"/>
                                </column>
                            </right>
                        </binary-operation-expression>
                    </left>
                    <operator>AND</operator>
                    <right>
                        <binary-operation-expression start-index="87" stop-index="106">
                            <left>
                                <column name="order_id" start-index="87" stop-index="102">
                                    <owner name="t_order" start-index="87" stop-index="93"/>
                                </column>
                            </left>
                            <operator>=</operator>
                            <right>
                                <literal-expression value="1" start-index="106" stop-index="106"/>
                                <parameter-marker-expression parameter-index="0" start-index="106" stop-index="106"/>
                            </right>
                        </binary-operation-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
        <from start-index="14" stop-index="34">
            <join-table start-index="14" stop-index="34" join-type="COMMA">
                <left start-index="14" stop-index="20">
                    <simple-table name="t_order" start-index="14" stop-index="20"/>
                </left>
                <right start-index="23" stop-index="34">
                    <simple-table name="t_order_item" start-index="23" stop-index="34"/>
                </right>
            </join-table>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <lock start-index="108" stop-index="154">
            <table name="t_order" start-index="122" stop-index="128"/>
            <table name="t_order_item" start-index="143" stop-index="154"/>
        </lock>
    </select>

    <select sql-case-id="select_lock_with_of_multi_tables" parameters="1">
        <where start-index="44" stop-index="151">
            <expr>
                <binary-operation-expression start-index="50" stop-index="151">
                    <left>
                        <binary-operation-expression start-index="50" stop-index="126">
                            <left>
                                <binary-operation-expression start-index="50" stop-index="89">
                                    <left>
                                        <column name="order_id" start-index="50" stop-index="65">
                                            <owner name="t_order" start-index="50" stop-index="56"/>
                                        </column>
                                    </left>
                                    <operator>=</operator>
                                    <right>
                                        <column name="order_id" start-index="69" stop-index="89">
                                            <owner name="t_order_item" start-index="69" stop-index="80"/>
                                        </column>
                                    </right>
                                </binary-operation-expression>
                            </left>
                            <operator>AND</operator>
                            <right>
                                <binary-operation-expression start-index="95" stop-index="126">
                                    <left>
                                        <column name="user_id" start-index="95" stop-index="109">
                                            <owner name="t_order" start-index="95" stop-index="101"/>
                                        </column>
                                    </left>
                                    <operator>=</operator>
                                    <right>
                                        <column name="user_id" start-index="113" stop-index="126">
                                            <owner name="t_user" start-index="113" stop-index="118"/>
                                        </column>
                                    </right>
                                </binary-operation-expression>
                            </right>
                        </binary-operation-expression>
                    </left>
                    <operator>AND</operator>
                    <right>
                        <binary-operation-expression start-index="132" stop-index="151">
                            <left>
                                <column name="order_id" start-index="132" stop-index="147">
                                    <owner name="t_order" start-index="132" stop-index="138"/>
                                </column>
                            </left>
                            <operator>=</operator>
                            <right>
                                <literal-expression value="1" start-index="151" stop-index="151"/>
                                <parameter-marker-expression parameter-index="0" start-index="151" stop-index="151"/>
                            </right>
                        </binary-operation-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
        <from start-index="14" stop-index="42">
            <join-table start-index="14" stop-index="42" join-type="COMMA">
                <left start-index="14" stop-index="34">
                    <join-table start-index="14" stop-index="34" join-type="COMMA">
                        <left start-index="14" stop-index="20">
                            <simple-table name="t_order" start-index="14" stop-index="20"/>
                        </left>
                        <right start-index="23" stop-index="34">
                            <simple-table name="t_order_item" start-index="23" stop-index="34"/>
                        </right>
                    </join-table>
                </left>
                <right start-index="37" stop-index="42">
                    <simple-table name="t_user" start-index="37" stop-index="42"/>
                </right>
            </join-table>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <lock start-index="153" stop-index="207">
            <table name="t_order" start-index="167" stop-index="173"/>
            <table name="t_order_item" start-index="176" stop-index="187"/>
            <table name="t_user" start-index="202" stop-index="207"/>
        </lock>
    </select>

    <select sql-case-id="select_lock_with_for_update_column">
        <projections start-index="7" stop-index="14">
            <column-projection name="order_id" start-index="7" stop-index="14"/>
        </projections>
        <from start-index="21" stop-index="27">
            <simple-table name="t_order" start-index="21" stop-index="27"/>
        </from>
        <lock start-index="29" stop-index="50">
            <column name="order_id" start-index="43" stop-index="50"/>
        </lock>
    </select>

    <select sql-case-id="select_lock_with_for_update_table_column">
        <projections start-index="7" stop-index="14">
            <column-projection name="order_id" start-index="7" stop-index="14"/>
        </projections>
        <from start-index="21" stop-index="27">
            <simple-table name="t_order" start-index="21" stop-index="27"/>
        </from>
        <lock start-index="29" stop-index="58">
            <table name="t_order" start-index="43" stop-index="49"/>
            <column name="order_id" start-index="51" stop-index="58"/>
        </lock>
    </select>
</sql-parser-test-cases>
