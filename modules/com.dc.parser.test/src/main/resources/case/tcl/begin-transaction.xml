<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <begin-transaction sql-case-id="begin"/>
    <begin-transaction sql-case-id="begin_work"/>
    <begin-transaction sql-case-id="begin_transaction"/>
    <begin-transaction sql-case-id="begin_with_name"/>
    <begin-transaction sql-case-id="begin_with_variable_name"/>
    <begin-transaction sql-case-id="begin_read_committed"/>
    <begin-transaction sql-case-id="begin_read_only"/>
    <begin-transaction sql-case-id="begin_with_transaction_mode"/>
    <begin-transaction sql-case-id="begin_with_mark_transaction"/>
    <begin-transaction sql-case-id="begin_distributed_transaction"/>
</sql-parser-test-cases>
