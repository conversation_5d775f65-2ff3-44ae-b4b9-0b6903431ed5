package com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dal;

import lombok.Getter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.SQLParserTestCase;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.table.ExpectedSimpleTable;

import javax.xml.bind.annotation.XmlElement;
import java.util.LinkedList;
import java.util.List;

/**
 * Checksum table statement test case.
 */
@Getter
public final class ChecksumTableStatementTestCase extends SQLParserTestCase {

    @XmlElement(name = "table")
    private final List<ExpectedSimpleTable> tables = new LinkedList<>();
}
