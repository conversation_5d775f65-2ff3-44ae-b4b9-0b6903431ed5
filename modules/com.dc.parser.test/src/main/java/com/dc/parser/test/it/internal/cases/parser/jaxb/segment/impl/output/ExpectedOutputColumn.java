package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.output;

import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedSQLSegment;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.projection.ExpectedProjection;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.projection.impl.expression.ExpectedExpressionProjection;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlElement;
import java.util.LinkedList;
import java.util.List;

/**
 * Expected output column.
 */
@Getter
@Setter
public final class ExpectedOutputColumn extends AbstractExpectedSQLSegment {

    @XmlElement(name = "column-projection")
    private final List<ExpectedProjection> columnProjections = new LinkedList<>();

    @XmlElement(name = "expression-projection")
    private final List<ExpectedExpressionProjection> expressionProjections = new LinkedList<>();
}
