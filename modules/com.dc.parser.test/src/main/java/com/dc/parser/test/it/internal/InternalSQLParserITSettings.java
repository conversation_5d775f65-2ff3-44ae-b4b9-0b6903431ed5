package com.dc.parser.test.it.internal;

import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * Internal SQL parser integrate test settings.
 */
@Inherited
@Retention(RetentionPolicy.RUNTIME)
public @interface InternalSQLParserITSettings {

    /**
     * Get to be tested database types.
     *
     * @return to be tested database types
     */
    String[] value();
}
