package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.expr;

import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedSQLSegment;
import lombok.Getter;

import javax.xml.bind.annotation.XmlElement;
import java.util.LinkedList;
import java.util.List;

/**
 * Expected list expression.
 */
@Getter
public final class ExpectedListExpression extends AbstractExpectedSQLSegment implements ExpectedExpressionSegment {

    @XmlElement
    private final List<ExpectedExpression> items = new LinkedList<>();
}
