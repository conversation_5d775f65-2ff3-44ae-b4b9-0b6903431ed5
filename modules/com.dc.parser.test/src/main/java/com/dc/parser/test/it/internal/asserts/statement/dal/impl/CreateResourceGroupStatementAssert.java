package com.dc.parser.test.it.internal.asserts.statement.dal.impl;

import com.dc.parser.model.statement.dal.CreateResourceGroupStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dal.CreateResourceGroupStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * Create resource group statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class CreateResourceGroupStatementAssert {

    /**
     * Assert create resource group statement is correct with expected parser result.
     *
     * @param assertContext assert context
     * @param actual        actual create resource group statement
     * @param expected      expected create resource group statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final CreateResourceGroupStatement actual, final CreateResourceGroupStatementTestCase expected) {
        assertNotNull(expected.getGroup(), assertContext.getText("expected create resource group should be not null"));
        assertThat(assertContext.getText("group name does not match: "), actual.getGroupName(), is(expected.getGroup().getName()));
    }
}
