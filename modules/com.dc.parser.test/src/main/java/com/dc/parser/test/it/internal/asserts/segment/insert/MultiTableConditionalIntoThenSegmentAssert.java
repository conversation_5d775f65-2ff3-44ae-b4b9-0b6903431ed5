package com.dc.parser.test.it.internal.asserts.segment.insert;

import com.dc.parser.model.segment.dml.table.MultiTableConditionalIntoThenSegment;
import com.dc.parser.model.statement.dml.InsertStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.segment.SQLSegmentAssert;
import com.dc.parser.test.it.internal.asserts.statement.dml.impl.InsertStatementAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.insert.ExpectedMultiTableConditionalIntoThenClause;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;

/**
 * Multi table conditional into then segment assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class MultiTableConditionalIntoThenSegmentAssert {

    /**
     * Assert actual multi table conditional into then segment is correct with expected multi table conditional into then segment.
     *
     * @param assertContext assert context
     * @param actual        actual multi table conditional into then segment
     * @param expected      expected multi table conditional into then segment
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final MultiTableConditionalIntoThenSegment actual, final ExpectedMultiTableConditionalIntoThenClause expected) {
        assertThat(assertContext.getText("Multi table conditional into then segment' insert values size assertion error: "), actual.getInsertStatements().size(),
                is(expected.getInsertTestCases().size()));
        int count = 0;
        for (InsertStatement each : actual.getInsertStatements()) {
            InsertStatementAssert.assertIs(assertContext, each, expected.getInsertTestCases().get(count));
            SQLSegmentAssert.assertIs(assertContext, actual, expected);
            count++;
        }
    }
}
