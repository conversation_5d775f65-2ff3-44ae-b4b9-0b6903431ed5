package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.like;

import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedSQLSegment;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;

/**
 * Expected like clause.
 */
@XmlAccessorType(XmlAccessType.FIELD)
@Getter
@Setter
public final class ExpectedLikeClause extends AbstractExpectedSQLSegment {

    @XmlAttribute
    private String pattern;
}
