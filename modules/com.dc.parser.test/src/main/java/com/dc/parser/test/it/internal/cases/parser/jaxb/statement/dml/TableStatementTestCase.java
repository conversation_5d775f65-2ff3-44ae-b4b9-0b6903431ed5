package com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dml;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.SQLParserTestCase;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.column.ExpectedColumn;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.limit.ExpectedLimitClause;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.table.ExpectedSimpleTable;

import javax.xml.bind.annotation.XmlElement;

/**
 * Table statement test case.
 */
@Getter
@Setter
public final class TableStatementTestCase extends SQLParserTestCase {

    @XmlElement(name = "simple-table")
    private ExpectedSimpleTable simpleTable;

    @XmlElement
    private ExpectedColumn column;

    @XmlElement(name = "limit")
    private ExpectedLimitClause limitClause;
}
