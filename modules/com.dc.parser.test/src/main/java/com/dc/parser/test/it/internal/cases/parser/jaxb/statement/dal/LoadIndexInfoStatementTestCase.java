package com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dal;

import lombok.Getter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.SQLParserTestCase;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.index.ExpectedLoadTableIndex;

import javax.xml.bind.annotation.XmlElement;
import java.util.LinkedList;
import java.util.List;

/**
 * Load index info statement test case.
 */
@Getter
public final class LoadIndexInfoStatementTestCase extends SQLParserTestCase {

    @XmlElement(name = "table-index")
    private final List<ExpectedLoadTableIndex> tableIndexes = new LinkedList<>();
}
