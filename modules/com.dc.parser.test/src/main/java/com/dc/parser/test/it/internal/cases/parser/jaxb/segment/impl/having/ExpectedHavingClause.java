package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.having;

import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedSQLSegment;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.expr.ExpectedExpression;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

/**
 * Expected having clause.
 */
@XmlAccessorType(XmlAccessType.FIELD)
@Getter
@Setter
public final class ExpectedHavingClause extends AbstractExpectedSQLSegment {

    @XmlElement
    private ExpectedExpression expr;
}
