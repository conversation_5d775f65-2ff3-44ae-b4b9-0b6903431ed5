package com.dc.parser.test.it.loader;

import com.parser.test.loader.TestParameterLoadTemplate;

import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * External SQL case test settings.
 */
@Inherited
@Retention(RetentionPolicy.RUNTIME)
public @interface ExternalCaseSettings {

    /**
     * Get to be tested database types.
     *
     * @return to be tested database types
     */
    String value();

    /**
     * Get test case URL.
     *
     * @return test case URL
     */
    String caseURL();

    /**
     * Get test case result URL.
     *
     * @return test case result URL
     */
    String resultURL();

    /**
     * Report type.
     *
     * @return get report type
     */
    String reportType() default "CSV";

    /**
     * Get test parameter load template.
     *
     * @return test parameter load template
     */
    Class<? extends TestParameterLoadTemplate> template();
}
