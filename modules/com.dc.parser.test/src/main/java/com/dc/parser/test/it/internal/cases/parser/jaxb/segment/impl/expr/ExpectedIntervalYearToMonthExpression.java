package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.expr;

import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedSQLSegment;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlElement;

/**
 * Expected interval day to second expression.
 */
@Getter
@Setter
public class ExpectedIntervalYearToMonthExpression extends AbstractExpectedSQLSegment implements ExpectedExpressionSegment {

    @XmlElement
    private String year;

    @XmlElement
    private String to;

    @XmlElement
    private String month;

    @XmlElement(name = "leading-field-precision")
    private Integer leadingFieldPrecision;
}
