package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.orderby.item;

import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedDelimiterSQLSegment;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAttribute;

/**
 * Expected order by item.
 */
@Getter
@Setter
public abstract class ExpectedOrderByItem extends AbstractExpectedDelimiterSQLSegment {

    @XmlAttribute(name = "order-direction")
    private String orderDirection = "ASC";

    @XmlAttribute(name = "nulls-order-type")
    private String nullsOrderType;
}
