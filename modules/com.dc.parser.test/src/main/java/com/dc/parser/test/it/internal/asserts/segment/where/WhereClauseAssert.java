package com.dc.parser.test.it.internal.asserts.segment.where;

import com.dc.parser.model.segment.dml.predicate.WhereSegment;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.segment.SQLSegmentAssert;
import com.dc.parser.test.it.internal.asserts.segment.expression.ExpressionAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.where.ExpectedWhereClause;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * Where clause assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class WhereClauseAssert {

    /**
     * Assert actual where segment is correct with expected where clause.
     *
     * @param assertContext assert context
     * @param actual        actual where segment
     * @param expected      expected where clause
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final WhereSegment actual, final ExpectedWhereClause expected) {
        ExpressionAssert.assertExpression(assertContext, actual.getExpr(), expected.getExpr());
        SQLSegmentAssert.assertIs(assertContext, actual, expected);
    }
}
