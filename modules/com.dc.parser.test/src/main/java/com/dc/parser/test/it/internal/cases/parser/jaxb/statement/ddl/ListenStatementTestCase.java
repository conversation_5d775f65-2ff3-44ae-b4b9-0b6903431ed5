package com.dc.parser.test.it.internal.cases.parser.jaxb.statement.ddl;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.SQLParserTestCase;

import javax.xml.bind.annotation.XmlAttribute;

/**
 * Listen statement test case.
 */
@Getter
@Setter
public final class ListenStatementTestCase extends SQLParserTestCase {

    @XmlAttribute(name = "channel-name")
    private String channelName;
}
