package com.dc.parser.test.it.internal.asserts.statement.dal.impl;

import com.dc.parser.model.statement.dal.ShowSlaveHostsStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dal.ShowSlaveHostsStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * Show slave host statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ShowSlaveHostsStatementAssert {

    /**
     * Assert show slave host statement is correct with expected show slave host statement test case.
     *
     * @param assertContext assert context
     * @param actual        actual show slave host statement
     * @param expected      expected show slave host statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final ShowSlaveHostsStatement actual, final ShowSlaveHostsStatementTestCase expected) {
    }
}
