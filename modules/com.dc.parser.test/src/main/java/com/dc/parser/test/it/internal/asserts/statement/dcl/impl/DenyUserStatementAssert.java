package com.dc.parser.test.it.internal.asserts.statement.dcl.impl;

import com.dc.parser.model.statement.dcl.DenyUserStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.segment.column.ColumnAssert;
import com.dc.parser.test.it.internal.asserts.segment.table.TableAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dcl.DenyUserStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Deny user statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class DenyUserStatementAssert {

    /**
     * Assert deny user statement is correct with expected parser result.
     *
     * @param assertContext assert context
     * @param actual        actual deny user statement
     * @param expected      expected deny user statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final DenyUserStatement actual, final DenyUserStatementTestCase expected) {
        assertTable(assertContext, actual, expected);
        assertColumns(assertContext, actual, expected);
    }

    private static void assertTable(final SQLCaseAssertContext assertContext, final DenyUserStatement actual, final DenyUserStatementTestCase expected) {
        if (null == expected.getTable()) {
            assertNull(actual.getTable(), assertContext.getText("Actual table segment should not exist."));
        } else {
            assertNotNull(actual.getTable(), assertContext.getText("Actual table segment should exist."));
            TableAssert.assertIs(assertContext, actual.getTable(), expected.getTable());
        }
    }

    private static void assertColumns(final SQLCaseAssertContext assertContext, final DenyUserStatement actual, final DenyUserStatementTestCase expected) {
        if (expected.getColumns().isEmpty()) {
            assertTrue(actual.getColumns().isEmpty(), assertContext.getText("Actual columns segments should not exist."));
        } else {
            ColumnAssert.assertIs(assertContext, actual.getColumns(), expected.getColumns());
        }
    }
}
