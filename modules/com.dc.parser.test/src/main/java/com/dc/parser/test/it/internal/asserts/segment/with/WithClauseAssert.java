package com.dc.parser.test.it.internal.asserts.segment.with;

import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.segment.dml.expr.complex.CommonTableExpressionSegment;
import com.dc.parser.model.segment.generic.WithSegment;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.segment.SQLSegmentAssert;
import com.dc.parser.test.it.internal.asserts.segment.column.ColumnAssert;
import com.dc.parser.test.it.internal.asserts.segment.expression.ExpressionAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.with.ExpectedCommonTableExpressionClause;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.with.ExpectedWithClause;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * With clause assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class WithClauseAssert {

    /**
     * Assert actual with segment is correct with expected with clause.
     *
     * @param assertContext assert context
     * @param actual        actual with segment
     * @param expected      expected with clause
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final WithSegment actual, final ExpectedWithClause expected) {
        assertNotNull(expected, assertContext.getText("With clause should exist."));
        assertThat(assertContext.getText("With clause common table expressions size assertion error: "),
                actual.getCommonTableExpressions().size(), is(expected.getCommonTableExpressions().size()));
        int count = 0;
        for (CommonTableExpressionSegment each : actual.getCommonTableExpressions()) {
            assertCommonTableExpressionSegment(assertContext, each, expected.getCommonTableExpressions().get(count));
            count++;
        }
        SQLSegmentAssert.assertIs(assertContext, actual, expected);
    }

    private static void assertCommonTableExpressionSegment(final SQLCaseAssertContext assertContext, final CommonTableExpressionSegment actual, final ExpectedCommonTableExpressionClause expected) {
        if (!expected.getColumns().isEmpty()) {
            assertThat(assertContext.getText("Common table expression column size assertion error: "), actual.getColumns().size(), is(expected.getColumns().size()));
        }
        assertThat(assertContext.getText("Common table expression name assertion error: "), actual.getAliasName().orElse(null), is(expected.getName()));
        int count = 0;
        for (ColumnSegment each : actual.getColumns()) {
            ColumnAssert.assertIs(assertContext, each, expected.getColumns().get(count));
            count++;
        }
        if (null != expected.getSubquery()) {
            ExpressionAssert.assertSubquery(assertContext, actual.getSubquery(), expected.getSubquery());
        }
        SQLSegmentAssert.assertIs(assertContext, actual, expected);
    }
}
