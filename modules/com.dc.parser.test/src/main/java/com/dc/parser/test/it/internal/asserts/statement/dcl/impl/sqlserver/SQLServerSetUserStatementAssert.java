package com.dc.parser.test.it.internal.asserts.statement.dcl.impl.sqlserver;

import com.dc.parser.model.statement.dcl.SetUserStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.segment.SQLSegmentAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dcl.SetUserStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;

/**
 * SQLServer set user statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class SQLServerSetUserStatementAssert {

    /**
     * Assert SQLServer set user statement is correct with expected parser result.
     *
     * @param assertContext assert context
     * @param actual        actual SQLServer set user statement
     * @param expected      expected set user statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final SetUserStatement actual, final SetUserStatementTestCase expected) {
        if (null != expected.getUser()) {
            assertThat(assertContext.getText("Actual user name does not match: "), actual.getUser().getUser(), is(expected.getUser().getName()));
            SQLSegmentAssert.assertIs(assertContext, actual.getUser(), expected.getUser());
        }
    }
}
