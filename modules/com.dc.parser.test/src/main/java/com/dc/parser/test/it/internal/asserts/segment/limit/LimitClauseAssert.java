package com.dc.parser.test.it.internal.asserts.segment.limit;

import com.dc.parser.model.segment.dml.pagination.NumberLiteralPaginationValueSegment;
import com.dc.parser.model.segment.dml.pagination.PaginationValueSegment;
import com.dc.parser.model.segment.dml.pagination.ParameterMarkerPaginationValueSegment;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.segment.SQLSegmentAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.limit.ExpectedPaginationValue;
import com.dc.parser.test.it.internal.cases.sql.type.SQLCaseType;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.jupiter.api.Assertions.assertNull;

/**
 * Limit clause assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class LimitClauseAssert {

    /**
     * Assert actual offset segment is correct with expected offset.
     *
     * @param assertContext assert context
     * @param actual        actual offset
     * @param expected      expected offset
     */
    public static void assertOffset(final SQLCaseAssertContext assertContext, final PaginationValueSegment actual, final ExpectedPaginationValue expected) {
        if (null == actual) {
            assertNull(expected, assertContext.getText("Offset should not exist."));
            return;
        }
        if (actual instanceof ParameterMarkerPaginationValueSegment) {
            assertThat(assertContext.getText("Offset index assertion error: "),
                    ((ParameterMarkerPaginationValueSegment) actual).getParameterIndex(), is(expected.getParameterIndex()));
        } else {
            assertThat(assertContext.getText("Offset value assertion error: "), ((NumberLiteralPaginationValueSegment) actual).getValue(), is(expected.getValue()));
        }
        SQLSegmentAssert.assertIs(assertContext, actual, expected);
    }

    /**
     * Assert actual row count segment is correct with expected row count.
     *
     * @param assertContext assert context
     * @param actual        actual row count
     * @param expected      expected row count
     */
    public static void assertRowCount(final SQLCaseAssertContext assertContext, final PaginationValueSegment actual, final ExpectedPaginationValue expected) {
        if (null == actual) {
            assertNull(expected, assertContext.getText("Row count should not exist."));
            return;
        }
        if (SQLCaseType.PLACEHOLDER == assertContext.getCaseType()) {
            assertThat(assertContext.getText("Row count index assertion error: "),
                    ((ParameterMarkerPaginationValueSegment) actual).getParameterIndex(), is(expected.getParameterIndex()));
        } else {
            assertThat(assertContext.getText("Row count value assertion error: "), ((NumberLiteralPaginationValueSegment) actual).getValue(), is(expected.getValue()));
        }
        SQLSegmentAssert.assertIs(assertContext, actual, expected);
    }
}
