package com.dc.parser.test.it.internal.asserts.statement.ddl.impl;

import com.dc.parser.ext.oracle.statement.ddl.OracleDropIndexStatement;
import com.dc.parser.model.segment.ddl.index.IndexSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.ddl.DropIndexStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.segment.index.IndexAssert;
import com.dc.parser.test.it.internal.asserts.segment.table.TableAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.ddl.DropIndexStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.Optional;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Drop index statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class DropIndexStatementAssert {

    /**
     * Assert drop index statement is correct with expected parser result.
     *
     * @param assertContext assert context
     * @param actual        actual drop index statement
     * @param expected      expected drop index statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final DropIndexStatement actual, final DropIndexStatementTestCase expected) {
        assertTables(assertContext, actual, expected);
        assertIndex(assertContext, actual, expected);
        assertLockTable(assertContext, actual, expected);
        assertAlgorithm(assertContext, actual, expected);
    }

    private static void assertTables(final SQLCaseAssertContext assertContext, final DropIndexStatement actual, final DropIndexStatementTestCase expected) {
        Optional<SimpleTableSegment> simpleTableSegment = actual.getSimpleTable();
        if (null == expected.getTable()) {
            assertFalse(simpleTableSegment.isPresent(), assertContext.getText("Actual table segment should not exist."));
        } else {
            assertTrue(simpleTableSegment.isPresent(), assertContext.getText("Actual table segment should exist."));
            TableAssert.assertIs(assertContext, simpleTableSegment.get(), expected.getTable());
        }
    }

    private static void assertIndex(final SQLCaseAssertContext assertContext, final DropIndexStatement actual, final DropIndexStatementTestCase expected) {
        // TODO should assert index for all databases(mysql and sqlserver do not parse index right now)
        if (actual instanceof OracleDropIndexStatement) {
            int count = 0;
            for (IndexSegment each : actual.getIndexes()) {
                IndexAssert.assertIs(assertContext, each, expected.getIndexes().get(count));
                count++;
            }
        }
    }

    private static void assertLockTable(final SQLCaseAssertContext assertContext, final DropIndexStatement actual, final DropIndexStatementTestCase expected) {
        if (null == expected.getLockOption()) {
            assertFalse(actual.getLockTable().isPresent(), assertContext.getText("Actual lock table segments should not exist."));
        } else {
            assertTrue(actual.getLockTable().isPresent(), assertContext.getText("Actual lock table segments should exist."));
            assertThat(assertContext.getText(String.format("`%s`'s lock table assertion error: ", actual.getClass().getSimpleName())),
                    actual.getLockTable().get().getLockTableOption().name(), is(expected.getLockOption().getType()));
        }
    }

    private static void assertAlgorithm(final SQLCaseAssertContext assertContext, final DropIndexStatement actual, final DropIndexStatementTestCase expected) {
        if (null == expected.getAlgorithmOption()) {
            assertFalse(actual.getAlgorithmType().isPresent(), assertContext.getText("Actual algorithm segments should not exist."));
        } else {
            assertTrue(actual.getAlgorithmType().isPresent(), assertContext.getText("Actual algorithm segments should exist."));
            assertThat(assertContext.getText(String.format("`%s`'s algorithm assertion error: ", actual.getClass().getSimpleName())),
                    actual.getAlgorithmType().get().getAlgorithmOption().name(), is(expected.getAlgorithmOption().getType()));
        }
    }
}
