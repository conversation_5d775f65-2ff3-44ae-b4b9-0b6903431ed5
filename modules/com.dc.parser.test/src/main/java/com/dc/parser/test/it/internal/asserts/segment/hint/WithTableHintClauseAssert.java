package com.dc.parser.test.it.internal.asserts.segment.hint;

import com.dc.parser.model.segment.dml.hint.TableHintLimitedSegment;
import com.dc.parser.model.segment.dml.hint.WithTableHintSegment;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.segment.SQLSegmentAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.hint.ExpectedTableHint;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.hint.ExpectedWithTableHintClause;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

/**
 * With table hint clause assert.
 **/
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class WithTableHintClauseAssert {

    /**
     * Assert actual with table hint segment is correct with expected table hint clause.
     *
     * @param assertContext assert context
     * @param actual        actual with table hint segment
     * @param expected      expected with table hint clause
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final WithTableHintSegment actual, final ExpectedWithTableHintClause expected) {
        if (null == expected.getTableHint()) {
            assertThat(assertContext.getText("with table hint clause  assertion error: "), actual.getTableHintLimitedSegments().size(), is(expected.getTableHint().size()));
        } else {
            int count = 0;
            for (TableHintLimitedSegment each : actual.getTableHintLimitedSegments()) {
                assertTableHint(assertContext, each, expected.getTableHint().get(count));
                count++;
            }
        }
        SQLSegmentAssert.assertIs(assertContext, actual, expected);
    }

    /**
     * Assert table hint.
     *
     * @param assertContext assert context
     * @param actual        actual table hint segment
     * @param expected      expected table hint
     */
    public static void assertTableHint(final SQLCaseAssertContext assertContext, final TableHintLimitedSegment actual, final ExpectedTableHint expected) {
        if (null == expected) {
            assertNull(actual, assertContext.getText("Actual table hint should not exist."));
        } else {
            assertNotNull(actual, assertContext.getText("Actual table hint should exist."));
            assertThat(assertContext.getText("table hint value assertion error."), actual.getValue(), is(expected.getValue()));
            SQLSegmentAssert.assertIs(assertContext, actual, expected);
        }
    }
}
