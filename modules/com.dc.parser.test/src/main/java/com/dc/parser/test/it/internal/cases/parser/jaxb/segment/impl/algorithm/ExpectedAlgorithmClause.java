package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.algorithm;

import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedSQLSegment;
import lombok.Getter;

import javax.xml.bind.annotation.XmlAttribute;

/**
 * Expected algorithm clause.
 */
@Getter
public final class ExpectedAlgorithmClause extends AbstractExpectedSQLSegment {

    @XmlAttribute
    private String type;
}
