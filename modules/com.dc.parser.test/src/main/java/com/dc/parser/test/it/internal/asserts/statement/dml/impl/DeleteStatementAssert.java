package com.dc.parser.test.it.internal.asserts.statement.dml.impl;

import com.dc.parser.model.segment.dml.order.OrderBySegment;
import com.dc.parser.model.segment.dml.pagination.limit.LimitSegment;
import com.dc.parser.model.segment.generic.OutputSegment;
import com.dc.parser.model.segment.generic.WithSegment;
import com.dc.parser.model.segment.generic.table.DeleteMultiTableSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.segment.generic.table.SubqueryTableSegment;
import com.dc.parser.model.statement.dml.DeleteStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.segment.SQLSegmentAssert;
import com.dc.parser.test.it.internal.asserts.segment.limit.LimitClauseAssert;
import com.dc.parser.test.it.internal.asserts.segment.orderby.OrderByClauseAssert;
import com.dc.parser.test.it.internal.asserts.segment.output.OutputClauseAssert;
import com.dc.parser.test.it.internal.asserts.segment.table.TableAssert;
import com.dc.parser.test.it.internal.asserts.segment.where.WhereClauseAssert;
import com.dc.parser.test.it.internal.asserts.segment.with.WithClauseAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dml.DeleteStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.LinkedList;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Delete statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class DeleteStatementAssert {

    /**
     * Assert delete statement is correct with expected parser result.
     *
     * @param assertContext assert context
     * @param actual        actual delete statement
     * @param expected      expected delete statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final DeleteStatement actual, final DeleteStatementTestCase expected) {
        assertWithClause(assertContext, actual, expected);
        assertTable(assertContext, actual, expected);
        assertOutput(assertContext, actual, expected);
        assertWhereClause(assertContext, actual, expected);
        assertOrderByClause(assertContext, actual, expected);
        assertLimitClause(assertContext, actual, expected);
    }

    private static void assertWithClause(final SQLCaseAssertContext assertContext, final DeleteStatement actual, final DeleteStatementTestCase expected) {
        Optional<WithSegment> withSegment = actual.getWithSegment();
        if (null == expected.getWithClause()) {
            assertFalse(withSegment.isPresent(), assertContext.getText("Actual with segment should not exist."));
        } else {
            assertTrue(withSegment.isPresent(), assertContext.getText("Actual with segment should exist."));
            WithClauseAssert.assertIs(assertContext, withSegment.get(), expected.getWithClause());
        }
    }

    private static void assertTable(final SQLCaseAssertContext assertContext, final DeleteStatement actual, final DeleteStatementTestCase expected) {
        if (null != expected.getTables() && !expected.getTables().isEmpty()) {
            assertNotNull(actual.getTable(), assertContext.getText("Actual table segment should exist."));
            List<SimpleTableSegment> actualTableSegments = new LinkedList<>();
            if (actual.getTable() instanceof SimpleTableSegment) {
                actualTableSegments.add((SimpleTableSegment) actual.getTable());
            } else if (actual.getTable() instanceof DeleteMultiTableSegment) {
                DeleteMultiTableSegment deleteMultiTableSegment = (DeleteMultiTableSegment) actual.getTable();
                actualTableSegments.addAll(deleteMultiTableSegment.getActualDeleteTables());
            }
            TableAssert.assertIs(assertContext, actualTableSegments, expected.getTables());
        } else if (null != expected.getSubqueryTable()) {
            assertNotNull(actual.getTable(), assertContext.getText("Actual subquery table segment should exist."));
            TableAssert.assertIs(assertContext, (SubqueryTableSegment) actual.getTable(), expected.getSubqueryTable());
        } else {
            assertNull(actual.getTable(), assertContext.getText("Actual table should not exist."));
        }
    }

    private static void assertOutput(final SQLCaseAssertContext assertContext, final DeleteStatement actual, final DeleteStatementTestCase expected) {
        Optional<OutputSegment> outputSegment = actual.getOutputSegment();
        if (null == expected.getOutputClause()) {
            assertFalse(outputSegment.isPresent(), assertContext.getText("Actual output segment should not exist."));
        } else {
            assertTrue(outputSegment.isPresent(), assertContext.getText("Actual output segment should exist."));
            OutputClauseAssert.assertIs(assertContext, outputSegment.get(), expected.getOutputClause());
        }
    }

    private static void assertWhereClause(final SQLCaseAssertContext assertContext, final DeleteStatement actual, final DeleteStatementTestCase expected) {
        if (null == expected.getWhereClause()) {
            assertFalse(actual.getWhere().isPresent(), assertContext.getText("Actual where segment should not exist."));
        } else {
            assertTrue(actual.getWhere().isPresent(), assertContext.getText("Actual where segment should exist."));
            WhereClauseAssert.assertIs(assertContext, actual.getWhere().get(), expected.getWhereClause());
        }
    }

    private static void assertOrderByClause(final SQLCaseAssertContext assertContext, final DeleteStatement actual, final DeleteStatementTestCase expected) {
        Optional<OrderBySegment> orderBySegment = actual.getOrderBy();
        if (null == expected.getOrderByClause()) {
            assertFalse(orderBySegment.isPresent(), assertContext.getText("Actual order by segment should not exist."));
        } else {
            assertTrue(orderBySegment.isPresent(), assertContext.getText("Actual order by segment should exist."));
            OrderByClauseAssert.assertIs(assertContext, orderBySegment.get(), expected.getOrderByClause());
        }
    }

    private static void assertLimitClause(final SQLCaseAssertContext assertContext, final DeleteStatement actual, final DeleteStatementTestCase expected) {
        Optional<LimitSegment> limitSegment = actual.getLimit();
        if (null == expected.getLimitClause()) {
            assertFalse(limitSegment.isPresent(), assertContext.getText("Actual limit segment should not exist."));
        } else {
            assertTrue(limitSegment.isPresent(), assertContext.getText("Actual limit segment should exist."));
            LimitClauseAssert.assertRowCount(assertContext, limitSegment.get().getRowCount().orElse(null), expected.getLimitClause().getRowCount());
            SQLSegmentAssert.assertIs(assertContext, limitSegment.get(), expected.getLimitClause());
        }
    }
}
