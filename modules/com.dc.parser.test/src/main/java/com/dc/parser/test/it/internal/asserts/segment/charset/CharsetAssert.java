package com.dc.parser.test.it.internal.asserts.segment.charset;

import com.dc.parser.model.segment.ddl.charset.CharsetNameSegment;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.segment.SQLSegmentAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.charset.ExpectedCharsetName;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

/**
 * Charset assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class CharsetAssert {

    /**
     * Assert actual charset name segment is correct with expected charset name.
     *
     * @param assertContext assert context
     * @param actual        actual charset name segment
     * @param expected      expected charset name
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final CharsetNameSegment actual, final ExpectedCharsetName expected) {
        if (null == expected) {
            assertNull(actual, assertContext.getText("Actual charset name should not exist."));
        } else {
            assertNotNull(actual, assertContext.getText("Actual charset name should exist."));
            assertThat(assertContext.getText("charset name assertion error. "), actual.getName(), is(expected.getName()));
            SQLSegmentAssert.assertIs(assertContext, actual, expected);
        }
    }
}
