package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.insert;

import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedSQLSegment;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dml.InsertStatementTestCase;
import lombok.Getter;

import javax.xml.bind.annotation.XmlElement;
import java.util.LinkedList;
import java.util.List;

/**
 * Expected multi table insert into clause.
 */
@Getter
public final class ExpectedMultiTableInsertIntoClause extends AbstractExpectedSQLSegment {

    @XmlElement(name = "insert-statement")
    private final List<InsertStatementTestCase> insertTestCases = new LinkedList<>();
}
