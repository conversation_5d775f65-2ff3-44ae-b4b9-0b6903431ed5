

package com.dc.summer.ext.vertica.edit;

import com.dc.summer.DBException;
import com.dc.summer.ext.generic.edit.GenericTableColumnManager;
import com.dc.summer.ext.generic.model.GenericTableBase;
import com.dc.summer.model.DBConstants;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.edit.DBECommandContext;
import com.dc.summer.model.edit.DBEObjectRenamer;
import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.impl.edit.DBECommandAbstract;
import com.dc.summer.model.impl.edit.SQLDatabasePersistAction;
import com.dc.summer.model.impl.sql.edit.SQLObjectEditor;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;
import com.dc.summer.ext.generic.model.GenericTableColumn;
import com.dc.utils.CommonUtils;

import java.util.List;
import java.util.Map;

/**
 * Vertica table column manager
 */
public class VerticaTableColumnManager extends GenericTableColumnManager implements DBEObjectRenamer<GenericTableColumn> {

    private final ColumnModifier<GenericTableColumn> VerticaDataTypeModifier = (monitor, column, sql, command) -> {
        sql.append(" SET DATA TYPE ");
        DataTypeModifier.appendModifier(monitor, column, sql, command);
    };

    private final ColumnModifier<GenericTableColumn> VerticaDefaultModifier = (monitor, column, sql, command) -> {
        if (CommonUtils.isEmpty(command.getObject().getDefaultValue())) {
            sql.append(" DROP DEFAULT");
        } else {
            sql.append(" SET DEFAULT ");
            DefaultModifier.appendModifier(monitor, column, sql, command);
        }
    };

    private final ColumnModifier<GenericTableColumn> VerticaNotNullModifier = (monitor, column, sql, command) -> {
        if (command.getObject().isRequired()) {
            sql.append(" SET NOT NULL");
        } else {
            sql.append(" DROP NOT NULL");
        }
    };

    private final ColumnModifier<GenericTableColumn> IncrementModifier = (monitor, column, sql, command) -> {
        if (column.isAutoIncrement()) {
            sql.append(" IDENTITY"); //$NON-NLS-1$
        }
    };

    @Override
    protected ColumnModifier[] getSupportedModifiers(GenericTableColumn column, Map<String, Object> options) {
        if (column.isAutoIncrement() && !column.isPersisted()) {
            // DefaultModifier and DataTypeModifier not supported in this case, IncrementModifier must be before NotNullModifier
            return new ColumnModifier[] {IncrementModifier, NotNullModifier};
        }
        if (column.isPersisted()) {
            // According to SQL92 DEFAULT comes before constraints
            return new ColumnModifier[]{VerticaDataTypeModifier, VerticaDefaultModifier, VerticaNotNullModifier};
        }
        return super.getSupportedModifiers(column, options);
    }

    @Override
    public void addIncrementClauseToNestedDeclaration(DBECommandAbstract<GenericTableColumn> command, StringBuilder decl) {
        // Increment clause already append with the IncrementModifier
    }

    /**
     * Copy-pasted from PostgreSQL implementation.
     * TODO: Vertica is originally based on PG. Maybe we should refactor this stuff somehow.
     */
    @Override
    protected void addObjectModifyActions(DBRProgressMonitor monitor, DBCExecutionContext executionContext, List<DBEPersistAction> actionList, SQLObjectEditor<GenericTableColumn, GenericTableBase>.ObjectChangeCommand command, Map<String, Object> options) throws DBException
    {
        final GenericTableColumn column = command.getObject();

        String prefix = "ALTER TABLE " + DBUtils.getObjectFullName(column.getTable(), DBPEvaluationContext.DDL) + " ALTER COLUMN " + DBUtils.getQuotedIdentifier(column) + " ";
        String typeClause = column.getFullTypeName();
        if (command.getProperty(DBConstants.PROP_ID_TYPE_NAME) != null || command.getProperty("maxLength") != null || command.getProperty("precision") != null || command.getProperty("scale") != null) {
            actionList.add(new SQLDatabasePersistAction("Set column type", prefix + "SET DATA TYPE " + typeClause));
        }
        if (command.getProperty(DBConstants.PROP_ID_REQUIRED) != null) {
            actionList.add(new SQLDatabasePersistAction("Set column nullability", prefix + (column.isRequired() ? "SET" : "DROP") + " NOT NULL"));
        }
        if (command.getProperty(DBConstants.PROP_ID_DEFAULT_VALUE) != null) {
            if (CommonUtils.isEmpty(column.getDefaultValue())) {
                actionList.add(new SQLDatabasePersistAction("Drop column default", prefix + "DROP DEFAULT"));
            } else {
                actionList.add(new SQLDatabasePersistAction("Set column default", prefix + "SET DEFAULT " + column.getDefaultValue()));
            }
        }
        super.addObjectModifyActions(monitor, executionContext, actionList, command, options);
    }

    @Override
    protected void addObjectRenameActions(DBRProgressMonitor monitor, DBCExecutionContext executionContext, List<DBEPersistAction> actions, SQLObjectEditor<GenericTableColumn, GenericTableBase>.ObjectRenameCommand command, Map<String, Object> options)
    {
        final GenericTableColumn column = command.getObject();

        actions.add(
                new SQLDatabasePersistAction(
                        "Rename column",
                        "ALTER TABLE " + column.getTable().getFullyQualifiedName(DBPEvaluationContext.DDL) + " RENAME COLUMN " +
                                DBUtils.getQuotedIdentifier(column.getDataSource(), command.getOldName()) +
                                " TO " + DBUtils.getQuotedIdentifier(column.getDataSource(), command.getNewName())));
    }

    @Override
    public void renameObject(@NotNull DBECommandContext commandContext, @NotNull GenericTableColumn object, @NotNull Map<String, Object> options, @NotNull String newName) throws DBException {
        processObjectRename(commandContext, object, options, newName);
    }
}
