
package com.dc.summer.ext.vertica.model;

import com.dc.summer.ext.generic.model.GenericTableBase;
import com.dc.summer.ext.generic.model.GenericUniqueKey;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.struct.DBSEntityConstraintType;
import com.dc.code.Nullable;
import com.dc.summer.model.struct.rdb.DBSTableCheckConstraint;

public class VerticaConstraint extends Generic<PERSON>nique<PERSON>ey implements DBSTableCheckConstraint {

    private String checkConstraintDefinition;
    private boolean isEnabled;

    public VerticaConstraint(GenericTableBase table, String name, @Nullable String remarks, DBSEntityConstraintType constraintType, boolean persisted, @Nullable String checkExpression, boolean isEnabled) {
        super(table, name, remarks, constraintType, persisted);
        this.checkConstraintDefinition = checkExpression;
        this.isEnabled = isEnabled;
    }

    @Nullable
    @Override
    @Property(viewable = true, order = 4)
    public String getCheckConstraintDefinition() {
        return checkConstraintDefinition;
    }

    @Override
    public void setCheckConstraintDefinition(String expression) {
        this.checkConstraintDefinition = expression;
    }

    @Property(viewable = true, editable = true, updatable = true, order = 6)
    public boolean isEnabled() {
        return isEnabled;
    }

    public void setEnabled(boolean enabled) {
        isEnabled = enabled;
    }
}
