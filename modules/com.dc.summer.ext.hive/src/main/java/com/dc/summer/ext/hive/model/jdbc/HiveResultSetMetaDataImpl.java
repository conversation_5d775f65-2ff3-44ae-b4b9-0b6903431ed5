
package com.dc.summer.ext.hive.model.jdbc;

import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.impl.jdbc.exec.JDBCResultSetMetaDataImpl;
import com.dc.utils.CommonUtils;

import java.sql.SQLException;

/**
 * HiveResultSetMetaDataImpl.
 *
 * Fixes Hive driver results metadata. All column names/labels have table name prefix (weird)
 */
public class HiveResultSetMetaDataImpl extends JDBCResultSetMetaDataImpl
{

    public HiveResultSetMetaDataImpl(JDBCResultSet resultSet) throws SQLException {
        super(resultSet);
    }

    @Override
    public String getColumnName(int column) throws SQLException {
        return removeTableNamePrefix(super.getColumnName(column));
    }

    @Override
    public String getColumnLabel(int column) throws SQLException {
        return removeTableNamePrefix(super.getColumnLabel(column));
    }

    private String removeTableNamePrefix(String columnName) throws SQLException {
        if (!CommonUtils.isEmpty(columnName)) {
            int divPos = columnName.indexOf('.');
            if (divPos > 0 && divPos < columnName.length() - 1) {
                // Remove table name prefix
                return columnName.substring(divPos + 1);
            }
        }
        return columnName;
    }

}
