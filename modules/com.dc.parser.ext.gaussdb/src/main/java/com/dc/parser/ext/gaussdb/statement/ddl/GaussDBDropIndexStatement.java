package com.dc.parser.ext.gaussdb.statement.ddl;

import com.dc.parser.ext.gaussdb.statement.GaussDBStatement;
import com.dc.parser.model.statement.ddl.DropIndexStatement;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

/**
 * GaussDB drop index statement.
 */
@RequiredArgsConstructor
@Getter
@Setter
public final class GaussDBDropIndexStatement extends DropIndexStatement implements GaussDBStatement {

    private boolean ifExists;
}
