
package com.dc.parser.ext.gaussdb.visitor.statement.type;

import com.dc.parser.model.api.ASTNode;
import com.dc.parser.model.api.visitor.statement.type.TCLStatementVisitor;
import com.dc.parser.ext.gaussdb.parser.autogen.GaussDBStatementParser.*;
import com.dc.parser.ext.gaussdb.visitor.statement.GaussDBStatementVisitor;
import com.dc.parser.ext.gaussdb.statement.tcl.*;

/**
 * TCL statement visitor for openGauss.
 */
public final class GaussDBTCLStatementVisitor extends GaussDBStatementVisitor implements TCLStatementVisitor {
    
    @Override
    public ASTNode visitSetTransaction(final SetTransactionContext ctx) {
        return new GaussDBSetTransactionStatement();
    }
    
    @Override
    public ASTNode visitBeginTransaction(final BeginTransactionContext ctx) {
        return new GaussDBBeginTransactionStatement();
    }
    
    @Override
    public ASTNode visitCommit(final CommitContext ctx) {
        return new GaussDBCommitStatement();
    }
    
    @Override
    public ASTNode visitRollback(final RollbackContext ctx) {
        return new GaussDBRollbackStatement();
    }
    
    @Override
    public ASTNode visitAbort(final AbortContext ctx) {
        return new GaussDBRollbackStatement();
    }
    
    @Override
    public ASTNode visitSavepoint(final SavepointContext ctx) {
        String savepointName = ctx.colId().getText();
        GaussDBSavepointStatement result = new GaussDBSavepointStatement();
        result.setSavepointName(savepointName);
        return result;
    }
    
    @Override
    public ASTNode visitRollbackToSavepoint(final RollbackToSavepointContext ctx) {
        GaussDBRollbackStatement result = new GaussDBRollbackStatement();
        result.setSavepointName(ctx.colId().getText());
        return result;
    }
    
    @Override
    public ASTNode visitReleaseSavepoint(final ReleaseSavepointContext ctx) {
        String savepointName = ctx.colId().getText();
        GaussDBReleaseSavepointStatement result = new GaussDBReleaseSavepointStatement();
        result.setSavepointName(savepointName);
        return result;
    }
    
    @Override
    public ASTNode visitStartTransaction(final StartTransactionContext ctx) {
        return new GaussDBStartTransactionStatement();
    }
    
    @Override
    public ASTNode visitEnd(final EndContext ctx) {
        return new GaussDBCommitStatement();
    }
    
    @Override
    public ASTNode visitSetConstraints(final SetConstraintsContext ctx) {
        return new GaussDBSetConstraintsStatement();
    }
    
    @Override
    public ASTNode visitCommitPrepared(final CommitPreparedContext ctx) {
        return new GaussDBCommitPreparedStatement();
    }
    
    @Override
    public ASTNode visitRollbackPrepared(final RollbackPreparedContext ctx) {
        return new GaussDBRollbackPreparedStatement();
    }
}
