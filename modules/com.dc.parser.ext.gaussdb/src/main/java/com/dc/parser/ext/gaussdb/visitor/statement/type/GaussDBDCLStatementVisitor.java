
package com.dc.parser.ext.gaussdb.visitor.statement.type;

import com.dc.parser.model.api.ASTNode;
import com.dc.parser.model.api.visitor.statement.type.DCLStatementVisitor;
import com.dc.parser.ext.gaussdb.parser.autogen.GaussDBStatementParser.*;
import com.dc.parser.ext.gaussdb.visitor.statement.GaussDBStatementVisitor;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.value.collection.CollectionValue;
import com.dc.parser.ext.gaussdb.statement.dcl.*;

import java.util.Collection;

/**
 * DCL statement visitor for openGauss.
 */
public final class GaussDBDCLStatementVisitor extends GaussDBStatementVisitor implements DCLStatementVisitor {
    
    @Override
    public ASTNode visitGrant(final GrantContext ctx) {
        GaussDBGrantStatement result = new GaussDBGrantStatement();
        if (containsTableSegment(ctx.privilegeClause())) {
            result.getTables().addAll(getTableSegments(ctx.privilegeClause()));
        }
        return result;
    }
    
    @Override
    public ASTNode visitRevoke(final RevokeContext ctx) {
        GaussDBRevokeStatement result = new GaussDBRevokeStatement();
        if (containsTableSegment(ctx.privilegeClause())) {
            result.getTables().addAll(getTableSegments(ctx.privilegeClause()));
        }
        return result;
    }
    
    private boolean containsTableSegment(final PrivilegeClauseContext ctx) {
        return null != ctx && null != ctx.onObjectClause() && null != ctx.onObjectClause().privilegeLevel() && null != ctx.onObjectClause().privilegeLevel().tableNames();
    }
    
    @SuppressWarnings("unchecked")
    private Collection<SimpleTableSegment> getTableSegments(final PrivilegeClauseContext ctx) {
        return ((CollectionValue<SimpleTableSegment>) visit(ctx.onObjectClause().privilegeLevel().tableNames())).getValue();
    }
    
    @Override
    public ASTNode visitCreateUser(final CreateUserContext ctx) {
        return new GaussDBCreateUserStatement();
    }
    
    @Override
    public ASTNode visitDropUser(final DropUserContext ctx) {
        return new GaussDBDropUserStatement();
    }
    
    @Override
    public ASTNode visitAlterUser(final AlterUserContext ctx) {
        return new GaussDBAlterUserStatement();
    }
    
    @Override
    public ASTNode visitCreateRole(final CreateRoleContext ctx) {
        return new GaussDBCreateRoleStatement();
    }
    
    @Override
    public ASTNode visitAlterRole(final AlterRoleContext ctx) {
        return new GaussDBAlterRoleStatement();
    }
    
    @Override
    public ASTNode visitDropRole(final DropRoleContext ctx) {
        return new GaussDBDropRoleStatement();
    }
}
