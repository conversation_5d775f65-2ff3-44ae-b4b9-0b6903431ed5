
package com.dc.summer.ext.mssql;

import com.dc.summer.ext.mssql.model.SQLServerAuthentication;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.impl.auth.AuthModelDatabaseNative;
import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.ext.mssql.model.SQLServerDataSource;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.connection.DBPAuthModelDescriptor;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.connection.DBPDriver;
import com.dc.summer.model.impl.jdbc.JDBCDataSourceProvider;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.runtime.DBWorkbench;
import com.dc.utils.CommonUtils;

import java.util.HashMap;
import java.util.Map;

public class SQLServerDataSourceProvider extends JDBCDataSourceProvider {

    private static Map<String,String> connectionsProps;

    static {
        connectionsProps = new HashMap<>();
    }

    public static Map<String,String> getConnectionsProps() {
        return connectionsProps;
    }

    public SQLServerDataSourceProvider()
    {
    }

    @Override
    public long getFeatures() {
        return FEATURE_CATALOGS | FEATURE_SCHEMAS;
    }

    @Override
    public String getConnectionURL(DBPDriver driver, DBPConnectionConfiguration connectionInfo) {
        StringBuilder url = new StringBuilder();
        boolean isJtds = SQLServerUtils.isDriverJtds(driver);
        boolean isSqlServer = SQLServerUtils.isDriverSqlServer(driver);
        boolean isDriverAzure = isSqlServer && SQLServerUtils.isDriverAzure(driver);

        if (isSqlServer) {
            // SQL Server
            if (isJtds) {
                url.append("jdbc:jtds:sqlserver://");
                url.append(connectionInfo.getHostName());
                if (!CommonUtils.isEmpty(connectionInfo.getHostPort()) && !connectionInfo.getHostPort().equals(driver.getDefaultPort())) {
                    url.append(":").append(connectionInfo.getHostPort());
                }
            } else {
                url.append("jdbc:sqlserver://");
                url.append(";serverName=").append(connectionInfo.getHostName());
                if (!CommonUtils.isEmpty(connectionInfo.getHostPort()) && !connectionInfo.getHostPort().equals(driver.getDefaultPort())) {
                    url.append(";port=").append(connectionInfo.getHostPort());
                }
            }
            if (isJtds) {
                if (!CommonUtils.isEmpty(connectionInfo.getDatabaseName())) {
                    url.append("/").append(connectionInfo.getDatabaseName());
                }
            } else {
                url.append(";");
                if (!CommonUtils.isEmpty(connectionInfo.getDatabaseName())) {
                    url.append("databaseName=").append(connectionInfo.getDatabaseName());
                }

                if (isDriverAzure) {
                    url.append(";encrypt=true"); // ;hostNameInCertificate=*.database.windows.net
                }
            }
/*
            if ("TRUE".equalsIgnoreCase(connectionInfo.getProviderProperty(SQLServerConstants.PROP_CONNECTION_WINDOWS_AUTH))) {
                url.append(";integratedSecurity=true");
            }
*/
        } else {
            // Sybase
            if (isJtds) {
                url.append("jdbc:jtds:sybase://");
                url.append(connectionInfo.getHostName());
                if (!CommonUtils.isEmpty(connectionInfo.getHostPort())) {
                    url.append(":").append(connectionInfo.getHostPort());
                }
                if (!CommonUtils.isEmpty(connectionInfo.getDatabaseName())) {
                    url.append("/").append(connectionInfo.getDatabaseName());
                }
            } else {
                url.append("jdbc:sybase:Tds:");
                url.append(connectionInfo.getHostName());
                if (!CommonUtils.isEmpty(connectionInfo.getHostPort())) {
                    url.append(":").append(connectionInfo.getHostPort());
                }
                if (!CommonUtils.isEmpty(connectionInfo.getDatabaseName())) {
                    url.append("?ServiceName=").append(connectionInfo.getDatabaseName());
                }
            }
        }

        return url.toString();
    }

    @NotNull
    @Override
    public DBPDataSource openDataSource(
            @NotNull DBRProgressMonitor monitor,
            @NotNull DBPDataSourceContainer container)
            throws DBException
    {
        return new SQLServerDataSource(monitor, container);
    }

    @Override
    public DBPAuthModelDescriptor detectConnectionAuthModel(DBPDriver driver, DBPConnectionConfiguration connectionInfo) {
        if (driver.getProviderDescriptor().matchesId(SQLServerConstants.PROVIDER_SQL_SERVER) &&
            (CommonUtils.isEmpty(connectionInfo.getAuthModelId()) ||
            connectionInfo.getAuthModelId().equals(AuthModelDatabaseNative.ID)))
        {
            // Convert legacy config to auth model
            SQLServerAuthentication authSchema = SQLServerUtils.detectAuthSchema(connectionInfo);
            String amId = authSchema.getReplacedByAuthModelId();
            DBPAuthModelDescriptor authModel = null;
            // TODO 1
//            DBPAuthModelDescriptor authModel = DBWorkbench.getPlatform().getDataSourceProviderRegistry().getAuthModel(amId);
            if (authModel != null) {
                return authModel;
            }
            log.error("Replacement auth model " + amId + " not found");
        }
        return super.detectConnectionAuthModel(driver, connectionInfo);
    }

}
