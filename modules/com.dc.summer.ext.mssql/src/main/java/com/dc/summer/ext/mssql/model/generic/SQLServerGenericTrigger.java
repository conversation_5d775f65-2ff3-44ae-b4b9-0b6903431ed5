
package com.dc.summer.ext.mssql.model.generic;

import com.dc.summer.ext.generic.model.GenericTableBase;
import com.dc.summer.ext.generic.model.GenericTableTrigger;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBPQualifiedObject;
import com.dc.summer.model.DBUtils;

/**
* SQL server trigger
*/
public class SQLServerGenericTrigger extends GenericTableTrigger implements DBPQualifiedObject {

    public SQLServerGenericTrigger(GenericTableBase container, String name, String description) {
        super(container, name, description);
    }

    @Override
    public SQLServerGenericTable getTable() {
        return (SQLServerGenericTable) super.getTable();
    }

    @Override
    public String getFullyQualifiedName(DBPEvaluationContext context) {
        return DBUtils.getFullQualifiedName(getDataSource(),
            getTable().getSchema(),
            this);
    }
}
