
package com.dc.summer.ext.mssql.model;

import com.dc.summer.model.struct.rdb.DBSTableForeignKeyColumn;
import com.dc.summer.model.meta.Property;

/**
 * SQLServerTableForeignKeyColumn
 */
public class SQLServerTableForeignKeyColumn extends SQLServerTableUniqueKeyColumn implements DBSTableForeignKeyColumn
{
    private SQLServerTableColumn referencedColumn;

    public SQLServerTableForeignKeyColumn(
        SQLServerTableForeignKey constraint,
        SQLServerTableColumn tableColumn,
        int ordinalPosition,
        SQLServerTableColumn referencedColumn)
    {
        super(constraint, tableColumn, ordinalPosition);
        this.referencedColumn = referencedColumn;
    }

    @Override
    @Property(id = "reference", viewable = true, order = 4)
    public SQLServerTableColumn getReferencedColumn()
    {
        return referencedColumn;
    }

}
