package com.dc.summer.ext.mssql.model;

import java.util.Arrays;

public enum SetOptionsType {

    ANSI_DEFAULTS("1"),
    QUOTED_IDENTIFIER("2"),
    ANSI_NULL_DFLT_ON("3"),
    CURSOR_CLOSE_ON_COMMIT("4"),
    ANSI_PADDING("5"),
    ANSI_WARNINGS("6"),
    ANSI_NULLS("7");

    private String code;

    SetOptionsType(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public static final String DEFAULT_OPTIONS = String.join(",", Arrays.asList(
            QUOTED_IDENTIFIER.getCode(),
            ANSI_NULL_DFLT_ON.getCode(),
            ANSI_PADDING.getCode(),
            ANSI_WARNINGS.getCode(),
            ANSI_NULLS.getCode()));

}
