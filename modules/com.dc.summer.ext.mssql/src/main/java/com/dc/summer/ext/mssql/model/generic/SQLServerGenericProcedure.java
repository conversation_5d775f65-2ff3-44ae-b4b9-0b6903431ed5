
package com.dc.summer.ext.mssql.model.generic;

import com.dc.summer.ext.generic.model.GenericFunctionResultType;
import com.dc.summer.ext.generic.model.GenericProcedure;
import com.dc.summer.ext.generic.model.GenericStructContainer;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.struct.rdb.DBSProcedureType;

/**
* SQL Server procedure
*/
public class SQLServerGenericProcedure extends GenericProcedure {

    public SQLServerGenericProcedure(GenericStructContainer container, String procedureName, String specificName, String description, DBSProcedureType procedureType, GenericFunctionResultType functionResultType) {
        super(container, procedureName, specificName, description, procedureType, functionResultType);
    }


    @Override
    public String getFullyQualifiedName(DBPEvaluationContext context) {
        return super.getFullyQualifiedName(context);
    }
}
