package com.dc.summer.ext.mssql.model.data;

import com.dc.summer.ext.mssql.SQLServerConstants;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.impl.jdbc.data.handlers.JDBCObjectValueHandler;
import com.dc.summer.model.struct.DBSTypedObject;
import com.dc.utils.CommonUtils;

import java.sql.SQLException;

public class SQLServerTimestampValueHandler extends JDBCObjectValueHandler {

    public static final SQLServerTimestampValueHandler INSTANCE = new SQLServerTimestampValueHandler();

    @Override
    protected Object fetchColumnValue(DBCSession session, JDBCResultSet resultSet, DBSTypedObject type, int index) throws DBCException, SQLException {

        if (SQLServerConstants.TYPE_DATETIMEOFFSET.equalsIgnoreCase(type.getTypeName())) {
            return resultSet.getString(index);
        }

        byte[] bytes = resultSet.getBytes(index);
        if (bytes == null) {
            return null;
        }
        return String.format("0x%s", CommonUtils.toHexString(bytes));
    }
}
