
package com.dc.summer.ext.mssql.model;

import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.model.struct.DBSObjectType;

import java.util.ArrayList;
import java.util.List;

/**
 * Object type
 */
public enum SQLServerObjectType implements DBSObjectType {

    AF("AF", null, "Aggregate function (CLR)"),
    C("C", SQLServerTableCheckConstraint.class, "CHECK constraint"),
    D("D", null, "DEFAULT (constraint or stand-alone)"),
    F("F", SQLServerTableForeignKey.class, "FOREIGN KEY constraint"),
    FN("FN", SQLServerProcedure.class, "SQL scalar function"),
    FS("FS", SQLServerProcedure.class, "Assembly (CLR) scalar-function"),
    FT("FT", SQLServerProcedure.class, "Assembly (CLR) table-valued function"),
    IF("IF", SQLServerProcedure.class, "SQL inline table-valued function"),
    IT("IT", SQLServerTable.class, "Internal table"),
    P("P", SQLServerProcedure.class, "SQL Stored Procedure"),
    PC("PC", SQLServerProcedure.class, "Assembly (CLR) stored-procedure"),
    PG("PG", null, "Plan guide"),
    PK("PK", SQLServerTableUniqueKey.class, "PRIMARY KEY constraint"),
    R("R", null, "Rule (old-style, stand-alone)"),
    RF("RF", null, "Replication-filter-procedure"),
    S("S", SQLServerTable.class, "System base table"),
    SN("SN", SQLServerSynonym.class, "Synonym"),
    SQ("SQ", null, "Service queue"),
    TA("TA", null, "Assembly (CLR) DML trigger"),
    TF("TF", SQLServerProcedure.class, "SQL table-valued-function"),
    TR("TR", SQLServerTableTrigger.class, "SQL DML trigger"),
    TT("TT", null, "Table type"),
    U("U", SQLServerTable.class, "Table"),
    UQ("UQ", SQLServerTableUniqueKey.class, "UNIQUE constraint"),
    V("V", SQLServerView.class, "View"),
    X("X", SQLServerProcedure.class, "Extended stored procedure");


    private final String type;
    private final String description;
    private final Class<? extends DBSObject> theClass;

    private static final Log log = Log.getLog(SQLServerObjectType.class);

    SQLServerObjectType(String type, Class<? extends DBSObject> theClass, String description) {
        this.type = type;
        this.theClass = theClass;
        this.description = description;
    }

    @Override
    public String getTypeName() {
        return description;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public Class<? extends DBSObject> getTypeClass() {
        return theClass;
    }

    public String getTypeID() {
        return type;
    }

    @Override
    public String toString() {
        return type;
    }

    public DBSObject findObject(DBRProgressMonitor monitor, SQLServerDatabase database, SQLServerSchema schema, String objectName) throws DBException {
        if (schema == null) {
            log.debug("Null schema in table " + objectName + " search (" + name() + ")");
            return null;
        }

        if (SQLServerTableBase.class.isAssignableFrom(theClass)) {
            return schema.getChild(monitor, objectName);
        } else if (SQLServerProcedure.class.isAssignableFrom(theClass)) {
            return schema.getProcedure(monitor, objectName);
        } else if (SQLServerSynonym.class.isAssignableFrom(theClass)) {
            SQLServerSynonym synonym = schema.getSynonym(monitor, objectName);
            return synonym;
        } else {
            log.debug("Unsupported object for SQL Server search: " + name());
            return null;
        }
    }

    public static List<SQLServerObjectType> getTypesForClass(Class<?> theClass) {
        List<SQLServerObjectType> result = new ArrayList<>();
        for (SQLServerObjectType ot : SQLServerObjectType.values()) {
            if (ot.theClass == theClass) {
                result.add(ot);
            }
        }
        return result;
    }
}
