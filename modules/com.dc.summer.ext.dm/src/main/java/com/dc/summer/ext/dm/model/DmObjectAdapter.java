package com.dc.summer.ext.dm.model;

import org.eclipse.core.runtime.IAdapterFactory;
import com.dc.summer.ext.dm.model.source.DmSourceObject;
import com.dc.summer.model.DBPScriptObjectExt;
import com.dc.summer.model.navigator.DBNDatabaseNode;
import com.dc.summer.model.struct.DBSObject;

public class DmObjectAdapter implements IAdapterFactory {

	public DmObjectAdapter() {

	}

	@Override
	public <T> T getAdapter(Object adaptableObject, Class<T> adapterType) {
		if (DBSObject.class.isAssignableFrom(adapterType)) {
			DBSObject dbObject = null;
			if (adaptableObject instanceof DBNDatabaseNode) {
				dbObject = ((DBNDatabaseNode) adaptableObject).getObject();
			}
			if (dbObject != null && adapterType.isAssignableFrom(dbObject.getClass())) {
				return adapterType.cast(dbObject);
			}
		}
		return null;
	}

	@Override
	public Class[] getAdapterList() {
		return new Class[] { DmSourceObject.class, DmProcedurePackaged.class, DBPScriptObjectExt.class };
	}
}
