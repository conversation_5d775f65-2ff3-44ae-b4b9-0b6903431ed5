
package com.dc.summer.ext.teradata.model;

import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.summer.ext.generic.model.GenericSQLDialect;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.impl.jdbc.JDBCDataSource;

import java.util.Arrays;

public class TeradataSQLDialect extends GenericSQLDialect {

    public TeradataSQLDialect() {
        super("Teradata", "teradata");
    }

    public void initDriverSettings(JDBCSession session, JDBCDataSource dataSource, JDBCDatabaseMetaData metaData) {
        super.initDriverSettings(session, dataSource, metaData);
        addSQLKeywords(
            Arrays.asList(
                "QUALIFY"
            ));
    }

    @Override
    public boolean supportsAliasInSelect() {
        return true;
    }

    @Override
    public boolean isCRLFBroken() {
        // #11985 Teradata returns DDL of views/procedures/triggers with extra break lines, when they are created in the SQL Editor
        return true;
    }

}
