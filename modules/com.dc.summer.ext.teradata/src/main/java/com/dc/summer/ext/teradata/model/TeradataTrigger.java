
package com.dc.summer.ext.teradata.model;

import com.dc.summer.model.struct.DBSActionTiming;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.ext.generic.model.GenericSchema;
import com.dc.summer.ext.generic.model.GenericTableBase;
import com.dc.summer.ext.generic.model.GenericTableTrigger;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBPQualifiedObject;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.meta.PropertyLength;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.rdb.DBSManipulationType;
import com.dc.utils.CommonUtils;

import java.util.Date;
import java.util.Map;

public class TeradataTrigger extends GenericTableTrigger implements DBPQualifiedObject {

    private DBSActionTiming actionTime;
    private DBSManipulationType eventType;
    private String enabledStatus;
    private String triggerType;
    private Date createDate;
    private String description;

    private String definition;

    public TeradataTrigger(GenericTableBase table, String name, String description, JDBCResultSet dbResult) {
        super(table, name, description);

        String actTime = JDBCUtils.safeGetString(dbResult, "ActionTime");
        if (!CommonUtils.isEmpty(actTime)) {
            switch (actTime) {
                case "A":
                    actionTime = DBSActionTiming.AFTER;
                    break;
                case "B":
                    actionTime = DBSActionTiming.BEFORE;
                    break;
                default:
                    actionTime = DBSActionTiming.UNKNOWN;
                    break;
            }
        }

        String event = JDBCUtils.safeGetString(dbResult, "Event");
        if (!CommonUtils.isEmpty(event)) {
            switch (event) {
                case "U":
                    eventType = DBSManipulationType.UPDATE;
                    break;
                case "I":
                    eventType = DBSManipulationType.INSERT;
                    break;
                case "D":
                    eventType = DBSManipulationType.DELETE;
                    break;
                default:
                    eventType = DBSManipulationType.UNKNOWN;
                    break;
            }
        }

        this.enabledStatus = JDBCUtils.safeGetString(dbResult, "status");
        this.triggerType = JDBCUtils.safeGetString(dbResult, "triggerKind");
        this.createDate = JDBCUtils.safeGetTimestamp(dbResult, "createDate");

        this.definition = JDBCUtils.safeGetString(dbResult, "definition");
        this.description = description;
    }

    @Property(viewable = true, order = 3)
    public DBSActionTiming getActionTime() {
        return actionTime;
    }

    @Property(viewable = true, order = 4)
    public DBSManipulationType getEventType() {
        return eventType;
    }

    @Property(viewable = true, order = 5)
    public String getEnabledStatus() {
        return enabledStatus;
    }

    @Property(viewable = true, order = 6)
    public String getTriggerType() {
        return triggerType;
    }

    @Property(order = 7)
    public Date getCreateDate() {
        return createDate;
    }

    @Override
    public String getObjectDefinitionText(DBRProgressMonitor monitor, Map<String, Object> options) {
        return definition;
    }

    public GenericSchema getSchema() {
        return getTable().getSchema();
    }

    @Nullable
    @Override
    @Property(viewable = true, editable = true, updatable = true, length = PropertyLength.MULTILINE, order = 100)
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String description) {
        this.description = description;
    }

    @NotNull
    @Override
    public String getFullyQualifiedName(DBPEvaluationContext context) {
        return DBUtils.getFullQualifiedName(getDataSource(),
            getSchema(),
            this);
    }
}
