
package com.dc.summer.ext.hana.edit;

import com.dc.summer.ext.generic.edit.GenericTableColumnManager;
import com.dc.summer.ext.generic.model.GenericTableBase;
import com.dc.summer.model.impl.edit.DBECommandAbstract;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.ext.generic.model.GenericTableColumn;

import java.util.Map;

/**
 * HANATableColumnManager
 */
public class HANATableColumnManager extends GenericTableColumnManager {

    @Override
    public StringBuilder getNestedDeclaration(DBRProgressMonitor monitor, GenericTableBase owner, DBECommandAbstract<GenericTableColumn> command, Map<String, Object> options) {
        StringBuilder decl = super.getNestedDeclaration(monitor, owner, command, options);
        if (owner.isPersisted()) {
            decl.insert(0, "(");
            decl.append(")");
        }
        return decl;
    }

}
