package com.dc.parser.ext.mongodb.segment.collection;

import com.dc.parser.ext.mongodb.segment.BsonObjectSegment;
import com.dc.parser.ext.mongodb.segment.MethodSegment;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CreateIndexSegment extends MethodSegment {
    private BsonObjectSegment keys;
    private BsonObjectSegment options;
    public CreateIndexSegment(int startIndex, int stopIndex) {
        super(startIndex, stopIndex);
    }
}