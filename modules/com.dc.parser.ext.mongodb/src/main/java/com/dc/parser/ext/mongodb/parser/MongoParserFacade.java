
package com.dc.parser.ext.mongodb.parser;

import com.dc.infra.database.type.DatabaseType;
import com.dc.parser.model.api.parser.SQLLexer;
import com.dc.parser.model.api.parser.SQLParser;
import com.dc.parser.model.spi.DialectSQLParserFacade;

/**
 * SQL parser facade for MONGO_DB.
 */
public final class MongoParserFacade implements DialectSQLParserFacade {
    
    @Override
    public Class<? extends SQLLexer> getLexerClass() {
        return MongoLexer.class;
    }
    
    @Override
    public Class<? extends SQLParser> getParserClass() {
        return MongoParser.class;
    }
    
    @Override
    public DatabaseType.Constant getDatabaseType() {
        return DatabaseType.Constant.MONGO_DB;
    }
}
