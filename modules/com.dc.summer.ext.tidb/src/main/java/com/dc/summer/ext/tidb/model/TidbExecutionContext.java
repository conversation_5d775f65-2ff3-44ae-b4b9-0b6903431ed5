package com.dc.summer.ext.tidb.model;

import com.dc.summer.Log;
import com.dc.summer.ext.mysql.model.MySQLExecutionContext;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.exec.*;
import com.dc.summer.model.impl.jdbc.JDBCRemoteInstance;
import com.dc.summer.model.runtime.DBRProgressMonitor;

import java.util.*;

public class TidbExecutionContext extends MySQLExecutionContext {

    private static final Log log = Log.getLog(TidbExecutionContext.class);

    protected TidbExecutionContext(JDBCRemoteInstance instance, String purpose) {
        super(instance, purpose);
    }

    @Override
    protected List<Map<String, Object>> initContextBootstrap(DBRProgressMonitor monitor, boolean autoCommit) throws DBCException {
        Map<String, String> sessionProperties = new HashMap<>();
        sessionProperties.put("tidb_mem_quota_query", "1073741824");
        sessionProperties.put("tidb_enable_rate_limit_action", "false");
        DBPConnectionConfiguration configuration = this.getConfiguration();
        Map<String, String> properties = configuration.getProperties();
        properties.forEach((k, v) -> {
            String key = k.toLowerCase(Locale.ROOT);
            if (sessionProperties.containsKey(key)) {
                sessionProperties.put(key, v);
            } else if (key.startsWith("tidb_")) {
                sessionProperties.put(key, v);
            }
        });
        List<String> queries = new ArrayList<>();
        sessionProperties.forEach( (k, v) -> queries.add("set session " + k + "=" + v));
        DBCExecutionPurpose executionPurpose = DBCExecutionPurpose.getByTitle(purpose);
        if (executionPurpose.getId() == DBCExecutionPurpose.USER.getId()) {
            queries.add("set MAX_EXECUTION_TIME=" + configuration.getQueryTimeout() * 1000);
        }
        try (DBCSession session = openSession(monitor, DBCExecutionPurpose.UTIL, "Run bootstrap queries")) {
            for (String query : queries) {
                try (DBCStatement dbStat = session.prepareStatement(DBCStatementType.SCRIPT, query, false, false, false)) {
                    dbStat.executeStatement();
                } catch (Exception e) {
                    log.error("Error executing query: " + query, e);
                }
            }
        }
        return super.initContextBootstrap(monitor, autoCommit);
    }
}
