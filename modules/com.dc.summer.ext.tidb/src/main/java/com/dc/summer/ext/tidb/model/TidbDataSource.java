package com.dc.summer.ext.tidb.model;

import com.dc.summer.DBException;
import com.dc.summer.ext.mysql.model.MySQLDataSource;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.DBPDataSourceInfo;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.connection.DBPDriver;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCExecutionPurpose;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.summer.model.impl.jdbc.JDBCExecutionContext;
import com.dc.summer.model.impl.jdbc.JDBCRemoteInstance;
import com.dc.summer.model.runtime.DBRProgressMonitor;

import java.util.Map;

public class TidbDataSource extends MySQLDataSource {
    public TidbDataSource(DBRProgressMonitor monitor, DBPDataSourceContainer container) throws DBException {
        super(monitor, container);
    }

    @Override
    protected DBPDataSourceInfo createDataSourceInfo(DBRProgressMonitor monitor, JDBCDatabaseMetaData metaData) {
        return new TidbDataSourceInfo(metaData);
    }

    @Override
    protected JDBCExecutionContext createExecutionContext(JDBCRemoteInstance instance, String type) {
        return new TidbExecutionContext(instance, type);
    }

    @Override
    protected Map<String, String> getInternalConnectionProperties(DBRProgressMonitor monitor, DBPDriver driver, JDBCExecutionContext context, String purpose, DBPConnectionConfiguration connectionInfo) throws DBCException {

        Map<String, String> map = super.getInternalConnectionProperties(monitor, driver, context, purpose, connectionInfo);

        Integer queryTimeout = connectionInfo.getQueryTimeout();
        if (DBCExecutionPurpose.getByTitle(purpose) != DBCExecutionPurpose.USER_SCRIPT && queryTimeout != null && queryTimeout > 0) {
            queryTimeout *= 1000;
        } else {
            queryTimeout = 0;
        }
        map.put("sessionVariables", "max_execution_time=" + queryTimeout);
        return map;
    }
}
