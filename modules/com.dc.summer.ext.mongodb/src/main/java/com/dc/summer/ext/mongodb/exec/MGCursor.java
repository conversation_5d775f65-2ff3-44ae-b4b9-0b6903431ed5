package com.dc.summer.ext.mongodb.exec;

import com.mongodb.client.FindIterable;
import java.io.Closeable;
import java.util.Iterator;
import org.bson.Document;
import com.dc.code.NotNull;

public class MGCursor implements Iterable<Document>, Closeable {
   protected final FindIterable<Document> delegate;

   protected int cursorLimit;

   public MGCursor(@NotNull FindIterable<Document> delegate) {
      this.delegate = delegate;
   }

   public MGCursor(@NotNull FindIterable<Document> delegate, int cursorLimit) {
      this.delegate = delegate;
      this.cursorLimit = cursorLimit;
   }

   public @NotNull MGCursor skip(int numberOfElements) {
      return new MGCursor(this.delegate.skip(numberOfElements), cursorLimit);
   }

   public MGCursor sort(Document orderBy) {
      return new MGCursor(this.delegate.sort(orderBy), cursorLimit);
   }

   public @NotNull MGCursor limit(int limit) {
      return new MGCursor(this.delegate.limit(limit), limit);
   }

   public @NotNull MGCursor batchSize(int numberOfElements) {
      return new MGCursor(this.delegate.batchSize(numberOfElements), cursorLimit);
   }

   public Iterator<Document> iterator() {
      return this.delegate.iterator();
   }

   public void close() {
   }

}
