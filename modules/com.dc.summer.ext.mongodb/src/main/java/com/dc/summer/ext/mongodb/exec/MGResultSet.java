package com.dc.summer.ext.mongodb.exec;

import com.dc.summer.ext.mongodb.data.MGDataUtils;
import com.dc.summer.ext.mongodb.data.MGDocument;
import com.dc.summer.ext.mongodb.model.MGDataSource;
import com.dc.summer.model.Types;
import com.dc.summer.model.document.data.DBNullValue;
import com.dc.summer.model.document.exec.DocumentResultSet;
import com.mongodb.client.MongoCursor;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.bson.Document;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCResultSetMetaData;
import com.dc.summer.model.exec.DBCResultSetSampleProvider;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.qm.QMUtils;
import com.dc.summer.model.runtime.DBRBlockingObject;
import com.dc.summer.model.runtime.DBRProgressMonitor;

public class MGResultSet extends DocumentResultSet<MGSession, MGBaseStatement> implements DBRBlockingObject, DBCResultSetSampleProvider {
    private final MGCursor cursor;
    private final MongoCursor mongoCursor;
    private final MongoCursor backupMongoCursor;
    private final List<Document> list;
    private DBCResultSetMetaData metaData;
    private Object curRow;
    private transient Iterator<Document> iterator;

    private int overRow;

    public MGResultSet(MGBaseStatement statement, MGCursor cursor) {
        super(statement);
        this.cursor = cursor;
        this.mongoCursor = null;
        this.backupMongoCursor = null;
        this.list = null;

        long offset = statement.getOffset();
        if (offset > 0L) {
            this.cursor.skip((int) offset);
        }

        long limit = statement.getLimit();
        if (limit > 0L) {
            this.cursor.batchSize((int) limit);
            //判断语句中的limit和页面设置的limit是否相等
            //语句中的limit需要减去页面的offset
            if (this.cursor.cursorLimit - offset != limit) {
                limit++;
            }
            //比较语句中的limit和页面设置的limit，哪个小，设置为最终limit
            if (this.cursor.cursorLimit - offset > 0 && this.cursor.cursorLimit - offset < limit) {
                limit = this.cursor.cursorLimit - offset;
            }
            this.cursor.limit((int) limit);
        } else if (statement.getResultsFetchSize() > 0) {
            this.cursor.batchSize(statement.getResultsFetchSize());
        }


        if ((statement.getSession()).isLoggingEnabled()) {
            QMUtils.getDefaultHandler().handleResultSetOpen(this);
        }

        (statement.getSession()).getProgressMonitor().startBlock(this, "Fetch result");
    }

    public MGResultSet(MGBaseStatement statement, MongoCursor mongoCursor, MongoCursor backupMongoCursor) {
        super(statement);
        this.mongoCursor = mongoCursor;
        this.backupMongoCursor = backupMongoCursor;
        this.cursor = null;
        this.list = null;

        long offset = statement.getOffset();
        if (offset > 0L) {
            for (int i = 0; (long) i < offset && this.mongoCursor.hasNext(); ++i) {
                this.mongoCursor.next();
                if (this.backupMongoCursor.hasNext()) {
                    this.backupMongoCursor.next();
                }
            }
        }

        if ((statement.getSession()).isLoggingEnabled()) {
            QMUtils.getDefaultHandler().handleResultSetOpen(this);
        }

        (statement.getSession()).getProgressMonitor().startBlock(this, "Fetch result");
    }

    public MGResultSet(MGBaseStatement statement, List<Document> list) {
        super(statement);
        this.cursor = null;
        this.mongoCursor = null;
        this.backupMongoCursor = null;
        this.list = list;
        if ((statement.getSession()).isLoggingEnabled()) {
            QMUtils.getDefaultHandler().handleResultSetOpen(this);
        }

        (statement.getSession()).getProgressMonitor().startBlock(this, "Fetch result");
    }

    public MGSession getSession() {
        return (this.statement).getSession();
    }

    public MGBaseStatement getSourceStatement() {
        return this.statement;
    }

    protected void checkRowFetched() throws DBCException {
        if (this.curRow == null) {
            curRow = new Document("", null);
        }
    }

    public Object getAttributeValue(int index) throws DBCException {
        this.checkRowFetched();
        if (index == 0) {
            return this.curRow;
        } else {
            throw new DBCException("Index out of range (" + index + ")");
        }
    }

    public Object getAttributeValue(String name) throws DBCException {
        this.checkRowFetched();
        boolean b = this.curRow instanceof Map;
        if (b) {
            Map<?, ?> map = (Map<?, ?>) this.curRow;
            Object value = map.get(name);
            if (value == null && map.containsKey(name)) {
                return new DBNullValue();
            }
            value = MGDataUtils.formatResultSet(value);
            return value;
        }
        return this.curRow;
    }

    public boolean nextRow() throws DBCException {
        long limit = (this.statement).getLimit();
        if (limit > 0L && (long) (this.itemNumber + 1) >= limit + overRow) {
            return false;
        } else if (this.list != null) {
            if (this.list.size() > this.itemNumber + 1) {
                this.curRow = this.list.get(this.itemNumber + 1);
                ++this.itemNumber;
                return true;
            } else {
                return false;
            }
        } else {
            try {
                if (this.mongoCursor != null) {
                    if (!this.mongoCursor.hasNext()) {
                        this.curRow = null;
                        return false;
                    }

                    this.curRow = this.mongoCursor.next();
                } else {
                    if (this.iterator == null) {
                        if (this.cursor != null) {
                            this.iterator = this.cursor.iterator();
                            return this.nextRow();
                        }

                        throw new DBCException("Null result");
                    }

                    if (!this.iterator.hasNext()) {
                        this.curRow = null;
                        return false;
                    }

                    this.curRow = this.iterator.next();
                }

                ++this.itemNumber;
                return true;
            } catch (Exception var4) {
                throw new DBCException("Error fetching Mongo cursor : " + var4.getMessage(), var4);
            }
        }
    }

    @Override
    public boolean overMaxRow() throws DBCException {
        overRow++;
        try {
            return nextRow();
        } finally {
            overRow--;
        }
    }

    public boolean moveTo(int position) throws DBCException {
        return false;
    }

    @Override
    public int getTypeId() {
        return Types.DOCUMENT;
    }

    public String getResultSetName() throws DBCException {
        return null;
    }

    public Object getFeature(String name) {
        return "document".equals(name) ? true : null;
    }

    public void close() {
        ((this.statement).getSession()).getProgressMonitor().endBlock();
        if (((this.statement).getSession()).isLoggingEnabled()) {
            QMUtils.getDefaultHandler().handleResultSetClose(this, (long) this.itemNumber);
        }

        this.iterator = null;
    }

    public void cancelBlock(@NotNull DBRProgressMonitor monitor, @Nullable Thread blockThread) throws DBException {
        if (this.cursor != null) {
            this.cursor.close();
        }

    }

    public @NotNull List<Object[]> getSampleRows(DBCSession session, int maxRows) throws DBCException {

        List<Object[]> sample = new ArrayList<>();
        Document object;

        if (this.list != null) {
            for (Document document : this.list) {
                object = document;
                sample.add(new Object[]{new MGDocument((MGDataSource) session.getDataSource(), object)});
                if (sample.size() >= maxRows) {
                    break;
                }
            }
        } else if (this.backupMongoCursor != null) {
            while (this.backupMongoCursor.hasNext()) {
                object = (Document) this.backupMongoCursor.next();
                sample.add(new Object[]{new MGDocument((MGDataSource) session.getDataSource(), object)});
                if (sample.size() >= maxRows || session.getProgressMonitor().isCanceled()) {
                    break;
                }
            }
        } else if (this.mongoCursor == null && this.cursor != null) {
            for (Document document : this.cursor) {
                object = document;
                sample.add(new Object[]{new MGDocument((MGDataSource) session.getDataSource(), object)});
                if (sample.size() >= maxRows || session.getProgressMonitor().isCanceled()) {
                    break;
                }
            }
        }

        return sample;
    }
}
