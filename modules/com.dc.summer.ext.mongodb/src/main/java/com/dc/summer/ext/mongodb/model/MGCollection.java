package com.dc.summer.ext.mongodb.model;

import com.dc.summer.ext.mongodb.data.MGDocument;
import com.dc.summer.ext.mongodb.exec.MGResultSet;
import com.dc.summer.ext.mongodb.exec.MGSession;
import com.dc.summer.ext.mongodb.exec.sql.MGDeleteStatement;
import com.dc.summer.ext.mongodb.exec.sql.MGInsertStatement;
import com.dc.summer.ext.mongodb.exec.sql.MGSelectStatement;
import com.dc.summer.ext.mongodb.exec.sql.MGUpdateStatement;
import com.dc.summer.ext.mongodb.exec.sql.MongoSQLUtils;
import com.dc.summer.model.document.DBAbstractDocumentContainer;
import com.mongodb.MongoException;
import com.mongodb.MongoServerException;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Accumulators;
import com.mongodb.client.model.Aggregates;
import com.mongodb.client.model.BsonField;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.Sorts;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.bson.Document;
import org.bson.conversions.Bson;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.model.DBFetchProgress;
import com.dc.summer.model.DBPDataKind;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBPNamedObject2;
import com.dc.summer.model.DBPQualifiedObject;
import com.dc.summer.model.DBPSaveableObject;
import com.dc.summer.model.DBPSystemObject;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.data.DBDDataFilter;
import com.dc.summer.model.data.DBDDataFormatter;
import com.dc.summer.model.data.DBDDataReceiver;
import com.dc.summer.model.data.DBDLabelValuePair;
import com.dc.summer.model.data.DBDLabelValuePairExt;
import com.dc.summer.model.data.DBDValueHandler;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCExecutionSource;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.DBCStatement;
import com.dc.summer.model.exec.DBCStatistics;
import com.dc.summer.model.impl.DBObjectNameCaseTransformer;
import com.dc.summer.model.impl.data.ExecuteBatchImpl;
import com.dc.summer.model.messages.ModelMessages;
import com.dc.summer.model.meta.IPropertyCacheValidator;
import com.dc.summer.model.meta.LazyProperty;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.meta.PropertyGroup;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.SQLUtils;
import com.dc.summer.model.struct.DBSAttributeBase;
import com.dc.summer.model.struct.DBSDataContainer;
import com.dc.summer.model.struct.DBSDataManipulator;
import com.dc.summer.model.struct.DBSDocumentAttributeEnumerable;
import com.dc.summer.model.struct.DBSDocumentContainer;
import com.dc.summer.model.struct.DBSEntityAttribute;
import com.dc.summer.model.struct.DBSEntityType;
import com.dc.summer.model.struct.cache.AbstractObjectCache;
import com.dc.summer.model.struct.rdb.DBSTable;
import com.dc.summer.model.struct.rdb.DBSTableIndex;
import com.dc.summer.model.struct.rdb.DBSTrigger;
import com.dc.utils.ArrayUtils;
import com.dc.utils.CommonUtils;

public class MGCollection extends DBAbstractDocumentContainer<MGDataSource> implements DBSTable, DBSDataContainer, DBSDataManipulator, DBSDocumentContainer, DBPSaveableObject, DBPSystemObject, DBPNamedObject2, DBSDocumentAttributeEnumerable {
    private static final Log log = Log.getLog(MGCollection.class);
    private static final boolean LOAD_ATTRIBUTES = false;
    private final MGDatabase database;
    private String collectionName;
    private final IndexCache indexCache = new IndexCache();
    private List<MGCollectionAttribute> metaAttributes;
    private final IdAttribute attrId;
    private final Stats stats = new Stats();
    private boolean persisted;

    public MGCollection(MGDatabase database, String collectionName, boolean persisted) {
        super(database.getDataSource());
        this.database = database;
        this.collectionName = collectionName;
        this.attrId = new IdAttribute(this);
        this.persisted = persisted;
    }

    protected @NotNull DBSEntityAttribute createDocumentAttribute() {
        return new DocumentAttribute(this);
    }

    @Property(
            viewable = true,
            order = 3
    )
    public MGDatabase getDatabase() {
        return this.database;
    }

    public @NotNull MongoCollection<Document> getMongoCollection(MGSession session) throws DBCException {
        return this.database.getDatabase(session).getCollection(this.collectionName);
    }

    @Property(
            viewable = true,
            editable = true,
            valueTransformer = DBObjectNameCaseTransformer.class,
            order = 1
    )
    public @NotNull String getName() {
        return this.collectionName;
    }

    public void setName(String newName) {
        this.collectionName = newName;
    }

    @Property(
            hidden = true
    )
    public String getDescription() {
        return null;
    }

    public MGDatabase getParentObject() {
        return this.database;
    }

    public @NotNull DBSEntityType getEntityType() {
        return DBSEntityType.TABLE;
    }

    public @NotNull List<? extends MGCollectionAttribute> getAttributes(@NotNull DBRProgressMonitor monitor) throws DBCException {
        return Collections.emptyList();
    }

    public DBSEntityAttribute getAttribute(@NotNull DBRProgressMonitor monitor, @NotNull String attributeName) throws DBCException {
        return attributeName.equals("_id") ? this.attrId : super.getAttribute(monitor, attributeName);
    }

    public boolean isView() {
        return false;
    }

    public Collection<? extends DBSTableIndex> getIndexes(DBRProgressMonitor monitor) throws DBException {
        return this.indexCache.getAllObjects(monitor, this);
    }

    public @Nullable List<? extends DBSTrigger> getTriggers(@NotNull DBRProgressMonitor monitor) throws DBException {
        return null;
    }

    public boolean isPersisted() {
        return this.persisted;
    }

    public void setPersisted(boolean persisted) {
        this.persisted = persisted;
    }

    public @NotNull String getFullyQualifiedName(DBPEvaluationContext context) {
        return DBUtils.getQuotedIdentifier(this.database) + "." + DBUtils.getQuotedIdentifier(this);
    }

    public String[] getSupportedFeatures() {
        return new String[]{"data.count", "data.filter", "data.insert", "data.update", "data.delete"};
    }

    @Override
    public DBCStatistics readData(DBCExecutionSource source, DBCSession session, DBDDataReceiver dataReceiver, DBDDataFilter dataFilter, long firstRow, long maxRows, long flags, int fetchSize, int stage, List<Object> data) throws DBCException {
        DBCStatistics var21;
        try (dataReceiver) {
            MGSession mongoSession = (MGSession) session;
            DBCStatistics statistics = new DBCStatistics();
            session.getProgressMonitor().subTask(ModelMessages.model_jdbc_fetch_table_data);

            try (MGSelectStatement statement = new MGSelectStatement(mongoSession, this, dataFilter)) {
                statement.setStatementSource(source);
                statement.setLimit(firstRow, maxRows);
                statement.setResultsFetchSize(fetchSize);
                statistics.setQueryText(statement.getQueryString());
                statistics.addStatementsCount();
                if (statement.executeStatement()) {
                    MGResultSet resultSet = statement.openResultSet();
                    if (resultSet != null) {
                        try {
                            dataReceiver.fetchStart(mongoSession, resultSet, firstRow, maxRows);
                            DBFetchProgress fetchProgress = new DBFetchProgress(session.getProgressMonitor());

                            while (true) {
                                if (!resultSet.nextRow() || fetchProgress.isCanceled()) {
                                    fetchProgress.dumpStatistics(statistics);
                                    break;
                                }

                                dataReceiver.fetchRow(mongoSession, resultSet);
                                fetchProgress.monitorRowFetch();
                            }
                        } finally {
                            dataReceiver.fetchEnd(mongoSession, resultSet);
                            resultSet.close();
                        }
                    }
                }
            }

            var21 = statistics;
        } catch (Throwable var45) {
            throw new DBCException(var45, session.getExecutionContext());
        }

        return var21;
    }

    public long countData(@NotNull DBCExecutionSource source, @NotNull DBCSession session, @Nullable DBDDataFilter dataFilter, long flags) throws DBCException {
        try {
            return this.getMongoCollection((MGSession) session).countDocuments(MongoSQLUtils.makeQueryFromFilter((MGDataSource) this.getDataSource(), dataFilter));
        } catch (MongoException var7) {
            throw new DBCException(var7, session.getExecutionContext());
        }
    }

    @NotNull
    public DBSDataManipulator.ExecuteBatch insertData(@NotNull DBCSession session, @NotNull DBSAttributeBase[] attributes, @Nullable DBDDataReceiver keysReceiver, final @NotNull DBCExecutionSource source, Map<String, Object> options) throws DBCException {
        final MGSession mongoSession = (MGSession) session;
        return new ExecuteBatchImpl(attributes, null, true) {
            protected @NotNull DBCStatement prepareStatement(@NotNull DBCSession session, DBDValueHandler[] handlers, Object[] attributeValues, Map<String, Object> options) throws DBCException {
                MGInsertStatement statement = new MGInsertStatement(mongoSession, MGCollection.this, this.attributes, attributeValues);
                statement.setStatementSource(source);
                return statement;
            }

            protected void bindStatement(@NotNull DBDValueHandler[] handlers, @NotNull DBCStatement statement, Object[] attributeValues) throws DBCException {
                ((MGInsertStatement) statement).addRowValues(attributeValues);
            }
        };
    }

    @NotNull
    public DBSDataManipulator.ExecuteBatch updateData(@NotNull DBCSession session, final @NotNull DBSAttributeBase[] updateAttributes, final @NotNull DBSAttributeBase[] keyAttributes, @Nullable DBDDataReceiver keysReceiver, final @NotNull DBCExecutionSource source) throws DBCException {
        DBSAttributeBase[] attributes = ArrayUtils.concatArrays(updateAttributes, keyAttributes);
        final MGSession mongoSession = (MGSession) session;
        return new ExecuteBatchImpl(attributes, keysReceiver, true) {
            protected @NotNull DBCStatement prepareStatement(@NotNull DBCSession session, DBDValueHandler[] handlers, Object[] attributeValues, Map<String, Object> options) throws DBCException {
                MGUpdateStatement statement = new MGUpdateStatement(mongoSession, MGCollection.this, updateAttributes, keyAttributes, attributeValues);
                statement.setStatementSource(source);
                return statement;
            }

            protected void bindStatement(@NotNull DBDValueHandler[] handlers, @NotNull DBCStatement statement, Object[] attributeValues) throws DBCException {
                ((MGUpdateStatement) statement).addRowValues(attributeValues);
            }
        };
    }

    @NotNull
    public DBSDataManipulator.ExecuteBatch deleteData(@NotNull DBCSession session, final @NotNull DBSAttributeBase[] keyAttributes, final @NotNull DBCExecutionSource source) throws DBCException {
        final MGSession mongoSession = (MGSession) session;
        return new ExecuteBatchImpl(keyAttributes, (DBDDataReceiver) null, true) {
            protected @NotNull DBCStatement prepareStatement(@NotNull DBCSession session, DBDValueHandler[] handlers, Object[] attributeValues, Map<String, Object> options) throws DBCException {
                MGDeleteStatement statement = new MGDeleteStatement(mongoSession, MGCollection.this, keyAttributes, attributeValues);
                statement.setStatementSource(source);
                return statement;
            }

            protected void bindStatement(@NotNull DBDValueHandler[] handlers, @NotNull DBCStatement statement, Object[] attributeValues) throws DBCException {
                ((MGDeleteStatement) statement).setRowValues(attributeValues);
            }
        };
    }

    public @NotNull DBCStatistics truncateData(@NotNull DBCSession session, @NotNull DBCExecutionSource source) throws DBCException {

        try {
            DBSDataManipulator.ExecuteBatch batch = this.deleteData(session, new DBSAttributeBase[0], source);

            DBCStatistics statistics = new DBCStatistics();
            try {
                batch.execute(session, Collections.emptyMap(), statistics);
            } finally {
                if (batch != null) {
                    batch.close();
                }

            }

            return statistics;
        } catch (Throwable var11) {
            throw new DBCException(var11.getMessage(), var11);
        }
    }

    public @NotNull List<DBDLabelValuePair> getValueEnumeration(@NotNull DBCSession session, @NotNull DBSAttributeBase attribute, @Nullable String pattern, boolean calcCount, boolean caseInsensitive, int maxResults) throws DBException {
        if (!(session instanceof MGSession)) {
            return Collections.emptyList();
        } else {
            String attributeName = this.getAttributeName(attribute);
            List<Bson> pipeline = new ArrayList();
            if (pattern != null) {
                pipeline.add(Aggregates.match(Filters.regex(attributeName, SQLUtils.makeRegexFromLike(pattern), caseInsensitive ? "i" : "")));
            }

            if (calcCount) {
                pipeline.add(Aggregates.group("$" + attributeName, new BsonField[]{Accumulators.sum("cnt", 1)}));
            } else {
                pipeline.add(Aggregates.group("$" + attributeName, new BsonField[0]));
            }

            pipeline.add(Aggregates.limit(maxResults));
            pipeline.add(Aggregates.sort(Sorts.ascending(new String[]{"_id"})));

            try {
                List<DBDLabelValuePair> result = new ArrayList();

                for (Document document : this.getMongoCollection((MGSession) session).aggregate(pipeline)) {
                    Object value = document.get("_id");
                    if (calcCount) {
                        result.add(new DBDLabelValuePairExt("", value, (long) document.getInteger("cnt")));
                    } else {
                        result.add(new DBDLabelValuePair("", value));
                    }
                }

                return result;
            } catch (MongoServerException var13) {
                throw new DBException(var13, session.getDataSource());
            }
        }
    }

    public String getAttributeName(DBSAttributeBase attribute) {
        return DBUtils.getObjectFullName(attribute, DBPEvaluationContext.DML);
    }

    public String getAttributeValue(DBSAttributeBase attribute, Object value) {
        if (!(value instanceof Number) && !(value instanceof Boolean)) {
            if (value instanceof Date) {
                String formattedTimestamp;
                try {
                    DBDDataFormatter formatter = ((MGDataSource) this.getDataSource()).getContainer().getDataFormatterProfile().createFormatter("timestamp", attribute);
                    formattedTimestamp = formatter.formatValue(value);
                } catch (Exception var5) {
                    formattedTimestamp = (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS")).format(value);
                }

                return "{ts '" + formattedTimestamp + "'}";
            } else {
                return value instanceof MGDocument ? String.valueOf(((MGDocument) value).getDocumentId()) : "'" + value + "'";
            }
        } else {
            return value.toString();
        }
    }

    @PropertyGroup(
            category = "Statistics"
    )
    @LazyProperty(
            cacheValidator = StatsValidator.class
    )
    public Stats getStats(DBRProgressMonitor monitor) throws DBCException {
        synchronized (this.stats) {
            if (!this.stats.loaded) {
                try (MGSession session = DBUtils.openMetaSession(monitor, this, "Read stats")) {
                    Document result = this.database.getDatabase(session).runCommand(new Document("collStats", this.getName()));
                    this.stats.count = CommonUtils.toLong(result.get("count"));
                    this.stats.size = CommonUtils.toLong(result.get("size"));
                    this.stats.avgObjSize = CommonUtils.toLong(result.get("avgObjSize"));
                    this.stats.storageSize = CommonUtils.toLong(result.get("storageSize"));
                    this.stats.numExtents = CommonUtils.toLong(result.get("numExtents"));
                    this.stats.lastExtentSize = CommonUtils.toLong(result.get("lastExtentSize"));
                    this.stats.paddingFactor = CommonUtils.toLong(result.get("paddingFactor"));
                    this.stats.flags = CommonUtils.toLong(result.get("flags"));
                    this.stats.totalIndexSize = CommonUtils.toLong(result.get("totalIndexSize"));
                    this.stats.totalSize = CommonUtils.toLong(result.get("totalSize"));
                    this.stats.loaded = true;
                }
            }

            return this.stats;
        }
    }

    public boolean isSystem() {
        return this.collectionName.startsWith("system.");
    }

    public String toString() {
        return this.getName();
    }

    public static class DocumentAttribute extends MGCollectionAttribute implements DBPQualifiedObject {
        DocumentAttribute(MGCollection collection) {
            super(collection, "##document", 0);
        }

        public String getTypeName() {
            return "json";
        }

        public int getTypeID() {
            return 2;
        }

        public DBPDataKind getDataKind() {
            return DBPDataKind.DOCUMENT;
        }

        public boolean isRequired() {
            return false;
        }

        public boolean isAutoGenerated() {
            return false;
        }

        public @NotNull String getFullyQualifiedName(DBPEvaluationContext context) {
            return context == DBPEvaluationContext.UI ? "Document" : "##document";
        }
    }

    public static class IdAttribute extends MGCollectionAttribute {
        IdAttribute(MGCollection collection) {
            super(collection, "_id", 0);
        }

        public String getTypeName() {
            return "ObjectId";
        }

        public int getTypeID() {
            return 1;
        }

        public DBPDataKind getDataKind() {
            return DBPDataKind.ROWID;
        }

        public boolean isRequired() {
            return true;
        }

        public boolean isAutoGenerated() {
            return true;
        }
    }

    class IndexCache extends AbstractObjectCache<MGCollection, MGIndex> {
        public @NotNull Collection<MGIndex> getAllObjects(@NotNull DBRProgressMonitor monitor, @Nullable MGCollection collection) throws DBException {
            if (!this.isFullyCached()) {

                try (MGSession session = DBUtils.openMetaSession(monitor, MGCollection.this, "Read indexes")) {
                    List<MGIndex> indexes = new ArrayList<>();
                    for (Document indexInfo : MGCollection.this.getMongoCollection(session).listIndexes()) {
                        MGIndex index = new MGIndex(monitor, collection, indexInfo);
                        indexes.add(index);
                    }

                    this.setCache(indexes);
                }

            }

            return this.getCachedObjects();
        }

        public MGIndex getObject(@NotNull DBRProgressMonitor monitor, @Nullable MGCollection casKeyspace, @NotNull String name) throws DBException {
            return (MGIndex) DBUtils.findObject(this.getAllObjects(monitor, casKeyspace), name);
        }
    }

    public static class MetaAttribute extends MGCollectionAttribute {
        private final MGDataType type;

        MetaAttribute(MGCollection collection, String name, MGDataType type, int position) {
            super(collection, name, position);
            this.type = type;
        }

        @Property(
                viewable = true,
                editable = false,
                order = 2
        )
        public String getTypeName() {
            return this.type.getName();
        }

        public int getTypeID() {
            return this.type.getTypeID();
        }

        public DBPDataKind getDataKind() {
            return this.type.getDataKind();
        }

        public boolean isRequired() {
            return false;
        }

        public boolean isAutoGenerated() {
            return false;
        }
    }

    public static class Stats {
        private boolean loaded;
        private Long count;
        private long size;
        private long avgObjSize;
        private long storageSize;
        private long numExtents;
        private long lastExtentSize;
        private long paddingFactor;
        private long flags;
        private long totalIndexSize;
        private long totalSize;

        @Property(
                viewable = true,
                order = 21,
                category = "Statistics"
        )
        public Long getCount() {
            return this.count;
        }

        @Property(
                viewable = true,
                order = 22,
                category = "Statistics"
        )
        public long getSize() {
            return this.size;
        }

        @Property(
                viewable = false,
                order = 23,
                category = "Statistics"
        )
        public long getAvgObjSize() {
            return this.avgObjSize;
        }

        @Property(
                viewable = false,
                order = 24,
                category = "Statistics"
        )
        public long getStorageSize() {
            return this.storageSize;
        }

        @Property(
                viewable = false,
                order = 25,
                category = "Statistics"
        )
        public long getNumExtents() {
            return this.numExtents;
        }

        @Property(
                viewable = false,
                order = 26,
                category = "Statistics"
        )
        public long getLastExtentSize() {
            return this.lastExtentSize;
        }

        @Property(
                viewable = false,
                order = 27,
                category = "Statistics"
        )
        public long getPaddingFactor() {
            return this.paddingFactor;
        }

        @Property(
                viewable = false,
                order = 28,
                category = "Statistics"
        )
        public long getFlags() {
            return this.flags;
        }

        @Property(
                viewable = false,
                order = 29,
                category = "Statistics"
        )
        public long getTotalIndexSize() {
            return this.totalIndexSize;
        }

        @Property(
                viewable = false,
                order = 30,
                category = "Statistics"
        )
        public long getTotalSize() {
            return this.totalSize;
        }
    }

    public static class StatsValidator implements IPropertyCacheValidator<MGCollection> {
        public boolean isPropertyCached(MGCollection object, Object propertyId) {
            return object.stats.loaded;
        }
    }
}
