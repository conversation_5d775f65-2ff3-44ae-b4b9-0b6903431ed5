package com.dc.summer.ext.mongodb.data;

import com.dc.summer.ext.mongodb.data.format.*;
import com.dc.summer.ext.mongodb.data.format.Long;
import com.dc.summer.ext.mongodb.model.MGTypeConverters;
import com.dc.summer.model.data.json.JSONUtils;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.bson.*;
import org.bson.types.Binary;

import java.io.Writer;
import java.util.Date;

public class MGDataUtils {

    public static final Gson BSON_BUILDER = new GsonBuilder()
            .disableHtmlEscaping()
            .serializeSpecialFloatingPointValues()
            .registerTypeAdapter(BinData.class, new MGTypeConverters.FunctionObjectTypeConverter())
            .registerTypeAdapter(Code.class, new MGTypeConverters.FunctionObjectTypeConverter())
            .registerTypeAdapter(Decimal128.class, new MGTypeConverters.FunctionObjectTypeConverter())
            .registerTypeAdapter(ISODate.class, new MGTypeConverters.FunctionObjectTypeConverter())
            .registerTypeAdapter(Long.class, new MGTypeConverters.FunctionObjectTypeConverter())
            .registerTypeAdapter(MaxKey.class, new MGTypeConverters.FunctionObjectTypeConverter())
            .registerTypeAdapter(MinKey.class, new MGTypeConverters.FunctionObjectTypeConverter())
            .registerTypeAdapter(ObjectId.class, new MGTypeConverters.FunctionObjectTypeConverter())
            .registerTypeAdapter(RegExp.class, new MGTypeConverters.FunctionObjectTypeConverter())
            .registerTypeAdapter(Timestamp.class, new MGTypeConverters.FunctionObjectTypeConverter())
            .create();

    public static void serializeDocument(Object document, Writer writer) {
        BSON_BUILDER.toJson(document, writer);
    }

    public static Object formatResultSet(Object result) {
        if (result instanceof Date) {
            return new ISODate((Date) result);
        } else if (result instanceof org.bson.types.ObjectId) {
            return new ObjectId((org.bson.types.ObjectId) result);
        } else if (result instanceof Binary) {
            return new BinData((Binary) result);
        } else if (result instanceof org.bson.types.Code) {
            return new Code((org.bson.types.Code) result);
        } else if (result instanceof java.lang.Long) {
            return new Long((java.lang.Long) result);
        } else if (result instanceof org.bson.types.Decimal128) {
            return new Decimal128((org.bson.types.Decimal128) result);
        } else if (result instanceof org.bson.types.MaxKey) {
            return new MaxKey((org.bson.types.MaxKey) result);
        } else if (result instanceof org.bson.types.MinKey) {
            return new MinKey((org.bson.types.MinKey) result);
        } else if (result instanceof BsonRegularExpression) {
            return new RegExp((BsonRegularExpression) result);
        } else if (result instanceof BsonTimestamp) {
            return new Timestamp((BsonTimestamp) result);
        } else if (result instanceof BsonUndefined) {
            return new Undefined((BsonUndefined) result);
        } else {
            return result;
        }
    }

    public static Object readBsonTypeValue(BsonReader reader) {
        Object value = null;
        // 根据元素类型读取数据
        switch (reader.getCurrentBsonType()) {
            case INT32:
                value = reader.readInt32();
                break;
            case INT64:
                value = reader.readInt64();
                break;
            case DOUBLE:
                value = MGDataUtils.formatResultSet(reader.readDouble());
                break;
            case STRING:
            case DOCUMENT:
            case UNDEFINED:
            case NULL:
                value = reader.readString();
                break;
            case BINARY:
                BsonBinary bsonBinary = reader.readBinaryData();
                Binary binary = new Binary(bsonBinary.getType(), bsonBinary.getData());
                value = binary;
                break;
            case ARRAY:
                reader.readStartArray();
                // 逐个读取数组元素
                while (reader.readBsonType() != BsonType.END_OF_DOCUMENT) {
                    value = readBsonTypeValue(reader);
                }
                break;
            case MIN_KEY:
                value = new MinKey("");
                break;
            case MAX_KEY:
                value = new MaxKey("");
                break;
            case BOOLEAN:
                value = reader.readBoolean();
                break;
            case OBJECT_ID:
                value = MGDataUtils.formatResultSet(reader.readObjectId());
                break;
            case DATE_TIME:
                long dateTime = reader.readDateTime();
                value = MGDataUtils.formatResultSet(new Date(dateTime));
                break;
            case JAVASCRIPT:
                value = new org.bson.types.Code(reader.readJavaScript());
                break;
            case REGULAR_EXPRESSION:
                value = MGDataUtils.formatResultSet(reader.readRegularExpression());
                break;
            case DB_POINTER:
                value = MGDataUtils.formatResultSet(reader.readDBPointer());
                break;
            case SYMBOL:
                value = reader.readSymbol();
                break;
            case JAVASCRIPT_WITH_SCOPE:
                value = reader.readJavaScriptWithScope();
                break;
            case TIMESTAMP:
                value = MGDataUtils.formatResultSet(reader.readTimestamp());
                break;
            case DECIMAL128:
                value = MGDataUtils.formatResultSet(reader.readDecimal128());
                break;
            // 其他数据类型的读取方法类似
            default:
                reader.skipValue();
                break;
        }
        return value;
    }

    public static Object formatObject(String type, String value) {
        if (BinData.class.getSimpleName().equals(type)) {
            return new BinData(value);
        } else if (Code.class.getSimpleName().equals(type)) {
            return new Code(value);
        } else if (Decimal128.class.getSimpleName().equals(type)) {
            return new Decimal128(value);
        } else if (ISODate.class.getSimpleName().equals(type)) {
            return new ISODate(value);
        } else if (Long.class.getSimpleName().equals(type)) {
            return new Long(value);
        } else if (Integer.class.getSimpleName().equals(type)) {
            return Integer.valueOf(value);
        } else if (ObjectId.class.getSimpleName().equals(type)) {
            return new ObjectId(value);
        } else if (RegExp.class.getSimpleName().equals(type)) {
            return new RegExp(value);
        } else if (Timestamp.class.getSimpleName().equals(type)) {
            return new Timestamp(value);
        } else if (MaxKey.class.getSimpleName().equals(type)) {
            return new MaxKey(value);
        } else if (MinKey.class.getSimpleName().equals(type)) {
            return new MinKey(value);
        } else if (Undefined.class.getSimpleName().equals(type)) {
            return new Undefined(value);
        } else {
            return JSONUtils.escapeJsonString(value);
        }
    }

}
