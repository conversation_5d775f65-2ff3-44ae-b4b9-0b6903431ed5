package com.dc.summer.ext.mongodb.data.format;

import com.dc.summer.model.document.data.format.DBFunctionObject;
import com.dc.utils.StringUtils;

public class ObjectId implements DBFunctionObject {

    private final org.bson.types.ObjectId objectId;

    public ObjectId(org.bson.types.ObjectId objectId) {
        this.objectId = objectId;
    }

    public ObjectId(String str) {
        String objectId = StringUtils.substring(str, "ObjectId(\"", -2);
        this.objectId = new org.bson.types.ObjectId(objectId);
    }

    @Override
    public String toString() {
        return String.format("ObjectId(\"%s\")", objectId);
    }
}
