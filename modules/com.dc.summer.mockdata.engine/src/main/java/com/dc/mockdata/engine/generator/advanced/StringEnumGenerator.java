package com.dc.mockdata.engine.generator.advanced;

import com.dc.mockdata.engine.generator.AbstractMockValueGenerator;
import com.dc.summer.DBException;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSAttributeBase;
import com.dc.summer.model.struct.DBSDataManipulator;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 枚举生成器
 */
@Slf4j
public class StringEnumGenerator extends AbstractMockValueGenerator {

    private List<String> enumStrings;

    private int enumStringSize;

    @Override
    public void init(DBSDataManipulator container, DBSAttributeBase attribute, Map<String, Object> properties) throws DBException {
        super.init(container, attribute, properties);
        enumStrings = (List<String>) properties.get("enumKey");
        enumStringSize = enumStrings.size();
    }

    @Override
    protected Object generateOneValue(DBRProgressMonitor var1) throws DBException, IOException {
        if (enumStringSize == 0) {
            return null;
        }
        return this.isGenerateNULL() ? null : enumStrings.get(this.random.nextInt(enumStringSize));
    }
}
