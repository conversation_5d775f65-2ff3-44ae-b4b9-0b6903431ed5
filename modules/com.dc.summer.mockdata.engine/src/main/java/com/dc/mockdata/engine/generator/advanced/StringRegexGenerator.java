package com.dc.mockdata.engine.generator.advanced;

import com.dc.mockdata.engine.generator.AbstractStringValueGenerator;
import com.dc.mockdata.engine.generator.advanced.regex.Xeger;
import com.dc.summer.DBException;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSAttributeBase;
import com.dc.summer.model.struct.DBSDataManipulator;
import com.dc.utils.CommonUtils;

import java.io.IOException;
import java.util.Map;

/**
 * 目前只用于生成ipv4地址
 */
public class StringRegexGenerator extends AbstractStringValueGenerator {

    private Xeger xeger;

    private Map<String, String> algorithmMap = Map.of(
            "stringIP4Generator", "(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])"
    );

    public StringRegexGenerator() {
    }

    @Override
    public void init(DBSDataManipulator container, DBSAttributeBase attribute, Map<String, Object> properties) throws DBException {
        super.init(container, attribute, properties);
        String algorithmId = CommonUtils.toString(properties.get("id"));
        String regex = algorithmMap.get(algorithmId);
        if (CommonUtils.isEmpty(regex)) {
            regex = "[a-zA-Z0-9]*";
        }

        this.xeger = new Xeger(regex);
    }

    @Override
    protected Object generateOneValue(DBRProgressMonitor monitor) throws DBException, IOException {
        return this.isGenerateNULL() ? null : this.tune(this.xeger.generate());
    }
}
