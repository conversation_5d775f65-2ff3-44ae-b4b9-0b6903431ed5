package com.dc.mockdata.engine.generator.advanced.markovchain;

import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Deque;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.Set;
import java.util.Map.Entry;

public final class MarkovChain {

    private final int wordsPerState;
    private final String[] words;
    private final Map<List<String>, Map<List<String>, Integer>> map = new HashMap<>();
    private final Map<List<String>, Integer> totalCountMap = new HashMap<>();
    private final List<List<String>> vocabulary = new ArrayList<>();
    private final Random random;

    public MarkovChain(String[] words, int wordsPerState, Random random) {
        this.words = Objects.requireNonNull(words, "Word array is null.");
        this.wordsPerState = this.checkPositive(wordsPerState);
        if (words.length < wordsPerState) {
            throw new IllegalArgumentException("number of words < k");
        } else {
            this.random = Objects.requireNonNull(random, "The random is null.");
            this.build();
        }
    }

    public MarkovChain(String[] words, int wordsPerState) {
        this(words, wordsPerState, new Random());
    }

    public Random getRandom() {
        return this.random;
    }

    public String[] compose(int numberOfWords) {
        this.checkRequestedNumberOfWords(numberOfWords);
        List<String> startState = this.vocabulary.get(this.random.nextInt(this.vocabulary.size()));
        String[] outputWords = new String[numberOfWords];
        numberOfWords -= this.wordsPerState;

        for (int i = 0; i < startState.size(); i++) {
            outputWords[i] = startState.get(i);
        }

        int index = this.wordsPerState;

        while (numberOfWords-- > 0) {
            List<String> nextState = this.randomTransition(startState);
            outputWords[index++] = lastOf(nextState);
            startState = nextState;
        }

        return outputWords;
    }

    private static <T> T lastOf(List<T> list) {
        return list.get(list.size() - 1);
    }

    private List<String> randomTransition(List<String> startState) {
        Map<List<String>, Integer> localMap = this.map.get(startState);
        if (localMap == null) {
            return this.vocabulary.get(this.random.nextInt(this.vocabulary.size()));
        } else {
            int choices = this.totalCountMap.get(startState);
            int coin = this.random.nextInt(choices);

            for (Entry<List<String>, Integer> entry : localMap.entrySet()) {
                if (coin < entry.getValue()) {
                    return entry.getKey();
                }

                coin -= entry.getValue();
            }

            throw new IllegalStateException("Should not get here");
        }
    }

    private void build() {
        Set<List<String>> filter = new HashSet<>();
        Deque<String> wordDeque = new ArrayDeque<>();

        for (int i = 0; i < this.wordsPerState; i++) {
            wordDeque.addLast(this.words[i]);
        }

        for (int i = this.wordsPerState; i < this.words.length; i++) {
            List<String> startSentence = new ArrayList<>(wordDeque);
            filter.add(startSentence);
            wordDeque.removeFirst();
            wordDeque.addLast(this.words[i]);
            List<String> nextSentence = new ArrayList<>(wordDeque);
            Map<List<String>, Integer> localMap = this.map.get(startSentence);
            if (localMap == null) {
                this.map.put(startSentence, localMap = new HashMap<>());
            }

            localMap.put(nextSentence, localMap.getOrDefault(nextSentence, 0) + 1);
            this.totalCountMap.put(startSentence, this.totalCountMap.getOrDefault(startSentence, 0) + 1);
        }

        this.vocabulary.addAll(filter);
    }

    private int checkPositive(int k) {
        if (k < 1) {
            throw new IllegalArgumentException("k < 1");
        } else {
            return k;
        }
    }

    private void checkRequestedNumberOfWords(int numberOfWords) {
        if (numberOfWords < this.wordsPerState) {
            throw new IllegalArgumentException("The minimum number of words for composition should be " + this.wordsPerState + ". Received " + numberOfWords);
        }
    }
}
