package com.dc.mockdata.engine.generator;

import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSAttributeBase;
import com.dc.summer.model.struct.DBSDataManipulator;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

public class DateSequenceGenerator extends AbstractMockValueGenerator {

    private static final Log log = Log.getLog(DateSequenceGenerator.class);

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("dd-MM-yyyy");

    public static final long DAY_RANGE = 24 * 60 * 60 * 1000; // 1 day

    private long startDate = Long.MAX_VALUE;
    private boolean reverse = false;
    private long step = DAY_RANGE; // in ms

    public DateSequenceGenerator() {
    }

    @Override
    public void init(DBSDataManipulator container, DBSAttributeBase attribute, Map<String, Object> properties) throws DBException {
        super.init(container, attribute, properties);

        String fromDate = (String) properties.get("startDate"); //$NON-NLS-1$
        if (fromDate != null) {
            try {
                this.startDate = DATE_FORMAT.parse(fromDate).getTime();
            } catch (ParseException e) {
                log.error("Error parse Start Date '" + fromDate + "'.", e);
            }
        }

        // default is today
        if (startDate == Long.MAX_VALUE) {
            startDate = new Date().getTime();
        }

        Integer step = (Integer) properties.get("step"); //$NON-NLS-1$
        if (step != null) {
            this.step = step * DAY_RANGE;
        }

        Boolean reverse = (Boolean) properties.get("reverse"); //$NON-NLS-1$
        if (reverse != null) {
            this.reverse = reverse;
        }
    }

    @Override
    public Object generateOneValue(DBRProgressMonitor monitor) throws DBException, IOException {
        if (isGenerateNULL()) {
            return null;
        } else {
            long value = this.startDate;
            if (reverse) {
                startDate -= step;
            } else {
                startDate += step;
            }

            return new Date(value);
        }
    }
}
