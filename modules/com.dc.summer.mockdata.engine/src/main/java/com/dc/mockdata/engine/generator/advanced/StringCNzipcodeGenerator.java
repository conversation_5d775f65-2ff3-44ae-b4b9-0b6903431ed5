package com.dc.mockdata.engine.generator.advanced;

import com.dc.summer.DBException;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.List;

/**
 * 中国邮编
 * <AUTHOR>
 */
@Slf4j
public class StringCNzipcodeGenerator extends AdvancedStringValueGenerator {

    private static List<String> ZIPCODES;

    private static int zipcodes;

    public StringCNzipcodeGenerator() {
    }

    @Override
    public Object generateOneValue(DBRProgressMonitor monitor) throws DBException, IOException {
        if (zipcodes == 0) {
            ZIPCODES = this.readDict("Chinese_zipcode.txt");
            zipcodes = ZIPCODES.size();
        }

        return this.isGenerateNULL() ? null : ZIPCODES.get(this.random.nextInt(zipcodes));
    }
}
