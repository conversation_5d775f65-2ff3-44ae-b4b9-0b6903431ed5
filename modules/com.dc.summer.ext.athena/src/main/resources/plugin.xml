<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.2"?>

<plugin>

    <extension point="com.dc.summer.dataSourceProvider">

        <!-- Athena -->

        <datasource
                class="com.dc.summer.ext.athena.AthenaDataSourceProvider"
                description="%datasource.athena.description"
                id="athena"
                parent="generic"
                label="Athena"
                icon="icons/athena_icon.png"
                dialect="aws_athena">
            <drivers managable="true">

                <!-- Simba JDBC driver -->
                <driver
                        id="aws_athena_jdbc_simba"
                        label="Athena"
                        icon="icons/athena_icon.png"
                        iconBig="icons/athena_icon_big.png"
                        class="com.simba.athena.jdbc.Driver"
                        sampleURL="***********************={region};"
                        defaultPort="444"
                        customEndpoint="true"
                        description="%driver.athena.description"
                        category="AWS"
                        webURL="https://docs.aws.amazon.com/athena/latest/ug/connect-with-jdbc.html"
                        propertiesURL="https://s3.amazonaws.com/athena-downloads/drivers/JDBC/SimbaAthenaJDBC-2.0.25.1001/doc/Simba+Athena+JDBC+Connector+Install+and+Configuration+Guide.pdf"
                        categories="bigdata,aws">
                    <replace provider="generic" driver="aws_athena_42"/>
                    <file type="jar" path="https://s3.amazonaws.com/athena-downloads/drivers/JDBC/SimbaAthenaJDBC-2.0.32.1000/AthenaJDBC42.jar" bundle="!drivers.athena"/>
                    <file type="license" path="https://s3.amazonaws.com/athena-downloads/drivers/JDBC/SimbaAthenaJDBC-2.0.32.1000/docs/LICENSE.txt" bundle="!drivers.athena"/>

                    <file type="jar" path="drivers/athena" bundle="drivers.athena"/>
                    <file type="license" path="drivers/athena/LICENSE.txt" bundle="drivers.athena"/>

                    <parameter name="supports-references" value="false"/>
                    <parameter name="supports-indexes" value="false"/>
                    <parameter name="omit-catalog" value="true"/>
                    <property name="@summer-default-resultset.maxrows.sql" value="true"/>
                    <parameter name="read-only-data" value="true"/>
                    <parameter name="read-only-meta-data" value="true"/>
                </driver>

                <provider-properties drivers="*">
                    <propertyGroup label="Settings">
                        <property id="AwsRegion" label="Region" type="string" description="Athena AWS Region" defaultValue="us-west-1" validValues="us-east-1,us-east-2,us-west-1,us-west-2,ca-central-1,eu-central-1,eu-west-1,eu-west-2,eu-west-3,ap-northeast-1,ap-northeast-2,ap-northeast-3,ap-southeast-1,ap-southeast-2,ap-south-1,sa-east-1,cn-north-1,cn-northwest-1"/>
                        <property id="S3OutputLocation" label="S3 Location" type="string" description="Athena AWS S3 Location"/>
                    </propertyGroup>
                </provider-properties>
            </drivers>

        </datasource>
    </extension>

    <extension point="com.dc.summer.sqlDialect">
        <dialect id="aws_athena" parent="generic" class="com.dc.summer.ext.athena.model.AthenaSQLDialect" label="Athena" description="AWS Athena dialect." icon="icons/athena_icon.png">
        </dialect>
    </extension>

</plugin>
