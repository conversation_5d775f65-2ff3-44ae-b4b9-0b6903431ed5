package com.dc.parser.exec.engine.segment.dml.projection.type;

import com.dc.infra.utils.CaseInsensitiveMap.CaseInsensitiveString;
import com.dc.parser.exec.engine.segment.dml.expression.type.SubquerySegmentBinder;
import com.dc.parser.exec.engine.segment.dml.from.context.TableSegmentBinderContext;
import com.dc.parser.exec.engine.statement.SQLStatementBinderContext;
import com.dc.parser.model.segment.dml.expr.subquery.SubquerySegment;
import com.dc.parser.model.segment.dml.item.SubqueryProjectionSegment;
import com.google.common.collect.Multimap;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * Subquery projection segment binder.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class SubqueryProjectionSegmentBinder {

    /**
     * Bind subquery projection segment.
     *
     * @param segment             subquery projection segment
     * @param binderContext       SQL statement binder context
     * @param tableBinderContexts table binder contexts
     * @return bound subquery projection segment
     */
    public static SubqueryProjectionSegment bind(final SubqueryProjectionSegment segment,
                                                 final SQLStatementBinderContext binderContext, final Multimap<CaseInsensitiveString, TableSegmentBinderContext> tableBinderContexts) {
        SubquerySegment boundSubquerySegment = SubquerySegmentBinder.bind(segment.getSubquery(), binderContext, tableBinderContexts);
        SubqueryProjectionSegment result = new SubqueryProjectionSegment(boundSubquerySegment, segment.getText());
        segment.getAliasSegment().ifPresent(result::setAlias);
        return result;
    }
}
