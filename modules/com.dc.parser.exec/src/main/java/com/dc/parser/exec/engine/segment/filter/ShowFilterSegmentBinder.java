package com.dc.parser.exec.engine.segment.filter;

import com.dc.infra.utils.CaseInsensitiveMap.CaseInsensitiveString;
import com.dc.parser.exec.engine.segment.dml.from.context.TableSegmentBinderContext;
import com.dc.parser.exec.engine.segment.dml.predicate.WhereSegmentBinder;
import com.dc.parser.exec.engine.statement.SQLStatementBinderContext;
import com.dc.parser.model.segment.dal.ShowFilterSegment;
import com.google.common.collect.Multimap;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * Show filter segment binder.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ShowFilterSegmentBinder {

    /**
     * Bind show filter segment.
     *
     * @param segment                  show filter segment
     * @param binderContext            SQL statement binder context
     * @param tableBinderContexts      table binder contexts
     * @param outerTableBinderContexts outer table binder contexts
     * @return bound show filter segment
     */
    public static ShowFilterSegment bind(final ShowFilterSegment segment, final SQLStatementBinderContext binderContext,
                                         final Multimap<CaseInsensitiveString, TableSegmentBinderContext> tableBinderContexts,
                                         final Multimap<CaseInsensitiveString, TableSegmentBinderContext> outerTableBinderContexts) {
        ShowFilterSegment result = new ShowFilterSegment(segment.getStartIndex(), segment.getStopIndex());
        segment.getLike().ifPresent(result::setLike);
        segment.getWhere().ifPresent(optional -> result.setWhere(WhereSegmentBinder.bind(optional, binderContext, tableBinderContexts, outerTableBinderContexts)));
        return result;
    }
}
