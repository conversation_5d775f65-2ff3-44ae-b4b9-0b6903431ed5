package com.dc.parser.exec.check;

import com.dc.infra.database.type.DatabaseType;
import com.dc.infra.exception.ServiceProviderNotFoundException;
import com.dc.infra.spi.TypedSPI;
import com.dc.infra.spi.TypedSPILoader;
import com.dc.parser.exec.SQLCheckExecutor;
import com.dc.parser.exec.context.SQLContextEngine;
import com.dc.parser.exec.sql.SQLStatementParserEngine;
import com.dc.parser.exec.sql.SQLStatementParserEngineFactory;
import com.dc.parser.model.check.flex.SQLFlex;
import com.dc.parser.model.check.flex.auth.AllowListAuth;
import com.dc.parser.model.check.flex.auth.SQLAuth;
import com.dc.parser.model.check.flex.auth.SpecialAuth;
import com.dc.parser.model.context.SQLStatementContext;
import com.dc.parser.model.engine.CacheOption;
import com.dc.parser.model.metadata.ShardingSphereMetaData;
import com.dc.parser.model.statement.SQLStatement;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class SQLFlexCheckEngine<P, R, T extends SQLFlex<P, R>> {

    private String sql;
    private final DatabaseType databaseType;
    private final SQLCheckExecutor sqlCheckExecutor; // TODO
    private SQLStatement sqlStatement;
    private final String defaultSchema;
    private final Class<T> sqlCheckContextClass;
    private SQLStatementContext sqlStatementContext;

    private List<T> sqlCheckContextList = new ArrayList<>();

    public SQLFlexCheckEngine(String sql, DatabaseType.Constant constant, SQLCheckExecutor sqlCheckExecutor, String defaultSchema, Class<T> sqlCheckContextClass) {
        this.sql = sql;
        this.databaseType = TypedSPILoader.getService(DatabaseType.class, constant);
        this.sqlCheckExecutor = sqlCheckExecutor;
        this.defaultSchema = defaultSchema;
        this.sqlCheckContextClass = sqlCheckContextClass;

        init();
    }

    public SQLFlexCheckEngine(SQLStatement sqlStatement, DatabaseType.Constant constant, SQLCheckExecutor sqlCheckExecutor, String defaultSchema, Class<T> sqlCheckContextClass) {
        this.sqlStatement = sqlStatement;
        this.databaseType = TypedSPILoader.getService(DatabaseType.class, constant);
        this.sqlCheckExecutor = sqlCheckExecutor;
        this.defaultSchema = defaultSchema;
        this.sqlCheckContextClass = sqlCheckContextClass;

        init();
    }

    private void init() {
        if (sqlStatement == null) {
            SQLStatementParserEngine sqlStatementParserEngine = SQLStatementParserEngineFactory.getSQLStatementParserEngine(this.databaseType, new CacheOption(2000, 65535L), new CacheOption(1, 1L));
            this.sqlStatement = sqlStatementParserEngine.parse(this.sql, false);
        }
        //TODO 需要传入 metaData
        ShardingSphereMetaData metaData = null;
        sqlStatementContext = new SQLContextEngine(metaData, defaultSchema).bind(sqlStatement, null);

        if (SQLAuth.class.equals(sqlCheckContextClass)) {
            sqlCheckContextList.add((T) getChecker(AllowListAuth.class, databaseType.getType()));
            sqlCheckContextList.add((T) getChecker(SpecialAuth.class, databaseType.getType()));
        } else {
            sqlCheckContextList.add(getChecker(sqlCheckContextClass, databaseType.getType()));
        }
    }

    public R check(P parameter) {

        for (T t : sqlCheckContextList) {
            try {
                R check = t.check(sqlStatementContext, parameter);
                String simpleName = t.getClass().getSimpleName();
                if (check != null) {
                    log.debug("Flex Check ({}) -> {}", simpleName, check);
                    return check;
                }
                log.debug("Flex Check ({}) is null.", simpleName);
            } catch (Exception e) {
                log.error("Flex Check Error", e);
            }
        }

        return null;
    }

    public static <T extends TypedSPI> T getChecker(Class<T> clazz, DatabaseType.Constant constant) {

        String simpleName = clazz.getSimpleName();

        // find SQLFlex
        Class<?>[] interfaces = clazz.getInterfaces();
        if (interfaces != null) {
            t:
            for (Class<?> face : interfaces) {
                if (TypedSPI.class.isAssignableFrom(face)) {
                    clazz = (Class<T>) face;
                    break t;
                }
            }
        }

        if (constant != null) {
            try {
                return TypedSPILoader.getService(clazz, constant.getName() + simpleName);
            } catch (ServiceProviderNotFoundException e) {
                log.debug(e.getMessage());
            }
        }
        return TypedSPILoader.getService(clazz, simpleName);
    }

}
