package com.dc.parser.exec.engine.statement.dml;

import com.dc.infra.utils.CaseInsensitiveMap.CaseInsensitiveString;
import com.dc.parser.exec.engine.segment.dml.assign.AssignmentSegmentBinder;
import com.dc.parser.exec.engine.segment.dml.column.InsertColumnsSegmentBinder;
import com.dc.parser.exec.engine.segment.dml.expression.type.SubquerySegmentBinder;
import com.dc.parser.exec.engine.segment.dml.from.context.TableSegmentBinderContext;
import com.dc.parser.exec.engine.segment.dml.from.type.SimpleTableSegmentBinder;
import com.dc.parser.exec.engine.segment.dml.with.WithSegmentBinder;
import com.dc.parser.exec.engine.statement.SQLStatementBinder;
import com.dc.parser.exec.engine.statement.SQLStatementBinderContext;
import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.segment.dml.item.ColumnProjectionSegment;
import com.dc.parser.model.segment.dml.item.ProjectionSegment;
import com.dc.parser.model.statement.dml.InsertStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlActionModel;
import com.google.common.collect.LinkedHashMultimap;
import com.google.common.collect.Multimap;
import lombok.SneakyThrows;

import java.util.Collection;
import java.util.stream.Collectors;

/**
 * Insert statement binder.
 */
public final class InsertStatementBinder implements SQLStatementBinder<InsertStatement> {

    @Override
    public InsertStatement bind(final InsertStatement sqlStatement, final SQLStatementBinderContext binderContext) {
        InsertStatement result = copy(sqlStatement);
        Multimap<CaseInsensitiveString, TableSegmentBinderContext> tableBinderContexts = LinkedHashMultimap.create();
        sqlStatement.getWithSegment().ifPresent(optional -> {
            binderContext.setOperation(SqlConstant.KEY_SELECT);
            result.setWithSegment(WithSegmentBinder.bind(optional, binderContext));
        });
        sqlStatement.getTable().ifPresent(optional -> {
            binderContext.setOperation(SqlConstant.KEY_INSERT);
            result.setTable(SimpleTableSegmentBinder.bind(optional, binderContext, tableBinderContexts));
        });
        if (sqlStatement.getInsertColumns().isPresent() && !sqlStatement.getInsertColumns().get().getColumns().isEmpty()) {
            result.setInsertColumns(InsertColumnsSegmentBinder.bind(sqlStatement.getInsertColumns().get(), binderContext, tableBinderContexts));
        } else {
            sqlStatement.getInsertColumns().ifPresent(result::setInsertColumns);
            tableBinderContexts.values().forEach(each -> result.getDerivedInsertColumns().addAll(getVisibleColumns(each.getProjectionSegments())));
        }
        sqlStatement.getSetAssignment().ifPresent(optional -> result.setSetAssignment(AssignmentSegmentBinder.bind(optional, binderContext, tableBinderContexts, LinkedHashMultimap.create())));
        sqlStatement.getInsertSelect().ifPresent(optional -> {
            binderContext.setOperation(SqlConstant.KEY_SELECT);
            SqlActionModel sqlActionModel = binderContext.getSqlActionModel();
            sqlActionModel.setInsertIntoSelect(true);
            result.setInsertSelect(SubquerySegmentBinder.bind(optional, binderContext, tableBinderContexts));
        });
        return result;
    }

    @SneakyThrows(ReflectiveOperationException.class)
    private InsertStatement copy(final InsertStatement sqlStatement) {
        InsertStatement result = sqlStatement.getClass().getDeclaredConstructor().newInstance();
        result.getValues().addAll(sqlStatement.getValues());
        sqlStatement.getOnDuplicateKeyColumns().ifPresent(result::setOnDuplicateKeyColumns);
        sqlStatement.getOutputSegment().ifPresent(result::setOutputSegment);
        sqlStatement.getMultiTableInsertType().ifPresent(result::setMultiTableInsertType);
        //TODO 适配 Oracle insert all
        sqlStatement.getMultiTableInsertIntoSegment().ifPresent(result::setMultiTableInsertIntoSegment);
        sqlStatement.getMultiTableConditionalIntoSegment().ifPresent(result::setMultiTableConditionalIntoSegment);
        sqlStatement.getReturningSegment().ifPresent(result::setReturningSegment);
        result.addParameterMarkerSegments(sqlStatement.getParameterMarkerSegments());
        result.getCommentSegments().addAll(sqlStatement.getCommentSegments());
        result.getVariableNames().addAll(sqlStatement.getVariableNames());
        return result;
    }

    private Collection<ColumnSegment> getVisibleColumns(final Collection<ProjectionSegment> projectionSegments) {
        return projectionSegments.stream()
                .filter(each -> each instanceof ColumnProjectionSegment && each.isVisible()).map(each -> ((ColumnProjectionSegment) each).getColumn()).collect(Collectors.toList());
    }
}
