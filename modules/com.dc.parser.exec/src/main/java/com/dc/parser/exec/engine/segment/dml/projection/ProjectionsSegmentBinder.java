package com.dc.parser.exec.engine.segment.dml.projection;

import com.dc.infra.utils.CaseInsensitiveMap.CaseInsensitiveString;
import com.dc.parser.exec.engine.segment.SegmentType;
import com.dc.parser.exec.engine.segment.dml.expression.ExpressionSegmentBinder;
import com.dc.parser.exec.engine.segment.dml.from.context.TableSegmentBinderContext;
import com.dc.parser.exec.engine.segment.dml.from.context.type.SimpleTableSegmentBinderContext;
import com.dc.parser.exec.engine.segment.dml.projection.type.ColumnProjectionSegmentBinder;
import com.dc.parser.exec.engine.segment.dml.projection.type.ShorthandProjectionSegmentBinder;
import com.dc.parser.exec.engine.segment.dml.projection.type.SubqueryProjectionSegmentBinder;
import com.dc.parser.exec.engine.segment.util.SubqueryTableBindUtils;
import com.dc.parser.exec.engine.statement.SQLStatementBinderContext;
import com.dc.parser.model.exception.ColumnNotFoundException;
import com.dc.parser.model.segment.dml.expr.ExpressionSegment;
import com.dc.parser.model.segment.dml.item.*;
import com.dc.parser.model.segment.generic.table.TableSegment;
import com.dc.parser.model.value.identifier.IdentifierValue;
import com.google.common.collect.LinkedHashMultimap;
import com.google.common.collect.Multimap;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.Collection;

/**
 * Projections segment binder.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ProjectionsSegmentBinder {

    /**
     * Bind projections segment.
     *
     * @param segment                  table segment
     * @param binderContext            statement binder context
     * @param boundTableSegment        bound table segment
     * @param tableBinderContexts      table binder contexts
     * @param outerTableBinderContexts outer table binder contexts
     * @return bound projections segment
     */
    public static ProjectionsSegment bind(final ProjectionsSegment segment, final SQLStatementBinderContext binderContext, final TableSegment boundTableSegment,
                                          final Multimap<CaseInsensitiveString, TableSegmentBinderContext> tableBinderContexts,
                                          final Multimap<CaseInsensitiveString, TableSegmentBinderContext> outerTableBinderContexts) {
        ProjectionsSegment result = new ProjectionsSegment(segment.getStartIndex(), segment.getStopIndex());
        result.setDistinctRow(segment.isDistinctRow());
        for (ProjectionSegment each : segment.getProjections()) {
            Multimap<CaseInsensitiveString, TableSegmentBinderContext> currentTableBinderContexts = createCurrentTableBinderContexts(binderContext, result.getProjections());
            result.getProjections().add(bind(binderContext, boundTableSegment, currentTableBinderContexts, tableBinderContexts, outerTableBinderContexts, each));
        }
        return result;
    }

    private static ProjectionSegment bind(final SQLStatementBinderContext binderContext, final TableSegment boundTableSegment,
                                          final Multimap<CaseInsensitiveString, TableSegmentBinderContext> currentTableBinderContexts,
                                          final Multimap<CaseInsensitiveString, TableSegmentBinderContext> tableBinderContexts,
                                          final Multimap<CaseInsensitiveString, TableSegmentBinderContext> outerTableBinderContexts,
                                          final ProjectionSegment projectionSegment) {
        try {
            return bind(projectionSegment, binderContext, boundTableSegment, tableBinderContexts, outerTableBinderContexts);
        } catch (final ColumnNotFoundException ignored) {
            return bind(projectionSegment, binderContext, boundTableSegment, currentTableBinderContexts, outerTableBinderContexts);
        }
    }

    private static ProjectionSegment bind(final ProjectionSegment projectionSegment, final SQLStatementBinderContext binderContext, final TableSegment boundTableSegment,
                                          final Multimap<CaseInsensitiveString, TableSegmentBinderContext> tableBinderContexts,
                                          final Multimap<CaseInsensitiveString, TableSegmentBinderContext> outerTableBinderContexts) {
        if (projectionSegment instanceof ColumnProjectionSegment) {
            return ColumnProjectionSegmentBinder.bind((ColumnProjectionSegment) projectionSegment, binderContext, tableBinderContexts, outerTableBinderContexts);
        }
        if (projectionSegment instanceof ShorthandProjectionSegment) {
            return ShorthandProjectionSegmentBinder.bind((ShorthandProjectionSegment) projectionSegment, boundTableSegment, tableBinderContexts);
        }
        if (projectionSegment instanceof SubqueryProjectionSegment) {
            Multimap<CaseInsensitiveString, TableSegmentBinderContext> newOuterTableBinderContexts = LinkedHashMultimap.create();
            newOuterTableBinderContexts.putAll(outerTableBinderContexts);
            newOuterTableBinderContexts.putAll(tableBinderContexts);
            return SubqueryProjectionSegmentBinder.bind((SubqueryProjectionSegment) projectionSegment, binderContext, newOuterTableBinderContexts);
        }
        if (projectionSegment instanceof ExpressionProjectionSegment) {
            ExpressionSegment boundExpressionSegment = ExpressionSegmentBinder.bind(
                    ((ExpressionProjectionSegment) projectionSegment).getExpr(), SegmentType.PROJECTION, binderContext, tableBinderContexts, outerTableBinderContexts);
            ExpressionProjectionSegment result = new ExpressionProjectionSegment(
                    projectionSegment.getStartIndex(), projectionSegment.getStopIndex(), ((ExpressionProjectionSegment) projectionSegment).getText(), boundExpressionSegment);
            result.setAlias(((ExpressionProjectionSegment) projectionSegment).getAliasSegment());
            return result;
        }
        if (projectionSegment instanceof AggregationDistinctProjectionSegment) {
            return bindAggregationDistinctProjection((AggregationDistinctProjectionSegment) projectionSegment, binderContext, tableBinderContexts, outerTableBinderContexts);
        }
        if (projectionSegment instanceof AggregationProjectionSegment) {
            return bindAggregationProjection((AggregationProjectionSegment) projectionSegment, binderContext, tableBinderContexts, outerTableBinderContexts);
        }
        // TODO support more ProjectionSegment bound
        return projectionSegment;
    }

    private static AggregationDistinctProjectionSegment bindAggregationDistinctProjection(final AggregationDistinctProjectionSegment aggregationDistinctSegment,
                                                                                          final SQLStatementBinderContext binderContext,
                                                                                          final Multimap<CaseInsensitiveString, TableSegmentBinderContext> tableBinderContexts,
                                                                                          final Multimap<CaseInsensitiveString, TableSegmentBinderContext> outerTableBinderContexts) {
        AggregationDistinctProjectionSegment result = new AggregationDistinctProjectionSegment(aggregationDistinctSegment.getStartIndex(), aggregationDistinctSegment.getStopIndex(),
                aggregationDistinctSegment.getType(), aggregationDistinctSegment.getExpression(), aggregationDistinctSegment.getDistinctInnerExpression(),
                aggregationDistinctSegment.getSeparator().orElse(null));
        aggregationDistinctSegment.getParameters()
                .forEach(each -> result.getParameters().add(ExpressionSegmentBinder.bind(each, SegmentType.PROJECTION, binderContext, tableBinderContexts, outerTableBinderContexts)));
        aggregationDistinctSegment.getAliasSegment().ifPresent(result::setAlias);
        return result;
    }

    private static AggregationProjectionSegment bindAggregationProjection(final AggregationProjectionSegment aggregationSegment, final SQLStatementBinderContext binderContext,
                                                                          final Multimap<CaseInsensitiveString, TableSegmentBinderContext> tableBinderContexts,
                                                                          final Multimap<CaseInsensitiveString, TableSegmentBinderContext> outerTableBinderContexts) {
        AggregationProjectionSegment result =
                new AggregationProjectionSegment(aggregationSegment.getStartIndex(), aggregationSegment.getStopIndex(), aggregationSegment.getType(), aggregationSegment.getExpression(),
                        aggregationSegment.getSeparator().orElse(null));
        aggregationSegment.getParameters()
                .forEach(each -> result.getParameters().add(ExpressionSegmentBinder.bind(each, SegmentType.PROJECTION, binderContext, tableBinderContexts, outerTableBinderContexts)));
        aggregationSegment.getAliasSegment().ifPresent(result::setAlias);
        return result;
    }

    private static Multimap<CaseInsensitiveString, TableSegmentBinderContext> createCurrentTableBinderContexts(final SQLStatementBinderContext binderContext,
                                                                                                               final Collection<ProjectionSegment> projections) {
        Multimap<CaseInsensitiveString, TableSegmentBinderContext> result = LinkedHashMultimap.create();
        Collection<ProjectionSegment> subqueryProjections = SubqueryTableBindUtils.createSubqueryProjections(projections, new IdentifierValue(""), binderContext.getSqlStatement().getDatabaseType());
        result.put(new CaseInsensitiveString(""), new SimpleTableSegmentBinderContext(subqueryProjections));
        return result;
    }
}
