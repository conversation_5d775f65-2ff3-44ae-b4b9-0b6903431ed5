package com.dc.parser.exec.engine.segment.dml.expression;

import com.dc.infra.utils.CaseInsensitiveMap.CaseInsensitiveString;
import com.dc.parser.exec.engine.segment.SegmentType;
import com.dc.parser.exec.engine.segment.dml.expression.type.*;
import com.dc.parser.exec.engine.segment.dml.from.context.TableSegmentBinderContext;
import com.dc.parser.exec.engine.statement.SQLStatementBinderContext;
import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.segment.dml.expr.*;
import com.dc.parser.model.segment.dml.expr.join.OuterJoinExpression;
import com.dc.parser.model.segment.dml.expr.subquery.SubqueryExpressionSegment;
import com.dc.parser.model.segment.dml.item.AggregationDistinctProjectionSegment;
import com.dc.parser.model.segment.dml.item.AggregationProjectionSegment;
import com.google.common.collect.LinkedHashMultimap;
import com.google.common.collect.Multimap;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * Expression segment binder.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ExpressionSegmentBinder {

    /**
     * Bind expression segment.
     *
     * @param segment                  expression segment
     * @param parentSegmentType        parent segment type
     * @param binderContext            SQL statement binder context
     * @param tableBinderContexts      table binder contexts
     * @param outerTableBinderContexts outer table binder contexts
     * @return bound expression segment
     */
    public static ExpressionSegment bind(final ExpressionSegment segment, final SegmentType parentSegmentType, final SQLStatementBinderContext binderContext,
                                         final Multimap<CaseInsensitiveString, TableSegmentBinderContext> tableBinderContexts,
                                         final Multimap<CaseInsensitiveString, TableSegmentBinderContext> outerTableBinderContexts) {
        if (segment instanceof BinaryOperationExpression) {
            return BinaryOperationExpressionBinder.bind((BinaryOperationExpression) segment, parentSegmentType, binderContext, tableBinderContexts, outerTableBinderContexts);
        }
        if (segment instanceof ExistsSubqueryExpression) {
            return ExistsSubqueryExpressionBinder.bind((ExistsSubqueryExpression) segment, binderContext, tableBinderContexts);
        }
        if (segment instanceof SubqueryExpressionSegment) {
            Multimap<CaseInsensitiveString, TableSegmentBinderContext> newOuterTableBinderContexts = LinkedHashMultimap.create();
            newOuterTableBinderContexts.putAll(outerTableBinderContexts);
            newOuterTableBinderContexts.putAll(tableBinderContexts);
            return new SubqueryExpressionSegment(SubquerySegmentBinder.bind(((SubqueryExpressionSegment) segment).getSubquery(), binderContext, newOuterTableBinderContexts));
        }
        if (segment instanceof InExpression) {
            return InExpressionBinder.bind((InExpression) segment, parentSegmentType, binderContext, tableBinderContexts, outerTableBinderContexts);
        }
        if (segment instanceof NotExpression) {
            return NotExpressionBinder.bind((NotExpression) segment, parentSegmentType, binderContext, tableBinderContexts);
        }
        if (segment instanceof ColumnSegment) {
            return ColumnSegmentBinder.bind((ColumnSegment) segment, parentSegmentType, binderContext, tableBinderContexts, outerTableBinderContexts);
        }
        if (segment instanceof FunctionSegment) {
            return FunctionExpressionSegmentBinder.bind((FunctionSegment) segment, parentSegmentType, binderContext, tableBinderContexts, outerTableBinderContexts);
        }
        if (segment instanceof BetweenExpression) {
            return BetweenExpressionSegmentBinder.bind((BetweenExpression) segment, binderContext, tableBinderContexts, outerTableBinderContexts);
        }
        if (segment instanceof AggregationDistinctProjectionSegment) {
            return AggregationDistinctProjectionSegmentBinder.bind((AggregationDistinctProjectionSegment) segment, parentSegmentType, binderContext, tableBinderContexts, outerTableBinderContexts);
        }
        if (segment instanceof AggregationProjectionSegment) {
            return AggregationProjectionSegmentBinder.bind((AggregationProjectionSegment) segment, parentSegmentType, binderContext, tableBinderContexts, outerTableBinderContexts);
        }
        if (segment instanceof CaseWhenExpression) {
            return CaseWhenExpressionBinder.bind((CaseWhenExpression) segment, parentSegmentType, binderContext, tableBinderContexts, outerTableBinderContexts);
        }
        if (segment instanceof OuterJoinExpression) {
            return OuterJoinExpressionBinder.bind((OuterJoinExpression) segment, parentSegmentType, binderContext, tableBinderContexts, outerTableBinderContexts);
        }
        // TODO support more ExpressionSegment bound
        return segment;
    }
}
