

package com.dc.summer.model.sql.registry;

import com.dc.summer.DBException;
import com.dc.summer.model.impl.AbstractContextDescriptor;
import com.dc.summer.model.sql.format.SQLFormatter;
import org.eclipse.core.runtime.IConfigurationElement;
import com.dc.code.NotNull;

/**
 * SQLFormatterDescriptor
 */
public class SQLFormatterDescriptor extends AbstractContextDescriptor {

    public static final String EXTENSION_ID = "com.dc.summer.sqlFormatter"; //$NON-NLS-1$

    private final String id;
    private final String label;
    private final String description;
    private final ObjectType formatterImplClass;

    public SQLFormatterDescriptor(IConfigurationElement config) {
        super(config);
        this.id = config.getAttribute("id");
        this.label = config.getAttribute("label");
        this.description = config.getAttribute("description");
        this.formatterImplClass = new ObjectType(config.getAttribute("class"));
    }

    public String getId() {
        return id;
    }

    public String getLabel() {
        return label;
    }

    public String getDescription() {
        return description;
    }

    @NotNull
    public SQLFormatter createFormatter()
        throws DBException {
        return formatterImplClass.createInstance(SQLFormatter.class);
    }

}
