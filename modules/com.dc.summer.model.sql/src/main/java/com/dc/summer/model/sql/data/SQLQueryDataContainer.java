
package com.dc.summer.model.sql.data;

import com.dc.summer.Log;
import com.dc.summer.model.*;
import com.dc.summer.model.data.DBDDataFilter;
import com.dc.summer.model.data.DBDDataReceiver;
import com.dc.summer.model.exec.*;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.*;
import com.dc.summer.model.sql.parser.SQLParserContext;
import com.dc.summer.model.sql.parser.SQLRuleManager;
import com.dc.summer.model.sql.parser.SQLScriptParser;
import com.dc.summer.model.struct.DBSDataContainer;
import org.apache.commons.collections4.CollectionUtils;
import org.eclipse.jface.text.Document;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.struct.DBSObject;
import com.dc.utils.CommonUtils;

import java.sql.SQLException;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * Data container for single SQL query.
 * Doesn't support multiple resulsets.
 */
public class SQLQueryDataContainer implements DBSDataContainer, SQLQueryContainer, DBPContextProvider {

    private final DBPContextProvider contextProvider;
    private final SQLScriptContext scriptContext;
    private SQLQuery query;
    private final Log log;

    private final long total;

    private final String tableName;

    private static final int MAX_RESULTS_COUNT = 100;

    public SQLQueryDataContainer(DBPContextProvider contextProvider,
                                 SQLQuery query,
                                 SQLScriptContext scriptContext,
                                 Log log,
                                 long total,
                                 String tableName) {
        this.contextProvider = contextProvider;
        this.query = query;
        this.scriptContext = scriptContext;
        this.log = log;
        this.total = total;
        this.tableName = tableName;
    }

    @Override
    public DBCExecutionContext getExecutionContext() {
        return contextProvider.getExecutionContext();
    }

    @Override
    public String[] getSupportedFeatures() {
        return new String[]{FEATURE_DATA_SELECT, FEATURE_DATA_COUNT, FEATURE_DATA_FILTER};
    }

    @Override
    public SQLScriptContext getScriptContext() {
        return scriptContext;
    }

    @NotNull
    @Override
    public DBCStatistics readData(@NotNull DBCExecutionSource source,
                                  @NotNull DBCSession session,
                                  @NotNull DBDDataReceiver dataReceiver,
                                  DBDDataFilter dataFilter,
                                  long firstRow,
                                  long maxRows,
                                  long flags,
                                  int stage,
                                  int fetchSize,
                                  List<Object> data) throws DBCException {

        DBCStatistics statistics = new DBCStatistics();
        // Modify query (filters + parameters)
        DBPDataSource dataSource = session.getDataSource();
        SQLQuery sqlQuery = query;
        String queryText = sqlQuery.getText();//.trim();
        if (dataFilter != null && dataFilter.hasFilters()) {
            String filteredQueryText = dataSource.getSQLDialect().addFiltersToQuery(
                    session.getProgressMonitor(),
                    dataSource, queryText, dataFilter);
            sqlQuery = new SQLQuery(dataSource, filteredQueryText, sqlQuery);
        } else {
            sqlQuery = new SQLQuery(dataSource, queryText, sqlQuery);
        }

        if (scriptContext != null) {
            SQLSyntaxManager syntaxManager = new SQLSyntaxManager();
            syntaxManager.init(dataSource.getSQLDialect(), dataSource.getContainer().getPreferenceStore());
            SQLRuleManager ruleManager = new SQLRuleManager(syntaxManager);
            ruleManager.loadRules(dataSource, false);
            SQLParserContext parserContext = new SQLParserContext(getDataSource(), syntaxManager, ruleManager, new Document(query.getText()));
            sqlQuery.setParameters(SQLScriptParser.parseParameters(parserContext, 0, sqlQuery.getLength()));
            if (!scriptContext.fillQueryParameters(sqlQuery, CommonUtils.isBitSet(flags, DBSDataContainer.FLAG_REFRESH))) {
                // User canceled
                return statistics;
            }
        }

        final SQLQueryResult curResult = new SQLQueryResult(sqlQuery);
        if (firstRow > 0) {
            curResult.setRowOffset(firstRow);
        }
        statistics.setQueryText(sqlQuery.getText());

        long startTime = System.currentTimeMillis();

        long exportLimit = maxRows;

        if (total > 0) {
            if (maxRows == 0) {
                maxRows = total;
            } else if (maxRows > 0) {
                maxRows = Math.min(maxRows, total);
            }
        }

        final DBCStatementType statementType = CollectionUtils.isNotEmpty(data) ? DBCStatementType.QUERY : DBCStatementType.SCRIPT;

        try (final DBCStatement dbcStatement = DBUtils.makeStatement(
                source,
                session,
                statementType,
                sqlQuery,
                firstRow,
                maxRows)) {

            if (statementType == DBCStatementType.QUERY && dbcStatement instanceof JDBCPreparedStatement) {
                for (int i = 0; i < data.size(); i++) {
                    try {
                        ((JDBCPreparedStatement) dbcStatement).setObject(i + 1, data.get(i));
                    } catch (SQLException e) {
                        log.warn("Can't binding prepared statement: " + e.getMessage());
                    }
                }
            }

            statistics.setQueryText(dbcStatement.getQueryString());

            DBExecUtils.setStatementFetchSize(dbcStatement, firstRow, maxRows, fetchSize);

            // Execute statement

            session.getProgressMonitor().subTask("Execute query");

            boolean hasResultSet = dbcStatement.executeStatement();

            statistics.addExecuteTime(System.currentTimeMillis() - startTime);
            statistics.addStatementsCount();

            curResult.setHasResultSet(hasResultSet);

            int maxResultsCount = resolveMaxResultsCount(getDataSource());
            if (stage > 0) {
                for (int i = 0; i < stage; i++) {
                    hasResultSet = dbcStatement.nextResults();
                }
            }

            if (hasResultSet) {
                DBCResultSet resultSet = dbcStatement.openResultSet();
                if (resultSet != null) {
                    SQLQueryResult.ExecuteResult executeResult = curResult.addExecuteResult(true);
                    DBRProgressMonitor monitor = session.getProgressMonitor();
                    monitor.subTask("Fetch result set");
                    DBFetchProgress fetchProgress = new DBFetchProgress(session.getProgressMonitor());

                    dataReceiver.fetchStart(session, resultSet, firstRow, maxRows);

                    try {
                        long fetchStartTime = System.currentTimeMillis();

                        // Fetch all rows
                        while (!fetchProgress.isMaxRowsFetched(maxRows) && !fetchProgress.isCanceled() && resultSet.nextRow()) {
                            dataReceiver.fetchRow(session, resultSet);
                            fetchProgress.monitorRowFetch();
                        }
                        if (fetchProgress.isMaxRowsFetched(exportLimit) && resultSet.overMaxRow()) {
                            statistics.setExportLimit(true);
                        }

                        statistics.addFetchTime(System.currentTimeMillis() - fetchStartTime);
                    } finally {
                        try {
                            resultSet.close();
                        } catch (Throwable e) {
                            log.error("Error while closing resultset", e);
                        }
                        try {
                            dataReceiver.fetchEnd(session, resultSet);
                        } catch (Throwable e) {
                            log.error("Error while handling end of result set fetch", e);
                        }
                        dataReceiver.close();
                    }

                    if (executeResult != null) {
                        executeResult.setRowCount(fetchProgress.getRowCount());
                    }
                    statistics.setRowsFetched(fetchProgress.getRowCount());
                    monitor.subTask(fetchProgress.getRowCount() + " rows fetched");
                }
            } else {
                log.warn("No results returned by query execution");
            }

            try {
                curResult.addWarnings(dbcStatement.getStatementWarnings());
            } catch (Throwable e) {
                log.warn("Can't read execution warnings: " + e.getMessage());
            }
        }

        return statistics;
    }

    private static int resolveMaxResultsCount(@Nullable DBPDataSource dataSource) {
        if (dataSource == null) {
            return MAX_RESULTS_COUNT;
        }
        return dataSource.getInfo().supportsMultipleResults() ? MAX_RESULTS_COUNT : 1;
    }

    @Override
    public long countData(@NotNull DBCExecutionSource source, @NotNull DBCSession session, @Nullable DBDDataFilter dataFilter, long flags)
            throws DBCException {
        return -1;
    }

    @Nullable
    @Override
    public String getDescription() {
        return "SQL Query";
    }

    @Nullable
    @Override
    public DBSObject getParentObject() {
        return getDataSource();
    }

    @Nullable
    @Override
    public DBPDataSource getDataSource() {
        DBCExecutionContext executionContext = getExecutionContext();
        return executionContext == null ? null : executionContext.getDataSource();
    }

    @Override
    public boolean isPersisted() {
        return false;
    }

    @NotNull
    @Override
    public String getName() {
        String name = query.getOriginalText();
        if (name == null) {
            name = "SQL";
        }
        return name;
    }

    @Nullable
    @Override
    public DBPDataSourceContainer getDataSourceContainer() {
        DBPDataSource dataSource = getDataSource();
        return dataSource == null ? null : dataSource.getContainer();
    }

    @Override
    public String toString() {
        return query.getOriginalText();
    }

    @Override
    public SQLScriptElement getQuery() {
        return query;
    }

    public void setQuery(SQLQuery query) {
        this.query = query;
    }

    @Override
    public Map<String, Object> getQueryParameters() {
        if (query.getParameters() == null) {
            return scriptContext.getAllParameters();
        }
        Map<String, Object> result = new LinkedHashMap<>();
        for (SQLQueryParameter parameter : query.getParameters()) {
            result.put(parameter.getVarName(), parameter.getValue());
        }
        return result;
    }

    @Override
    public String getTableName() {
        return tableName;
    }

    @Override
    public boolean equals(Object obj) {
        return obj instanceof SQLQueryDataContainer &&
                CommonUtils.equalObjects(query, ((SQLQueryDataContainer) obj).query);
    }
}
