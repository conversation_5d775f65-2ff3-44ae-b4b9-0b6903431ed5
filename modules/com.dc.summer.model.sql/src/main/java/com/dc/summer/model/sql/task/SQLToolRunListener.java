
package com.dc.summer.model.sql.task;

import com.dc.summer.model.DBPObject;
import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.summer.model.exec.DBCSession;

import java.util.List;

/**
 * SQLToolRunListener
 */
public interface SQLToolRunListener {

    void handleActionStatistics(DBPObject object, DBEPersistAction action, DBCSession session, List<? extends SQLToolStatistics> statistics);

}
