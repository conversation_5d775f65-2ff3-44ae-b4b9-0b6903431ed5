
package com.dc.summer.model.sql.parser.tokens.predicates;

import com.dc.summer.Log;
import com.dc.summer.model.sql.parser.SQLRuleManager;
import com.dc.summer.model.text.parser.TPCharacterScanner;
import com.dc.summer.model.text.parser.TPRule;
import com.dc.summer.model.text.parser.TPTokenDefault;
import com.dc.code.NotNull;
import com.dc.summer.model.sql.parser.tokens.SQLTokenType;
import com.dc.summer.model.text.parser.TPToken;

/**
 * Dialect-specific predicate node producer
 */
class SQLTokenPredicateFactory extends TokenPredicateFactory {
    private static final Log log = Log.getLog(SQLTokenPredicateFactory.class);

    private final TPRule[] allRules;

    private static class StringScanner implements TPCharacterScanner {
        private final String string;
        private int pos = 0;

        public StringScanner(@NotNull String string) {
            this.string = string;
        }

        @Override
        public char[][] getLegalLineDelimiters() {
            throw new UnsupportedOperationException();
        }

        @Override
        public int getColumn() {
            throw new UnsupportedOperationException();
        }

        @Override
        public int getOffset() {
            return pos;
        }

        @Override
        public int read() {
            return pos >= 0 && pos < string.length() ? string.charAt(pos++) : -1;
        }

        @Override
        public void unread() {
            pos--;
        }

        public void reset() {
            pos = 0;
        }
    }

    public SQLTokenPredicateFactory(@NotNull SQLRuleManager ruleManager) {
        super();
        allRules = ruleManager.getAllRules();
    }

    @Override
    @NotNull
    protected SQLTokenEntry classifyToken(@NotNull String string) {
        StringScanner scanner = new StringScanner(string);
        for (TPRule fRule : allRules) {
            try {
                scanner.reset();
                TPToken token = fRule.evaluate(scanner);
                if (!token.isUndefined()) {
                    SQLTokenType tokenType = token instanceof TPTokenDefault ? (SQLTokenType) ((TPTokenDefault) token).getData() : SQLTokenType.T_OTHER;
                    return new SQLTokenEntry(string, tokenType);
                }
            } catch (UnsupportedOperationException ignore) {
            } catch (Throwable e) {
                // some rules raise exceptions in a certain situations when the string does not correspond the rule
                log.debug(e.getMessage());
            }
        }
        return new SQLTokenEntry(string, null);
    }
}