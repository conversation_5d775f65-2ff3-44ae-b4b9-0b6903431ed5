/*
 * DBeaver - Universal Database Manager
 * Copyright (C) 2010-2022 DBeaver Corp and others
 *
 * Licensed under the Apache License, Version 2.0 (the "License")),
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dc.summer.model.sql.parser.tokens;

import com.dc.summer.model.text.parser.TPTokenType;

/**
 * SQL token type
 */
public enum SQLTokenType implements TPTokenType {

    T_KEYWORD(500),
    T_STRING(501),
    T_QUOTED(502),
    T_TYPE(503),
    T_NUMBER(504),

    T_UNKNOWN(1000),
    T_BLOCK_BEGIN(1001),
    T_BLOCK_END(1002),
    T_BLOCK_TOGGLE(1003),
    T_BLOCK_HEADER(1004),

    T_COMMENT(1005),
    T_CONTROL(1006),
    T_DELIMITER(1007),
    T_SET_DELIMITER(1008),
    T_PARAMETER(1009),
    T_VARIABLE(1010),
    T_LINE_BREAK(1011),
    T_DB_DOT(1012),

    T_SLASH(1013),
    T_TXN_BEGIN(1014),
    T_TXN_END(1015),

    T_OTHER(2000);

    private final int type;

    SQLTokenType(int type) {
        this.type = type;
    }

    @Override
    public int getTokenType() {
        return type;
    }

    @Override
    public String getTypeId() {
        return null;
    }

}
