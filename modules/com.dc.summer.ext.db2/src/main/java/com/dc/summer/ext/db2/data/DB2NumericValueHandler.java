
package com.dc.summer.ext.db2.data;

import com.dc.summer.model.impl.jdbc.data.handlers.JDBCNumberValueHandler;
import com.dc.summer.model.data.DBDFormatSettings;
import com.dc.summer.model.struct.DBSTypedObject;

/**
 * DECFLOAT type support
 */
public class DB2NumericValueHandler extends JDBCNumberValueHandler {

    DB2NumericValueHandler(DBSTypedObject type, DBDFormatSettings formatSettings) {
        super(type, formatSettings);
    }

    @Override
    protected boolean isReadDecimalsAsDouble() {
        return true;
    }
}
