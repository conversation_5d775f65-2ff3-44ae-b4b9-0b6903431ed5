<?xml version='1.0' encoding='UTF-8'?>
<schema targetNamespace="com.dc.summer.core" xmlns="http://www.w3.org/2001/XMLSchema">
<annotation>
      <appInfo>
         <meta.schema plugin="com.dc.summer.registry" id="com.dc.summer.auth.provider" name="Authentication provider"/>
      </appInfo>
      <documentation>
         DBeaver auth provider
      </documentation>
   </annotation>

   <element name="extension">
      <annotation>
         <appInfo>
            <meta.element />
         </appInfo>
      </annotation>
   </element>

   <element name="model">
      <annotation>
         <documentation>
            Data source auth model description
         </documentation>
      </annotation>
   </element>

   <annotation>
      <appInfo>
         <meta.section type="appInfo"/>
      </appInfo>
      <documentation>
         [Enter API information here.]
      </documentation>
   </annotation>

   <annotation>
      <appInfo>
         <meta.section type="implementation"/>
      </appInfo>
      <documentation>
         [Enter information about supplied implementation of this extension point.]
      </documentation>
   </annotation>


</schema>
