
package com.dc.summer.registry.language;

import com.dc.summer.model.DBPNamedObjectLocalized;
import com.dc.summer.model.app.DBPPlatformLanguage;
import com.dc.summer.model.impl.AbstractContextDescriptor;
import com.dc.summer.registry.RegistryConstants;
import org.eclipse.core.runtime.IConfigurationElement;
import com.dc.code.NotNull;

/**
 * PlatformLanguageDescriptor
 */
public class PlatformLanguageDescriptor extends AbstractContextDescriptor implements DBPPlatformLanguage, DBPNamedObjectLocalized {
    public static final String EXTENSION_ID = "com.dc.summer.language"; //$NON-NLS-1$

    private final IConfigurationElement config;

    public PlatformLanguageDescriptor(IConfigurationElement config) {
        super(config);

        this.config = config;
    }

    @NotNull
    @Override
    public String getCode() {
        return config.getAttribute(RegistryConstants.ATTR_CODE);
    }

    @NotNull
    @Override
    public String getLabel() {
        return config.getAttribute(RegistryConstants.ATTR_LABEL);
    }

    public String getDescription() {
        return config.getAttribute(RegistryConstants.ATTR_DESCRIPTION);
    }

    @Override
    public String toString() {
        return getCode();
    }

    @NotNull
    @Override
    public String getName() {
        return getLabel();
    }

    @Override
    public String getLocalizedName(String locale) {
        return config.getAttribute(RegistryConstants.ATTR_LABEL, locale);
    }

}
