
package com.dc.summer.registry;

import com.dc.summer.model.app.DBPWorkspace;
import org.eclipse.core.resources.IWorkspace;
import com.dc.code.NotNull;
import com.dc.summer.model.app.DBPPlatform;

/**
 * EclipseApplicationImpl
 */
public abstract class EclipseApplicationImpl extends BaseApplicationImpl {

    @NotNull
    @Override
    public DBPWorkspace createWorkspace(@NotNull DBPPlatform platform, @NotNull IWorkspace eclipseWorkspace) {
        return new EclipseWorkspaceImpl(platform, eclipseWorkspace);
    }

}
