

package com.dc.summer.registry;

import com.dc.summer.model.impl.AbstractDescriptor;
import org.eclipse.core.runtime.IConfigurationElement;
import com.dc.summer.model.edit.DBEObjectManager;

/**
 * ObjectManagerDescriptor
 */
public class ObjectManagerDescriptor extends AbstractDescriptor
{
    public static final String EXTENSION_ID = "com.dc.summer.objectManager"; //NON-NLS-1 //$NON-NLS-1$

    private String id;
    private ObjectType managerType;
    private ObjectType objectType;
    private DBEObjectManager managerInstance;

    ObjectManagerDescriptor(IConfigurationElement config)
    {
        super(config);

        this.id = config.getAttribute(RegistryConstants.ATTR_CLASS);
        this.managerType = new ObjectType(id);
        this.objectType = new ObjectType(config.getAttribute(RegistryConstants.ATTR_OBJECT_TYPE));
    }

    void dispose()
    {
        objectType = null;
        managerType = null;
        managerInstance = null;
    }

    public String getId()
    {
        return id;
    }

    public ObjectType getObjectType() {
        return objectType;
    }

    public boolean appliesToType(Class<?> clazz)
    {
        return objectType.matchesType(clazz);
    }

    public synchronized DBEObjectManager getManager()
    {
        if (managerInstance != null) {
            return managerInstance;
        }
        Class<? extends DBEObjectManager> clazz = managerType.getObjectClass(DBEObjectManager.class);
        if (clazz == null) {
            throw new IllegalStateException("Can't instantiate object manager '" + managerType.getImplName() + "'");
        }
        try {
            managerInstance = clazz.getConstructor().newInstance();
        } catch (Throwable ex) {
            throw new IllegalStateException("Error instantiating object manager '" + clazz.getName() + "'", ex);
        }
        return managerInstance;
    }

    @Override
    public String toString() {
        return id;
    }

}