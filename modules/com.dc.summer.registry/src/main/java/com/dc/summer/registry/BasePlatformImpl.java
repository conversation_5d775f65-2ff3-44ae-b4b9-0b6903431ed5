
package com.dc.summer.registry;

import com.dc.summer.Log;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.app.*;
import com.dc.summer.model.data.DBDRegistry;
import com.dc.summer.model.fs.DBFRegistry;
import com.dc.summer.model.impl.preferences.AbstractPreferenceStore;
import com.dc.summer.model.navigator.DBNModel;
import com.dc.summer.registry.driver.DriverDescriptor;
import com.dc.summer.runtime.IPluginService;
import com.dc.summer.runtime.jobs.DataSourceMonitorJob;
import com.dc.summer.utils.RuntimeUtils;
import org.eclipse.core.internal.registry.IRegistryConstants;
import org.eclipse.core.runtime.Platform;
import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.ModelPreferences;
import com.dc.summer.model.app.*;
import com.dc.summer.model.connection.DBPDataSourceProviderRegistry;
import com.dc.summer.model.edit.DBERegistry;
import com.dc.summer.model.preferences.DBPPreferenceStore;
import com.dc.summer.model.runtime.OSDescriptor;
import com.dc.summer.registry.datatype.DataTypeProviderRegistry;
import com.dc.summer.registry.formatter.DataFormatterRegistry;
import com.dc.summer.registry.fs.FileSystemProviderRegistry;
import com.dc.summer.registry.language.PlatformLanguageRegistry;
import com.dc.utils.CommonUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Properties;

/**
 * BaseWorkspaceImpl.
 *
 * Base implementation of DBeaver platform
 */
public abstract class BasePlatformImpl implements DBPPlatform, DBPPlatformLanguageManager {

    private static final Log log = Log.getLog(BasePlatformImpl.class);

    private static final String APP_CONFIG_FILE = "dbeaver.ini";
    private static final String ECLIPSE_CONFIG_FILE = "eclipse.ini";
    private static final String CONFIG_FILE = "config.ini";

    private DBPPlatformLanguage language;
    private OSDescriptor localSystem;

    private DBNModel navigatorModel;

    private final List<IPluginService> activatedServices = new ArrayList<>();

    protected void initialize() {
        log.debug("Initialize base platform...");

        DBPPreferenceStore prefsStore = getPreferenceStore();
        // Global pref events forwarder
        prefsStore.addPropertyChangeListener(event -> {
            // Forward event to all data source preferences
            for (DBPDataSourceContainer ds : DataSourceRegistry.getAllDataSources()) {
                ((AbstractPreferenceStore)ds.getPreferenceStore()).firePropertyChangeEvent(prefsStore, event.getProperty(), event.getOldValue(), event.getNewValue());
            }
        });

        this.localSystem = new OSDescriptor(Platform.getOS(), Platform.getOSArch());
        {
            this.language = PlatformLanguageRegistry.getInstance().getLanguage(Locale.getDefault());
            if (this.language == null) {
                log.debug("Language for locale '" + Locale.getDefault() + "' not found. Use default.");
                this.language = PlatformLanguageRegistry.getInstance().getLanguage(Locale.ENGLISH);
            }
        }

        // Navigator model
        this.navigatorModel = new DBNModel(this, null);
        this.navigatorModel.setModelAuthContext(getWorkspace().getAuthContext());
        this.navigatorModel.initialize();

        if (!getApplication().isExclusiveMode()) {
            // Activate plugin services
            for (IPluginService pluginService : PluginServiceRegistry.getInstance().getServices()) {
                try {
                    pluginService.activateService();
                    activatedServices.add(pluginService);
                } catch (Throwable e) {
                    log.error("Error activating plugin service", e);
                }
            }

            // Connections monitoring job
            new DataSourceMonitorJob(this).scheduleMonitor();
        }
    }

    public synchronized void dispose() {
        // Deactivate plugin services
        for (IPluginService pluginService : activatedServices) {
            try {
                pluginService.deactivateService();
            } catch (Exception e) {
                log.error("Error deactivating plugin service", e);
            }
        }
        activatedServices.clear();

        // Dispose navigator model first
        // It is a part of UI
        if (this.navigatorModel != null) {
            this.navigatorModel.dispose();
            //this.navigatorModel = null;
        }
    }

    @NotNull
    @Override
    public DBDRegistry getValueHandlerRegistry() {
        return DataTypeProviderRegistry.getInstance();
    }

    @NotNull
    @Override
    public DBERegistry getEditorsRegistry() {
        return ObjectManagerRegistry.getInstance();
    }

    @NotNull
    @Override
    public DBFRegistry getFileSystemRegistry() {
        return FileSystemProviderRegistry.getInstance();
    }

    @Override
    public DBPGlobalEventManager getGlobalEventManager() {
        return GlobalEventManagerImpl.getInstance();
    }

    @NotNull
    @Override
    public DBPDataFormatterRegistry getDataFormatterRegistry() {
        return DataFormatterRegistry.getInstance();
    }

    @NotNull
    @Override
    public File getApplicationConfiguration() {
        File configPath;
        try {
            configPath = RuntimeUtils.getLocalFileFromURL(Platform.getInstallLocation().getURL());
        } catch (IOException e) {
            throw new IllegalStateException("Can't detect application installation folder.", e);
        }
        File iniFile = new File(configPath, ECLIPSE_CONFIG_FILE);
        if (!iniFile.exists()) {
            iniFile = new File(configPath, APP_CONFIG_FILE);
        }
        return iniFile;
    }

    @NotNull
    @Override
    public OSDescriptor getLocalSystem() {
        return localSystem;
    }

    @NotNull
    @Override
    public DBPPlatformLanguage getLanguage() {
        return language;
    }

    @Override
    public void setPlatformLanguage(@NotNull DBPPlatformLanguage language) throws DBException {
        if (CommonUtils.equalObjects(language, this.language)) {
            return;
        }

        try {
            final File config = new File(RuntimeUtils.getLocalFileFromURL(Platform.getConfigurationLocation().getURL()), CONFIG_FILE);
            final Properties properties = new Properties();

            if (config.exists()) {
                try (FileInputStream is = new FileInputStream(config)) {
                    properties.load(is);
                }
            }

            properties.put(IRegistryConstants.PROP_NL, language.getCode());

            try (FileOutputStream os = new FileOutputStream(config)) {
                properties.store(os, null);
            }

            this.language = language;
            // This property is fake. But we set it to trigger property change listener
            // which will ask to restart workbench.
            getPreferenceStore().setValue(ModelPreferences.PLATFORM_LANGUAGE, language.getCode());
        } catch (IOException e) {
            throw new DBException("Unexpected error while saving startup configuration", e);
        }
    }

    @NotNull
    @Override
    public DBNModel getNavigatorModel() {
        return navigatorModel;
    }

    @NotNull
    @Override
    public DBPDataSourceProviderRegistry getDataSourceProviderRegistry() {
        return DataSourceProviderRegistry.getInstance();
    }

    @NotNull
    @Override
    public Path getCustomDriversHome() {
        return DriverDescriptor.getCustomDriversHome();
    }

    @Override
    public boolean isReadOnly() {
        return Platform.getInstanceLocation().isReadOnly();
    }
}
