
package com.dc.summer.registry.maven;

import com.dc.summer.Log;
import com.dc.summer.utils.RuntimeUtils;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.runtime.IVariableResolver;
import com.dc.summer.runtime.WebUtils;
import com.dc.summer.utils.GeneralUtils;
import com.dc.utils.CommonUtils;
import com.dc.utils.IOUtils;
import com.dc.utils.StandardConstants;
import com.dc.utils.xml.XMLException;
import com.dc.utils.xml.XMLUtils;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

import java.io.*;
import java.net.URISyntaxException;
import java.net.URL;
import java.util.*;

/**
 * Maven artifact version descriptor (POM).
 */
public class MavenArtifactVersion implements IMavenIdentifier {
    private static final Log log = Log.getLog(MavenArtifactVersion.class);

    public static final String PROP_PROJECT_VERSION = "project.version";
    public static final String PROP_PROJECT_PARENT_VERSION = "project.parent.version";
    public static final String PROP_PROJECT_GROUP_ID = "project.groupId";
    public static final String PROP_PROJECT_ARTIFACT_ID = "project.artifactId";
    private static final String DEFAULT_PROFILE_ID = "#root";

    private MavenArtifact artifact;
    private String name;
    private String version;
    private String packaging;
    private String description;
    private String url;
    private MavenArtifactVersion parent;
    private List<MavenArtifactVersion> imports;
    private final List<MavenArtifactLicense> licenses = new ArrayList<>();
    private final List<MavenProfile> profiles = new ArrayList<>();
    private final List<MavenRepository> repositories = new ArrayList<>();

    private IVariableResolver propertyResolver = new IVariableResolver() {
        @Override
        public String get(String name) {
            switch (name) {
                case PROP_PROJECT_VERSION:
                    return version;
                case PROP_PROJECT_PARENT_VERSION:
                    return parent != null ? parent.version : null;
                case PROP_PROJECT_GROUP_ID:
                    return artifact.getGroupId();
                case PROP_PROJECT_ARTIFACT_ID:
                    return artifact.getArtifactId();
            }
            for (MavenArtifactVersion v = MavenArtifactVersion.this; v != null; v = v.parent) {
                for (MavenProfile profile : v.profiles) {
                    if (!profile.isActive()) {
                        continue;
                    }
                    String value = profile.properties.get(name);
                    if (value != null) {
                        return evaluateString(value);
                    }
                }
            }
            return null;
        }
    };

    MavenArtifactVersion(@NotNull DBRProgressMonitor monitor, @NotNull MavenArtifact artifact, @NotNull String version, boolean resolveOptionalDependencies) throws IOException {
        this.artifact = artifact;
        this.version = CommonUtils.trim(version);

        loadPOM(monitor, resolveOptionalDependencies);
        this.version = evaluateString(this.version);

    }

    @NotNull
    public MavenArtifact getArtifact() {
        return artifact;
    }

    public String getName() {
        return name;
    }

    @NotNull
    @Override
    public String getGroupId() {
        return artifact.getGroupId();
    }

    @NotNull
    @Override
    public String getArtifactId() {
        return artifact.getArtifactId();
    }

    @Nullable
    @Override
    public String getFallbackVersion() {
        return artifact.getFallbackVersion();
    }

    @NotNull
    @Override
    public String getVersion() {
        return version;
    }

    @NotNull
    @Override
    public String getId() {
        return MavenArtifactReference.makeId(this);
    }

    @Nullable
    public String getPackaging() {
        return packaging;
    }

    public String getDescription() {
        return description;
    }

    public String getUrl() {
        return url;
    }

    public MavenArtifactVersion getParent() {
        return parent;
    }

    public List<MavenArtifactLicense> getLicenses() {
        return licenses;
    }

    public List<MavenProfile> getProfiles() {
        return profiles;
    }

    public List<MavenArtifactDependency> getDependencies() {
        List<MavenArtifactDependency> dependencies = new ArrayList<>();
        for (MavenProfile profile : profiles) {
            if (profile.isActive() && !CommonUtils.isEmpty(profile.dependencies)) {
                dependencies.addAll(profile.dependencies);
            }
        }
        if (parent != null) {
            List<MavenArtifactDependency> parentDependencies = parent.getDependencies();
            if (!CommonUtils.isEmpty(parentDependencies)) {
                dependencies.addAll(parentDependencies);
            }
        }
        return dependencies;
    }

    public File getCacheFile() {
        String fileExt = getPackagingFileExtension();
        if (artifact.getRepository().getType() == MavenRepository.RepositoryType.LOCAL) {
            String externalURL = getExternalURL(fileExt);
            try {
                return RuntimeUtils.getLocalFileFromURL(new URL(externalURL));
//                return new File(new URL(externalURL).toURI());
            } catch (Exception e) {
                log.warn("Bad repository URL", e);
                return new File(externalURL);
            }
        }
        return artifact.getRepository().getLocalCacheDir().resolve(
            artifact.getGroupId() + "/" + artifact.getVersionFileName(version, fileExt)).toFile();
    }

    public String getExternalURL() {
        return artifact.getFileURL(version, getPackagingFileExtension());
    }

    @NotNull
    private String getPackagingFileExtension() {
        String fileExt = packaging;
        if (CommonUtils.isEmpty(fileExt) || fileExt.equals(MavenArtifact.PACKAGING_BUNDLE) || fileExt.equals(MavenArtifact.FILE_POM)) {
            fileExt = MavenArtifact.FILE_JAR;
        }
        return fileExt;
    }

    public String getExternalURL(String fileType) {
        return artifact.getFileURL(version, fileType);
    }

    public String getPath() {
        return artifact.toString() + ":" + version;
    }

    @Override
    public String toString() {
        return getPath();
    }

    private File getLocalPOM() {
        if (artifact.getRepository().getType() == MavenRepository.RepositoryType.LOCAL) {
            try {
                return new File(GeneralUtils.makeURIFromFilePath(getRemotePOMLocation()));
            } catch (URISyntaxException e) {
                log.warn(e);
            }
        }
        return artifact.getRepository().getLocalCacheDir().resolve(
            artifact.getGroupId() + "/" + artifact.getVersionFileName(version, MavenArtifact.FILE_POM)).toFile();
    }

    private String getRemotePOMLocation() {
        return artifact.getFileURL(version, MavenArtifact.FILE_POM);
    }

    private void cachePOM(File localPOM) throws IOException {
        if (artifact.getRepository().getType() == MavenRepository.RepositoryType.LOCAL) {
            return;
        }
        String pomURL = getRemotePOMLocation();
        try (InputStream is = WebUtils.openConnection(pomURL, artifact.getRepository().getAuthInfo(), null).getInputStream()) {
            File folder = localPOM.getParentFile();
            if (!folder.exists() && !folder.mkdirs()) {
                throw new IOException("Can't create cache folder '" + folder.getAbsolutePath() + "'");
            }

            try (OutputStream os = new FileOutputStream(localPOM)) {
                IOUtils.fastCopy(is, os);
            }
        }
    }

    private void loadPOM(DBRProgressMonitor monitor, boolean resolveOptionalDependencies) throws IOException {
        monitor.subTask("Load POM " + this);

        File localPOM = getLocalPOM();
        log.info("Local pom is " + localPOM.getPath());
        if (!localPOM.exists()) {
            cachePOM(localPOM);
            log.info("Local pom download succeeded!");
        } else {
            log.info("Local pom found successfully!");
        }
        Document pomDocument;
        try {
            pomDocument = XMLUtils.parseDocument(localPOM);
        } catch (XMLException e) {
            throw new IOException("Error parsing POM", e);
        }
        Element root = pomDocument.getDocumentElement();
        name = CommonUtils.trim(XMLUtils.getChildElementBody(root, "name"));
        url = CommonUtils.trim(XMLUtils.getChildElementBody(root, "url"));
        version = CommonUtils.trim(XMLUtils.getChildElementBody(root, "version"));
        packaging = CommonUtils.trim(XMLUtils.getChildElementBody(root, "packaging"));
        // 默认使用jar包
        if ("${packaging.type}".equals(packaging)) {
            packaging = "jar";
        }
        description = CommonUtils.trim(XMLUtils.getChildElementBody(root, "description"));
        if (description != null) {
            description = CommonUtils.compactWhiteSpaces(description.trim());
        }
        repositories.addAll(parseRepositories(root));

        {
            // Parent
            Element parentElement = XMLUtils.getChildElement(root, "parent");
            if (parentElement != null) {
                String parentGroupId = CommonUtils.trim(XMLUtils.getChildElementBody(parentElement, "groupId"));
                String parentArtifactId = CommonUtils.trim(XMLUtils.getChildElementBody(parentElement, "artifactId"));
                String parentVersion = CommonUtils.trim(XMLUtils.getChildElementBody(parentElement, "version"));
                if (parentGroupId == null || parentArtifactId == null || parentVersion == null) {
                    log.error("Broken parent reference: " + parentGroupId + ":" + parentArtifactId + ":" + parentVersion);
                } else {
                    MavenArtifactReference parentReference = new MavenArtifactReference(
                        parentGroupId,
                        parentArtifactId,
                        null,
                        parentVersion);
                    if (this.version == null) {
                        this.version = parentReference.getVersion();
                    }
                    parent = MavenRegistry.getInstance().findArtifact(monitor, this, parentReference, null);
                    if (parent == null) {
                        log.error("Artifact [" + this + "] parent [" + parentReference + "] not found");
                    }
                }
            }
        }

        {
            // Licenses
            Element licensesElement = XMLUtils.getChildElement(root, "licenses");
            if (licensesElement != null) {
                for (Element prop : XMLUtils.getChildElementList(licensesElement, "license")) {
                    licenses.add(new MavenArtifactLicense(
                        XMLUtils.getChildElementBody(prop, "name"),
                        XMLUtils.getChildElementBody(prop, "url")
                    ));
                }
            }
        }

        // Default profile
        MavenProfile defaultProfile = new MavenProfile(DEFAULT_PROFILE_ID);
        defaultProfile.active = true;
        profiles.add(defaultProfile);
        parseProfile(monitor, defaultProfile, root, true, resolveOptionalDependencies);

        {
            // Profiles
            Element licensesElement = XMLUtils.getChildElement(root, "profiles");
            if (licensesElement != null) {
                for (Element profElement : XMLUtils.getChildElementList(licensesElement, "profile")) {
                    MavenProfile profile = new MavenProfile(XMLUtils.getChildElementBody(profElement, "id"));
                    profiles.add(profile);
                    parseProfile(monitor, profile, profElement, false, resolveOptionalDependencies);
                }
            }
        }

        monitor.worked(1);
    }

    private void parseProfile(DBRProgressMonitor monitor, MavenProfile profile, Element element, boolean isDefault, boolean resolveOptionalDependencies) {
        {
            // Activation
            Element activationElement = XMLUtils.getChildElement(element, "activation");
            if (activationElement != null) {
                String activeByDefault = XMLUtils.getChildElementBody(activationElement, "activeByDefault");
                if (!CommonUtils.isEmpty(activeByDefault)) {
                    profile.active = CommonUtils.getBoolean(activeByDefault);
                }
                String jdk = XMLUtils.getChildElementBody(activationElement, "jdk");
                if (!CommonUtils.isEmpty(jdk)) {
                    profile.active = MavenArtifact.versionMatches(System.getProperty(StandardConstants.ENV_JAVA_VERSION), jdk);
                }
                Element osElement = XMLUtils.getChildElement(activationElement, "os");
                if (osElement != null) {

                }
                Element propElement = XMLUtils.getChildElement(activationElement, "property");
                if (propElement != null) {
                    String propName = XMLUtils.getChildElementBody(propElement, "name");
                    //String propValue = XMLUtils.getChildElementBody(propElement, "value");
                    // TODO: implement real properties checks. Now enable all profiles with !prop
                    if (propName != null && propName.startsWith("!")) {
                        profile.active = true;
                    }
                }
            }
        }
        if (!profile.active) {
            // Do not parse dependencies of non-active profiles (most likely they will fail).
            return;
        }
        {
            // Properties
            Element propsElement = XMLUtils.getChildElement(element, "properties");
            if (propsElement != null) {
                for (Element prop : XMLUtils.getChildElementList(propsElement)) {
                    profile.properties.put(prop.getTagName(), XMLUtils.getElementBody(prop));
                }
            }
        }
        if (!isDefault) {
            // Repositories
            for (MavenRepository repository : parseRepositories(element)) {
                profile.addRepository(repository);
            }
        }
        {
            // Dependencies
            Element dmElement = XMLUtils.getChildElement(element, "dependencyManagement");
            if (dmElement != null) {
                profile.dependencyManagement = parseDependencies(monitor, dmElement, true, resolveOptionalDependencies);
            }
            profile.dependencies = parseDependencies(monitor, element, false, resolveOptionalDependencies);
        }
    }

    private List<MavenRepository> parseRepositories(Element element)
    {
        List<MavenRepository> repositories = new ArrayList<>();
        // Repositories
        Element repsElement = XMLUtils.getChildElement(element, "repositories");
        if (repsElement != null) {
            for (Element repElement : XMLUtils.getChildElementList(repsElement, "repository")) {
                MavenRepository repository = new MavenRepository(
                    XMLUtils.getChildElementBody(repElement, "id"),
                    XMLUtils.getChildElementBody(repElement, "name"),
                    XMLUtils.getChildElementBody(repElement, "url"),
                    MavenRepository.RepositoryType.EXTERNAL);
                String layout = XMLUtils.getChildElementBody(repElement, "layout");
                if ("legacy".equals(layout)) {
                    log.debug("Skip legacy repository [" + repository + "]");
                    continue;
                }
                Element releasesElement = XMLUtils.getChildElement(repElement, "releases");
                boolean enabled = releasesElement == null ||
                    CommonUtils.toBoolean(XMLUtils.getChildElementBody(releasesElement, "enabled"));
                if (enabled) {
                    repositories.add(repository);
                }
            }
        }
        return repositories;
    }

    private List<MavenArtifactDependency> parseDependencies(
        @NotNull DBRProgressMonitor monitor,
        @NotNull Element element,
        boolean depManagement,
        boolean resolveOptionalDependencies) {
        List<MavenArtifactDependency> result = new ArrayList<>();
        Element dependenciesElement = XMLUtils.getChildElement(element, "dependencies");
        if (dependenciesElement != null) {
            for (Element dep : XMLUtils.getChildElementList(dependenciesElement, "dependency")) {
                String groupId = evaluateString(XMLUtils.getChildElementBody(dep, "groupId"));
                String artifactId = evaluateString(XMLUtils.getChildElementBody(dep, "artifactId"));
                if (groupId == null || artifactId == null) {
                    log.warn("Broken dependency reference: " + groupId + ":" + artifactId);
                    continue;
                }
                String classifier = evaluateString(XMLUtils.getChildElementBody(dep, "classifier"));

                MavenArtifactDependency dmInfo = depManagement ? null : findDependencyManagement(groupId, artifactId);

                // Resolve scope
                String scopeName = XMLUtils.getChildElementBody(dep, "scope");
                MavenArtifactDependency.Scope scope = scopeName == null ? null : CommonUtils.valueOf(
                    MavenArtifactDependency.Scope.class, scopeName.toUpperCase(Locale.ENGLISH), null);
                if (scope == null && dmInfo != null) {
                    scope = dmInfo.getScope();
                }
                if (scope == null) {
                    scope = MavenArtifactDependency.Scope.COMPILE;
                }

                String optionalString = XMLUtils.getChildElementBody(dep, "optional");
                boolean optional = !resolveOptionalDependencies &&
                    (optionalString == null ?
                        (dmInfo != null && dmInfo.isOptional()) :
                        CommonUtils.getBoolean(optionalString));

                // Resolve version
                String version = evaluateString(XMLUtils.getChildElementBody(dep, "version"));

                if (depManagement && scope == MavenArtifactDependency.Scope.IMPORT) {
                    // Import another pom
                    if (version == null) {
                        log.error("Missing imported artifact [" + groupId + ":" + artifactId + "] version. Skip.");
                        continue;
                    }
                    MavenArtifactReference importReference = new MavenArtifactReference(
                        groupId,
                        artifactId,
                        classifier,
                        version);
                    if (resolveOptionalDependencies) {
                        importReference.setResolveOptionalDependencies(true);
                    }
                    MavenArtifactVersion importedVersion = MavenRegistry.getInstance().findArtifact(monitor, this, importReference, null);
                    if (importedVersion == null) {
                        log.error("Imported artifact [" + importReference + "] not found. Skip.");
                    }
                    if (imports == null) {
                        imports = new ArrayList<>();
                    }
                    imports.add(importedVersion);
                } else if (depManagement || (!optional && includesScope(scope, resolveOptionalDependencies))) {
                    // TODO: maybe we should include optional or PROVIDED

                    if (version == null && dmInfo != null) {
                        version = dmInfo.getVersion();
                    }
                    if (version == null) {
                        log.error("Can't resolve artifact [" + groupId + ":" + artifactId + "] version. Skip.");
                        continue;
                    }

                    MavenArtifactDependency dependency = new MavenArtifactDependency(
                        groupId,
                        artifactId,
                        classifier,
                        version,
                        scope,
                        optional);
                    result.add(dependency);

                    // Exclusions
                    Element exclusionsElement = XMLUtils.getChildElement(dep, "exclusions");
                    if (exclusionsElement != null) {
                        for (Element exclusion : XMLUtils.getChildElementList(exclusionsElement, "exclusion")) {
                            dependency.addExclusion(
                                new MavenArtifactReference(
                                    CommonUtils.notEmpty(XMLUtils.getChildElementBody(exclusion, "groupId")),
                                    CommonUtils.notEmpty(XMLUtils.getChildElementBody(exclusion, "artifactId")),
                                    null,
                                    ""));
                        }
                    }
                    if (dmInfo != null) {
                        List<MavenArtifactReference> dmExclusions = dmInfo.getExclusions();
                        if (dmExclusions != null) {
                            for (MavenArtifactReference dmEx : dmExclusions) {
                                dependency.addExclusion(dmEx);
                            }
                        }
                    }
                }
            }
        }
        return result;
    }

    private boolean includesScope(MavenArtifactDependency.Scope scope, boolean resolveOptionalDependencies) {
        return
            scope == MavenArtifactDependency.Scope.COMPILE ||
            scope == MavenArtifactDependency.Scope.RUNTIME ||
            (resolveOptionalDependencies && scope == MavenArtifactDependency.Scope.PROVIDED);
    }

    private MavenArtifactDependency findDependencyManagement(String groupId, String artifactId) {
        for (MavenProfile profile : profiles) {
            if (profile.isActive() && profile.dependencyManagement != null) {
                for (MavenArtifactDependency dmArtifact : profile.dependencyManagement) {
                    if (dmArtifact.getGroupId().equals(groupId) &&
                        dmArtifact.getArtifactId().equals(artifactId)) {
                        return dmArtifact;
                    }
                }
            }
        }
        // Check in imported BOMs
        if (imports != null) {
            for (MavenArtifactVersion i : imports) {
                MavenArtifactDependency dependencyManagement = i.findDependencyManagement(groupId, artifactId);
                if (dependencyManagement != null) {
                    return dependencyManagement;
                }
            }
        }
        return parent == null ? null : parent.findDependencyManagement(groupId, artifactId);
    }

    private String evaluateString(String value) {
        if (value == null) {
            return null;
        }
        return GeneralUtils.replaceVariables(value, propertyResolver);
    }

    @NotNull
    public Collection<MavenRepository> getActiveRepositories() {
        Map<String, MavenRepository> result = new LinkedHashMap<>();
        for (MavenRepository rep : repositories) {
            result.put(rep.getId(), rep);
        }
        for (MavenArtifactVersion v = MavenArtifactVersion.this; v != null; v = v.parent) {
            for (MavenProfile profile : v.profiles) {
                if (profile.isActive()) {
                    List<MavenRepository> pr = profile.getRepositories();
                    if (pr != null) {
                        for (MavenRepository repository : pr) {
                            result.put(repository.getId(), repository);
                        }
                    }
                }
            }
        }
        return result.values();
    }
}
