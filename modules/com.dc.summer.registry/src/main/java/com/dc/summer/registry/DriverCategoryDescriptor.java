

package com.dc.summer.registry;

import com.dc.summer.model.impl.AbstractDescriptor;
import org.eclipse.core.runtime.IConfigurationElement;
import com.dc.utils.CommonUtils;

/**
 * DriverCategoryDescriptor
 */
public class DriverCategoryDescriptor extends AbstractDescriptor
{
    private String id;
    private String name;
    private String description;
    private boolean promoted;
    private int rank;

    public DriverCategoryDescriptor(IConfigurationElement config)
    {
        super(config.getContributor().getName());
        this.id = config.getAttribute(RegistryConstants.ATTR_ID);
        this.name = config.getAttribute(RegistryConstants.ATTR_NAME);
        this.description = config.getAttribute(RegistryConstants.ATTR_DESCRIPTION);
        this.promoted = CommonUtils.toBoolean(config.getAttribute(RegistryConstants.ATTR_PROMOTED));
        this.rank = CommonUtils.toInt(config.getAttribute("rank"));
    }

    public String getId()
    {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    public boolean isPromoted() {
        return promoted;
    }

    public int getRank() {
        return rank;
    }

    @Override
    public String toString() {
        return id;
    }
}
