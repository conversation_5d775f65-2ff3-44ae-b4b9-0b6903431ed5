
package com.dc.summer.ext.oracle.data;

import com.dc.summer.ext.oracle.model.OracleConstants;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.impl.jdbc.data.JDBCContentXML;
import com.dc.summer.model.struct.DBSTypedObject;
import com.dc.utils.BeanUtils;

import java.io.IOException;
import java.io.InputStream;
import java.sql.SQLException;
import java.sql.SQLXML;

/**
 * XML content
 */
public class OracleContentXML extends JDBCContentXML {
    OracleContentXML(DBCExecutionContext executionContext, SQLXML xml)
    {
        super(executionContext, xml);
    }

    @Override
    protected OracleContentXML createNewContent()
    {
        return new OracleContentXML(executionContext, null);
    }

    @Override
    public void bindParameter(
        JDBCSession session,
        JDBCPreparedStatement preparedStatement,
        DBSTypedObject columnType,
        int paramIndex)
        throws DBCException
    {
        try {
            if (storage != null) {
                try (InputStream streamReader = storage.getContentStream()) {
                    final Object xmlObject = createXmlObject(session, streamReader);

                    preparedStatement.setObject(
                        paramIndex,
                        xmlObject);
                }
            } else if (xml != null) {
                preparedStatement.setObject(
                    paramIndex,
                    xml);
            } else {
                preparedStatement.setNull(paramIndex, java.sql.Types.SQLXML, columnType.getTypeName());
            }
        }
        catch (SQLException e) {
            throw new DBCException(e, session.getExecutionContext());
        }
        catch (IOException e) {
            throw new DBCException("IO error while reading XML", e);
        }
    }

    static Object createXmlObject(JDBCSession session, InputStream stream) throws DBCException
    {
        try {
            return BeanUtils.invokeStaticMethod(
                DBUtils.getDriverClass(session.getExecutionContext().getDataSource(), OracleConstants.XMLTYPE_CLASS_NAME),
                "createXML",
                new Class[] {java.sql.Connection.class, java.io.InputStream.class},
                new Object[] {session.getOriginal(), stream});
        } catch (SQLException e) {
            throw new DBCException(e, session.getExecutionContext());
        } catch (Throwable e) {
            throw new DBCException("Internal error when creating XMLType", e, session.getExecutionContext());
        }
    }

/*
    @Override
    protected XMLType createNewOracleObject(Connection connection) throws DBCException, IOException, SQLException
    {
        final InputStream contentStream = storage.getContentStream();
        try {
            return XMLType.createXML(connection, contentStream);
        } finally {
            ContentUtils.close(contentStream);
        }
    }

    @Override
    protected DBDContentStorage makeStorageFromOpaque(DBRProgressMonitor monitor, XMLType opaque) throws DBCException
    {
        long contentLength = opaque.getLength();
        if (contentLength < 4000) {
            try {
                return new StringContentStorage(opaque.getStringVal());
            } catch (SQLException e) {
                throw new DBCException(e);
            }
        } else {
            // Create new local storage
            IFile tempFile;
            try {
                tempFile = ContentUtils.createTempContentFile(monitor, "opaque" + opaque.hashCode());
            }
            catch (IOException e) {
                throw new DBCException(e);
            }
            try {
                ContentUtils.copyReaderToFile(monitor, opaque.getClobVal().getCharacterStream(), contentLength, null, tempFile);
            } catch (Exception e) {
                ContentUtils.deleteTempFile(monitor, tempFile);
                throw new DBCException(e);
            }
            return new TemporaryContentStorage(tempFile);
        }
    }
*/
}
