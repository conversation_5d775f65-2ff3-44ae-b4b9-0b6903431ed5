
package com.dc.summer.ext.oracle.tasks;

import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.summer.ext.oracle.model.OracleMaterializedView;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.impl.edit.SQLDatabasePersistAction;
import com.dc.summer.model.sql.task.SQLToolExecuteHandler;

import java.util.List;

public class OracleToolMViewRefresh extends SQLToolExecuteHandler<OracleMaterializedView, OracleToolMViewRefreshSettings> {
    @Override
    public OracleToolMViewRefreshSettings createToolSettings() {
        return new OracleToolMViewRefreshSettings();
    }

    @Override
    public void generateObjectQueries(DBCSession session, OracleToolMViewRefreshSettings settings, List<DBEPersistAction> queries, OracleMaterializedView object) throws DBCException {
        String method = "";
        if (settings.isFast()) method += "f";
        if (settings.isForce()) method += "?";
        if (settings.isComplete()) method += "c";
        if (settings.isAlways()) method += "a";
        if (settings.isRecomputed()) method += "p";

        String sql = "CALL DBMS_MVIEW.REFRESH('" + object.getFullyQualifiedName(DBPEvaluationContext.DDL) + "'," +
                "'" + method + "'" +
                ")";
        queries.add(new SQLDatabasePersistAction(sql));
    }

    public boolean needsRefreshOnFinish() {
        return true;
    }
}
