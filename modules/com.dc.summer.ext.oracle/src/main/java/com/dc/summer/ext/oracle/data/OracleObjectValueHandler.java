
package com.dc.summer.ext.oracle.data;

import com.dc.summer.model.impl.jdbc.data.handlers.JDBCStructValueHandler;

/**
 * Object type support
 */
public class OracleObjectValueHandler extends J<PERSON>BCStructValueHandler {

    public static final OracleObjectValueHandler INSTANCE = new OracleObjectValueHandler();

/*
    @NotNull
    @Override
    public Class<Object> getValueObjectType(@NotNull DBSTypedObject attribute)
    {
        return java.lang.Object.class;
    }

    @Override
    public Object getValueFromObject(@NotNull DBCSession session, @NotNull DBSTypedObject type, Object object, boolean copy) throws DBCException
    {
        if (object == null) {
            return new OracleObjectValue(null);
        } else if (object instanceof Struct) {
            return super.getValueFromObject(session, type, object, copy);
        } else if (object instanceof OracleObjectValue) {
            return copy ? new OracleObjectValue(((OracleObjectValue) object).getValue()) : (OracleObjectValue)object;
        } else {
            return new OracleObjectValue(object);
        }
    }
*/

}
