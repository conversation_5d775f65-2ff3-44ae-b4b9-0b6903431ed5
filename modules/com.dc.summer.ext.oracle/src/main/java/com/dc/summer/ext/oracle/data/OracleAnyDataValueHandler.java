package com.dc.summer.ext.oracle.data;

import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.impl.jdbc.data.handlers.JDBCObjectValueHandler;
import com.dc.summer.model.struct.DBSTypedObject;
import lombok.extern.slf4j.Slf4j;
import oracle.sql.ANYDATA;

import java.lang.reflect.Method;
import java.sql.SQLException;

@Slf4j
public class OracleAnyDataValueHandler extends JDBCObjectValueHandler {

    public static final OracleAnyDataValueHandler INSTANCE = new OracleAnyDataValueHandler();

    /**
     * @see ANYDATA#stringValue()
     */
    @Override
    protected Object fetchColumnValue(DBCSession session, JDBCResultSet resultSet, DBSTypedObject type, int index) throws DBCException, SQLException {
        try {
            Object object = resultSet.getObject(index);
            if (object == null) {
                return null;
            }
            Method stringValue = object.getClass().getMethod("stringValue");
            String anydata = (String) stringValue.invoke(object);

            if (anydata.contains("ANYDATA Value")) {
                return anydata.substring(anydata.lastIndexOf("ANYDATA Value: \"") + 16, anydata.lastIndexOf("\""));
            }
        } catch (Exception e) {
            log.debug(e.getMessage());
        }
        return "[ANYDATA]";
    }
}
