package com.dc.summer.ext.doris;

import com.dc.summer.DBException;
import com.dc.summer.ext.doris.model.DorisDataSource;
import com.dc.summer.ext.mysql.MySQLDataSourceProvider;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.runtime.DBRProgressMonitor;

public class DorisDataSourceProvider extends MySQLDataSourceProvider {

    @Override
    public DBPDataSource openDataSource(DBRProgressMonitor monitor, DBPDataSourceContainer container) throws DBException {
        return new DorisDataSource(monitor, container);
    }

}
