package com.dc.summer.ext.redis.model;

import com.dc.summer.ext.redis.RedisConstants;
import com.dc.summer.ext.redis.RedisUtils;
import com.dc.summer.ext.redis.exec.RedisBaseResultSet;
import com.dc.summer.ext.redis.exec.RedisKeyGetStatement;
import com.dc.summer.ext.redis.exec.RedisKeySetStatement;
import com.dc.summer.ext.redis.exec.RedisKeySizeStatement;
import com.dc.summer.ext.redis.exec.RedisSession;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.model.DBFetchProgress;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBPNamedObject2;
import com.dc.summer.model.DBPQualifiedObject;
import com.dc.summer.model.DBPRefreshableObject;
import com.dc.summer.model.DBPSaveableObject;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.data.DBDDataFilter;
import com.dc.summer.model.data.DBDDataReceiver;
import com.dc.summer.model.data.DBDValueHandler;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCExecutionSource;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.DBCStatement;
import com.dc.summer.model.exec.DBCStatistics;
import com.dc.summer.model.impl.AbstractExecutionSource;
import com.dc.summer.model.impl.DBObjectNameCaseTransformer;
import com.dc.summer.model.impl.data.ExecuteBatchImpl;
import com.dc.summer.model.impl.sql.SelectTableGenerator;
import com.dc.summer.model.meta.Association;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSAttributeBase;
import com.dc.summer.model.struct.DBSDataContainer;
import com.dc.summer.model.struct.DBSDataManipulator;
import com.dc.summer.model.struct.DBSEntity;
import com.dc.summer.model.struct.DBSEntityAssociation;
import com.dc.summer.model.struct.DBSEntityAttribute;
import com.dc.summer.model.struct.DBSEntityType;
import com.dc.summer.model.struct.DBSObject;
import com.dc.utils.ArrayUtils;
import com.dc.utils.CommonUtils;
import redis.clients.jedis.commands.JedisCommands;
import redis.clients.jedis.commands.KeyCommands;

public class RedisKey implements DBSEntity, DBPNamedObject2, DBSDataContainer, DBSDataManipulator, DBPSaveableObject, DBPQualifiedObject, DBPRefreshableObject, SelectTableGenerator {
    private static final Log log = Log.getLog(RedisKey.class);
    private static final List<RedisKey> EMPTY_SUB_KEYS = Collections.emptyList();
    private final RedisDatabase database;
    private final RedisKey parentKey;
    private String keyName;
    private RedisKeyType keyType;
    private Long ttl;
    private long keySize = -1L;
    private final boolean isFolder;
    private boolean persisted;
    private List<RedisKey> subKeys;
    private List<RedisKeyAttribute> attributes = null;
    private long memoryUsage = -1L;

    // $FF: synthetic field
    private static volatile int[] $SWITCH_TABLE$com$dbeaver$db$redis$model$RedisKeyType;

    public RedisKey(@NotNull RedisDatabase database, @Nullable RedisKey parentKey, @NotNull String keyName, boolean isFolder) {
        this.database = database;
        this.parentKey = parentKey;
        this.keyName = keyName;
        this.isFolder = isFolder;
        this.persisted = true;
    }

    public @Association
    @Nullable RedisKey getParentKey() {
        return this.parentKey;
    }

    public @NotNull RedisDataSource getDataSource() {
        return this.database.getDataSource();
    }

    @Property(
            viewable = true,
            editable = true,
            valueTransformer = DBObjectNameCaseTransformer.class,
            order = 1
    )
    public @NotNull String getName() {
        return this.keyName;
    }

    public RedisDatabase getDatabase() {
        return this.database;
    }

    @Property(
            viewable = true,
            order = 3
    )
    public @NotNull RedisKeyType getKeyType(DBRProgressMonitor monitor) {
        if (this.keyType == null) {
            if (this.isFolder) {
                return RedisKeyType.folder;
            }
            try (RedisSession session = DBUtils.openMetaSession(monitor, this, "Read key [" + this.getFullyQualifiedName(DBPEvaluationContext.DML) + "] type")) {
                RedisUtils.selectCurDatabase(session, this.getDatabase());
                if (session.supportsCommands(JedisCommands.class)) {
                    this.keyType = RedisKeyType.valueOf(((JedisCommands) session.getCommands(JedisCommands.class)).type(this.getFullyQualifiedName(DBPEvaluationContext.DML)));
                } else {
                    this.keyType = RedisKeyType.valueOf(((KeyCommands) session.getCommands(KeyCommands.class)).type(this.getFullyQualifiedName(DBPEvaluationContext.DML)));
                }
            } catch (Throwable var13) {
                log.error("Error getting key type", var13);
                this.keyType = RedisKeyType.string;
            }
        }
        return this.keyType;
    }

    @Property(
            viewable = false,
            order = 4
    )
    public long getTTL(DBRProgressMonitor monitor) {
        if (this.ttl == null) {
            try (RedisSession session = DBUtils.openMetaSession(monitor, this, "Read key [" + this.getFullyQualifiedName(DBPEvaluationContext.DML) + "] TTL")) {
                RedisUtils.selectCurDatabase(session, this.getDatabase());
                if (session.supportsCommands(JedisCommands.class)) {
                    this.ttl = session.getCommands(JedisCommands.class).ttl(this.getFullyQualifiedName(DBPEvaluationContext.DML));
                } else {
                    this.ttl = session.getCommands(KeyCommands.class).ttl(this.getFullyQualifiedName(DBPEvaluationContext.DML));
                }
            } catch (Throwable e) {
                log.error(e);
            }
            if (this.ttl == null) {
                this.ttl = -1L;
            }
        }
        return this.ttl;
    }

    public @Nullable Long getTTL() {
        return this.ttl;
    }

    public void setTTL(long ttl) {
        this.ttl = ttl;
    }

    @Property(
            viewable = false,
            order = 5
    )
    public long getKeySize(DBRProgressMonitor monitor) throws DBCException {
        if (this.keySize < 0L) {
            try (RedisSession session = DBUtils.openMetaSession(monitor, this, "Count key size")) {
                RedisUtils.selectCurDatabase(session, this.getDatabase());
                this.countData(new AbstractExecutionSource(this, DBUtils.getDefaultContext(this, true), this), session, (DBDDataFilter) null, 0L);
            }
        }
        return this.keySize;
    }

    public long getMemoryUsage(DBRProgressMonitor monitor) {
        if (this.memoryUsage < 0L) {
            try (RedisSession session = DBUtils.openMetaSession(monitor, this, "Read key [" + this.getFullyQualifiedName(DBPEvaluationContext.DML) + "] Memory Usage")) {
                RedisUtils.selectCurDatabase(session, this.getDatabase());
                if (session.supportsCommands(JedisCommands.class)) {
                    this.memoryUsage = session.getCommands(JedisCommands.class).memoryUsage(this.getFullyQualifiedName(DBPEvaluationContext.DML), 0);
                } else {
                    this.memoryUsage = session.getCommands(KeyCommands.class).memoryUsage(this.getFullyQualifiedName(DBPEvaluationContext.DML), 0);
                }
            } catch (Throwable e) {
                log.error(e);
            }
        }
        return this.memoryUsage;
    }

    public long getMemoryUsage() {
        return memoryUsage;
    }

    public String getDescription() {
        return null;
    }

    public DBSObject getParentObject() {
        return this.parentKey != null ? this.parentKey : this.database;
    }

    public boolean isPersisted() {
        return this.persisted;
    }

    public void setPersisted(boolean persisted) {
        this.persisted = persisted;
    }

    public boolean isFolder() {
        return this.isFolder;
    }

    public @Association List<RedisKey> getKeys(DBRProgressMonitor monitor) throws DBCException {
        if (this.subKeys == null) {
            this.subKeys = RedisUtils.getKeys(monitor, this.database, this, (String) null, true, null, null);
        }

        return this.subKeys;
    }

    public void setSubKeys(List<String> keys, String divider) {
        if (keys != null) {
            if (keys.isEmpty()) {
                if (this.subKeys == null) {
                    this.subKeys = EMPTY_SUB_KEYS;
                }
            } else {
                for (String key : keys) {
                    this.addSubKey(key, divider);
                }
            }
        }
    }

    public void addSubKey(@NotNull String key, @NotNull String divider) {
        List<String> foundSubKeys = splitSubKeys(key, divider);
        if (this.subKeys == null) {
            this.subKeys = new ArrayList<>();
        }

        RedisKey parent = this;
        RedisKey child = null;

        for (int i = 0; i < foundSubKeys.size(); ++i) {

            for (RedisKey k : parent.subKeys) {
                if (k.keyName.equals(foundSubKeys.get(i))) {
                    child = k;
                    break;
                }
            }

            if (child == null) {
                child = new RedisKey(this.database, parent, (String) foundSubKeys.get(i), i != foundSubKeys.size() - 1);
                parent.subKeys.add(child);
                child.subKeys = new ArrayList();
            }

            if (i != foundSubKeys.size() - 1) {
                parent = child;
                child = null;
            }
        }

    }

    public static int findIndexOfValidDivider(@NotNull String key, @NotNull String divider) {
        for (int i = 0; i < key.length(); ++i) {
            int index = key.indexOf(divider, i);
            if (index == -1) {
                return -1;
            }

            if (index != i) {
                return index;
            }
        }

        return -1;
    }

    public static @NotNull List<String> splitSubKeys(@NotNull String key, @NotNull String divider) {
        List<String> listOfSubKeys = new ArrayList();

        String subKey;
        do {
            int divPos = key.indexOf(divider);
            String keyName;
            if (divPos == 0) {
                int newDivider = findIndexOfValidDivider(key, divider);
                if (newDivider != -1) {
                    subKey = key.substring(newDivider + divider.length());
                    keyName = key.substring(0, newDivider);
                } else {
                    keyName = key;
                    subKey = null;
                }
            } else if (divPos != -1) {
                subKey = key.substring(divPos + divider.length());
                keyName = key.substring(0, divPos);
            } else {
                keyName = key;
                subKey = null;
            }

            listOfSubKeys.add(keyName);
            key = subKey;
        } while (subKey != null);

        return listOfSubKeys;
    }

    @Property(
            viewable = true,
            valueTransformer = DBObjectNameCaseTransformer.class,
            order = 2
    )
    public @NotNull String getFullyQualifiedName(DBPEvaluationContext context) {
        if (this.parentKey == null) {
            return this.getName();
        } else {
            String keyDivider = this.getDataSource().getKeyDivider();
            StringBuilder fqn = new StringBuilder();

            for (RedisKey key = this; key != null; key = key.parentKey) {
                if (fqn.length() > 0) {
                    fqn.insert(0, keyDivider);
                }

                fqn.insert(0, key.keyName);
            }

            return fqn.toString();
        }
    }

    public String[] getSupportedFeatures() {
        return new String[]{"data.select", "data.update", "data.delete", "data.insert", "data.key.value"};
    }

    public @NotNull DBCStatistics readData(@NotNull DBCExecutionSource source,
                                           @NotNull DBCSession session,
                                           @NotNull DBDDataReceiver dataReceiver,
                                           @Nullable DBDDataFilter dataFilter,
                                           long firstRow,
                                           long maxRows,
                                           long flags,
                                           int stage,
                                           int fetchSize, List<Object> data) throws DBCException {
        if (this.isFolder) {
            throw new DBCException("Cannot read data from key folder");
        } else {
            DBCStatistics statistics = new DBCStatistics();
            if (firstRow > 0L) {
                return statistics;
            } else {
                try (dataReceiver) {
                    try (RedisKeyGetStatement keyStatement = new RedisKeyGetStatement((RedisSession) session, this, firstRow, maxRows)) {
                        keyStatement.executeStatement();
                        try (RedisBaseResultSet resultSet = keyStatement.openResultSet()) {
                            dataReceiver.fetchStart(session, resultSet, firstRow, maxRows);
                            try {
                                DBFetchProgress fetchProgress = new DBFetchProgress(session.getProgressMonitor());

                                while (resultSet.nextRow()) {
                                    dataReceiver.fetchRow(session, resultSet);
                                    fetchProgress.monitorRowFetch();
                                }

                                fetchProgress.dumpStatistics(statistics);
                                return statistics;
                            } finally {
                                dataReceiver.fetchEnd(session, resultSet);
                            }
                        }
                    }
                }
            }
        }
    }

    public void createSelectStatement(@NotNull DBRProgressMonitor monitor, @NotNull StringBuilder sql) {
        RedisKeyType keyType = this.getKeyType(monitor);
        switch (keyType) {
            case list:
                sql.append("LRANGE");
                break;
            case set:
                sql.append("SMEMBERS");
                break;
            case zset:
                sql.append("ZRANGEBYSCORE");
                break;
            case hash:
                sql.append("HGETALL");
                break;
            default:
                sql.append("GET");
        }

        sql.append(" ").append(this.getFullyQualifiedName(DBPEvaluationContext.DDL));
        if (keyType == RedisKeyType.list) {
            sql.append(" -100 100");
        } else if (keyType == RedisKeyType.zset) {
            sql.append(" -inf +inf");
        }

        sql.append(";");
    }

    public long countData(@NotNull DBCExecutionSource source, @NotNull DBCSession session, @Nullable DBDDataFilter dataFilter, long flags) throws DBCException {
        if (this.keySize < 0L) {
            try (RedisKeySizeStatement keyStatement = new RedisKeySizeStatement((RedisSession) session, this.getFullyQualifiedName(DBPEvaluationContext.DML), this.getKeyType(session.getProgressMonitor()))) {
                keyStatement.executeStatement();
                this.keySize = keyStatement.getKeySize();
            }
        }

        return this.keySize;
    }

    @NotNull
    public DBSDataManipulator.ExecuteBatch insertData(@NotNull DBCSession session, @NotNull DBSAttributeBase[] attributes, DBDDataReceiver keysReceiver, @NotNull DBCExecutionSource source, Map<String, Object> options) throws DBCException {
        if (this.getKeyType(session.getProgressMonitor()) == RedisKeyType.string) {
            throw new DBCException("Can't add second value to string key");
        } else {
            return this.updateData(session, new DBSAttributeBase[0], attributes, keysReceiver, source);
        }
    }

    @NotNull
    public DBSDataManipulator.ExecuteBatch updateData(@NotNull DBCSession session, @NotNull DBSAttributeBase[] updateAttributes, final @NotNull DBSAttributeBase[] keyAttributes, DBDDataReceiver keysReceiver, @NotNull DBCExecutionSource source) throws DBCException {
        // TODO redis
        //        return new ExecuteBatchImpl(keyAttributes, (DBDDataReceiver) null, false, updateAttributes) {
        return new ExecuteBatchImpl(keyAttributes, null, false) {
            boolean isInsert;

            {
                // TODO redis
//                this.isInsert = ArrayUtils.isEmpty(var5);
            }

            protected @NotNull DBCStatement prepareStatement(@NotNull DBCSession session, DBDValueHandler[] handlers, Object[] attributeValues, Map<String, Object> options) throws DBCException {
                return new RedisKeySetStatement((RedisSession) session, RedisKey.this, RedisKey.this.getKeyType(session.getProgressMonitor()), ArrayUtils.isEmpty(keyAttributes) ? null : keyAttributes[0].getName(), (Object) null);
            }

            protected void bindStatement(@NotNull DBDValueHandler[] handlers, @NotNull DBCStatement statement, Object[] attributeValues) throws DBCException {
                RedisKeySetStatement setStatement = (RedisKeySetStatement) statement;
                if (attributeValues.length > 1) {
                    setStatement.setName(CommonUtils.toString(attributeValues[this.isInsert ? 0 : 1]));
                }

                if (attributeValues.length == 1) {
                    setStatement.setValue(attributeValues[0]);
                } else {
                    setStatement.setValue(attributeValues[this.isInsert ? 1 : 0]);
                    if (attributeValues.length == 3) {
                        setStatement.setOldValue(attributeValues[this.isInsert ? 1 : 2]);
                    }
                }

            }
        };
    }

    @NotNull
    public DBSDataManipulator.ExecuteBatch deleteData(@NotNull DBCSession session, @NotNull DBSAttributeBase[] keyAttributes, @NotNull DBCExecutionSource source) throws DBCException {
        return new ExecuteBatchImpl(keyAttributes, (DBDDataReceiver) null, false) {
            protected @NotNull DBCStatement prepareStatement(@NotNull DBCSession session, DBDValueHandler[] handlers, Object[] attributeValues, Map<String, Object> options) throws DBCException {
                return new RedisKeySetStatement((RedisSession) session, RedisKey.this, RedisKey.this.getKeyType(session.getProgressMonitor()), (String) null, (Object) null);
            }

            protected void bindStatement(@NotNull DBDValueHandler[] handlers, @NotNull DBCStatement statement, Object[] attributeValues) throws DBCException {
                String nameOrValue = CommonUtils.toString(attributeValues.length > 1 ? attributeValues[1] : attributeValues[0]);
                ((RedisKeySetStatement) statement).setName(nameOrValue);
            }
        };
    }

    public @NotNull DBCStatistics truncateData(@NotNull DBCSession session, @NotNull DBCExecutionSource source) throws DBCException {
        throw new DBCException("Truncate not supported");
    }

    public String toString() {
        return this.getFullyQualifiedName(DBPEvaluationContext.DML);
    }

    public DBSObject refreshObject(@NotNull DBRProgressMonitor monitor) throws DBException {
        this.keyType = null;
        this.ttl = null;
        this.keySize = -1L;
        this.subKeys = null;
        return this;
    }

    public @NotNull DBSEntityType getEntityType() {
        return RedisConstants.TYPE_KEY;
    }

    public List<RedisKeyAttribute> getAttributes(@NotNull DBRProgressMonitor monitor) {
        if (this.attributes == null) {
            this.attributes = new ArrayList();
            switch (this.getKeyType(monitor)) {
                case string:
                    this.attributes.add(new RedisKeyAttribute(this, "output", 0));
                    break;
                case list:
                default:
                    this.attributes.add(new RedisKeyAttribute(this, "name", 0));
                    this.attributes.add(new RedisKeyAttribute(this, "value", 1));
                    break;
                case set:
                    this.attributes.add(new RedisKeyAttribute(this, "value", 1));
                    break;
                case zset:
                    this.attributes.add(new RedisKeyAttribute(this, "score", 1));
                    this.attributes.add(new RedisKeyAttribute(this, "value", 1));
            }
        }

        return this.attributes;
    }

    public DBSEntityAttribute getAttribute(@NotNull DBRProgressMonitor monitor, @NotNull String attributeName) throws DBException {
        return (DBSEntityAttribute) DBUtils.findObject(this.getAttributes(monitor), attributeName);
    }

    public List<RedisKeySimpleConstraint> getConstraints(@NotNull DBRProgressMonitor monitor) throws DBException {
        RedisKeySimpleConstraint constraint;
        switch (this.getKeyType(monitor)) {
            case string:
                constraint = new RedisKeySimpleConstraint(this);
                break;
            case list:
            case set:
            default:
                constraint = new RedisKeyNameConstraint(this);
                break;
            case zset:
                constraint = new RedisKeyScoredConstraint(this);
        }

        return Collections.singletonList(constraint);
    }

    public Collection<? extends DBSEntityAssociation> getAssociations(@NotNull DBRProgressMonitor monitor) throws DBException {
        return null;
    }

    public Collection<? extends DBSEntityAssociation> getReferences(@NotNull DBRProgressMonitor monitor) throws DBException {
        return null;
    }

    public void setName(String newName) {
        this.keyName = newName;
    }

    public void setKeyType(@NotNull RedisKeyType keyType) {
        this.keyType = keyType;
    }

    // $FF: synthetic method
    static int[] $SWITCH_TABLE$com$dbeaver$db$redis$model$RedisKeyType() {
        int[] var10000 = $SWITCH_TABLE$com$dbeaver$db$redis$model$RedisKeyType;
        if (var10000 != null) {
            return var10000;
        } else {
            int[] var0 = new int[RedisKeyType.values().length];

            try {
                var0[RedisKeyType.folder.ordinal()] = 6;
            } catch (NoSuchFieldError var7) {
            }

            try {
                var0[RedisKeyType.hash.ordinal()] = 5;
            } catch (NoSuchFieldError var6) {
            }

            try {
                var0[RedisKeyType.list.ordinal()] = 2;
            } catch (NoSuchFieldError var5) {
            }

            try {
                var0[RedisKeyType.none.ordinal()] = 7;
            } catch (NoSuchFieldError var4) {
            }

            try {
                var0[RedisKeyType.set.ordinal()] = 3;
            } catch (NoSuchFieldError var3) {
            }

            try {
                var0[RedisKeyType.string.ordinal()] = 1;
            } catch (NoSuchFieldError var2) {
            }

            try {
                var0[RedisKeyType.zset.ordinal()] = 4;
            } catch (NoSuchFieldError var1) {
            }

            $SWITCH_TABLE$com$dbeaver$db$redis$model$RedisKeyType = var0;
            return var0;
        }
    }
}
