package com.dc.summer.ext.redis.exec;

import java.util.List;
import com.dc.summer.Log;
import com.dc.summer.model.DBPDataKind;
import com.dc.summer.model.exec.DBCAttributeMetaData;
import com.dc.summer.model.exec.DBCException;
import redis.clients.jedis.commands.JedisCommands;

public class RedisListResultSet extends RedisBaseResultSet {
   private static final Log log = Log.getLog(RedisListResultSet.class);
   public static final String ATTR_VALUE = "value";
   private final RedisBaseResultSet.OutputAttribute valueAttribute;
   private String keyName;
   private String curValue;

   public RedisListResultSet(RedisBaseStatement statement, String keyName) {
      super(statement);
      this.valueAttribute = new RedisBaseResultSet.OutputAttribute("value", 0, 1, DBPDataKind.OBJECT);
      this.keyName = keyName;
   }

   public Object getAttributeValue(int index) throws DBCException {
      if (index == 0) {
         return this.curValue;
      } else {
         throw new DBCException("Bad attribute index '" + index + "'");
      }
   }

   public Object getAttributeValue(String name) throws DBCException {
      if (name.equals("value")) {
         return this.curValue;
      } else {
         throw new DBCException("Attribute '" + name + "' not supported");
      }
   }

   public boolean nextRow() throws DBCException {
      try {
         ++this.rowNumber;
         if (this.session.supportsCommands(JedisCommands.class)) {
            this.curValue = this.session.getCommands(JedisCommands.class).lindex(this.keyName, this.statement.getOffset() + (long)this.rowNumber);
            return this.curValue != null;
         } else {
            throw new DBCException("List commands not supported");
         }
      } catch (Exception var2) {
         throw new DBCException("Error reading list element", var2);
      }
   }

   public boolean moveTo(int position) {
      this.rowNumber = position;
      return true;
   }

   protected void fillMetaData(List<DBCAttributeMetaData> attributes) {
      attributes.add(this.valueAttribute);
   }
}
