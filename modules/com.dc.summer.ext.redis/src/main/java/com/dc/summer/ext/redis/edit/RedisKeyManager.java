package com.dc.summer.ext.redis.edit;

import com.dc.summer.ext.redis.RedisUtils;
import com.dc.summer.ext.redis.exec.RedisSession;
import com.dc.summer.ext.redis.model.RedisDatabase;
import com.dc.summer.ext.redis.model.RedisKey;
import com.dc.summer.ext.redis.model.RedisKeyWithValue;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import com.dc.code.NotNull;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.edit.DBECommandContext;
import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.impl.edit.DirectDatabasePersistAction;
import com.dc.summer.model.impl.sql.edit.SQLObjectEditor;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.model.struct.cache.DBSObjectCache;
import com.dc.summer.utils.RuntimeUtils;
import com.dc.utils.CommonUtils;
import redis.clients.jedis.commands.JedisCommands;

public class RedisKeyManager extends SQLObjectEditor<RedisKey, RedisDatabase> {
   public long getMakerOptions(DBPDataSource dataSource) {
      return 1L;
   }

   public boolean canCreateObject(Object container) {
      return container instanceof RedisDatabase;
   }

   public boolean canDeleteObject(RedisKey object) {
      return true;
   }

   protected RedisKey createDatabaseObject(DBRProgressMonitor monitor, DBECommandContext context, Object container, Object copyFrom, Map<String, Object> options) {
      return new RedisKeyWithValue((RedisDatabase)container, (RedisKey)null, "new key", false, (Object)null);
   }

   public DBSObjectCache<? extends DBSObject, RedisKey> getObjectsCache(RedisKey object) {
      return object.getDatabase().getKeyCache();
   }

   protected void addObjectCreateActions(final DBRProgressMonitor monitor, DBCExecutionContext executionContext, List<DBEPersistAction> actions, final SQLObjectEditor<RedisKey, RedisDatabase>.ObjectCreateCommand command, Map<String, Object> options) {
      if (command.getObject() instanceof RedisKeyWithValue) {
         actions.add(new DirectDatabasePersistAction("Add key") {

            public void afterExecute(DBCSession session, Throwable error) throws DBCException {
               RedisKeyWithValue key = (RedisKeyWithValue)command.getObject();
               RedisSession redisSession = (RedisSession)session;
               RedisUtils.selectCurDatabase(redisSession, key.getDatabase());
               String name = key.getFullyQualifiedName(DBPEvaluationContext.DDL);
               Object value = key.getValue();
               if (redisSession.supportsCommands(JedisCommands.class)) {
                  JedisCommands commands = redisSession.getCommands(JedisCommands.class);
                  switch (key.getKeyType(monitor)) {
                     case string:
                        commands.set(name, (String)value);
                        break;
                     case list:
                        commands.lpush(name, RedisKeyManager.splitValue(value));
                        break;
                     case set:
                        commands.sadd(name, RedisKeyManager.splitValue(value));
                        break;
                     case zset:
                        commands.zadd(name, RedisKeyManager.splitMapValue(value, CommonUtils::toDouble));
                        break;
                     case hash:
                        commands.hset(name, RedisKeyManager.splitMapValue(value, Function.identity()));
                        break;
                     default:
                        throw new DBCException("Unexpected value: " + key.getKeyType(monitor));
                  }

                  Long ttl = key.getTTL();
                  if (ttl != null && ttl < 0L && redisSession.supportsCommands(JedisCommands.class)) {
                     redisSession.getCommands(JedisCommands.class).expire(key.getFullyQualifiedName(DBPEvaluationContext.DDL), (long)ttl.intValue());
                  }

               } else {
                  throw new DBCException("Jedis commands not supported");
               }
            }

         });
      }
   }

   protected void addObjectDeleteActions(DBRProgressMonitor monitor, DBCExecutionContext executionContext, List<DBEPersistAction> actions, final SQLObjectEditor<RedisKey, RedisDatabase>.ObjectDeleteCommand command, Map<String, Object> options) {
      actions.add(new DirectDatabasePersistAction("Delete key") {
         public String getScript() {
            return "DEL " + command.getObject().getFullyQualifiedName(DBPEvaluationContext.DDL);
         }

         public void afterExecute(DBCSession session, Throwable error) {
            RedisKey key = command.getObject();
            RedisSession redisSession = (RedisSession)session;
            RedisUtils.selectCurDatabase(redisSession, key.getDatabase());
            if (redisSession.supportsCommands(JedisCommands.class)) {
               redisSession.getCommands(JedisCommands.class).del(key.getFullyQualifiedName(DBPEvaluationContext.DDL));
            }

         }
      });
   }

   private static @NotNull String[] splitValue(@NotNull Object value) {
      return value instanceof String ? RuntimeUtils.splitCommandLine((String)value, false).toArray(String[]::new) : new String[0];
   }

   private static <T> @NotNull Map<String, T> splitMapValue(@NotNull Object value, @NotNull Function<String, T> mapper) throws DBCException {
      String[] values = splitValue(value);
      Map<String, T> map = new HashMap<>();

      for(int i = 0; i < values.length; i += 2) {
         map.put(values[i], mapper.apply(values[i + 1]));
      }

      return map;
   }
}
