
package com.dc.summer.ext.mysql.model.plan;

import com.dc.summer.ext.mysql.model.MySQLDataSource;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.exec.plan.*;
import com.dc.summer.model.impl.plan.ExecutionPlanDeserializer;
import com.dc.summer.model.sql.SQLDialect;
import com.dc.summer.model.sql.SQLUtils;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.JsonPrimitive;
import com.dc.code.NotNull;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.plan.*;
import com.dc.summer.model.impl.plan.AbstractExecutionPlanSerializer;
import com.dc.utils.CommonUtils;

import java.io.IOException;
import java.io.Reader;
import java.io.Writer;
import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * MySQL execution plan analyser
 */
public class MySQLPlanAnalyser extends AbstractExecutionPlanSerializer implements DBCQueryPlanner {

    private MySQLDataSource dataSource;

    public MySQLPlanAnalyser(MySQLDataSource dataSource) {
        this.dataSource = dataSource;
    }

    public MySQLPlanAbstract explain(JDBCSession session, String query) throws DBCException {
        final SQLDialect dialect = SQLUtils.getDialectFromObject(dataSource);
        final String plainQuery = SQLUtils.stripComments(dialect, query).toUpperCase();
        final String firstKeyword = SQLUtils.getFirstKeyword(dialect, plainQuery);
        if (!"SELECT".equalsIgnoreCase(firstKeyword) && !"WITH".equalsIgnoreCase(firstKeyword)) {
            throw new DBCException("Only SELECT statements could produce execution plan");
        }
        if (supportsExplainJSON()) {
            return new MySQLPlanJSON(session, query);
        } else {
            return new MySQLPlanClassic(session, query);
        }
    }

    private boolean supportsExplainJSON() {
        if (dataSource.isMariaDB()) {
            return dataSource.isServerVersionAtLeast(10, 1);
        } else {
            return dataSource.isServerVersionAtLeast(5, 6);
        }
    }

    @Override
    public DBPDataSource getDataSource() {
        return dataSource;
    }

    @NotNull
    @Override
    public DBCPlan planQueryExecution(@NotNull DBCSession session, @NotNull String query, @NotNull DBCQueryPlannerConfiguration configuration) throws DBCException {
        return explain((JDBCSession) session, query);
    }

    @NotNull
    @Override
    public DBCPlanStyle getPlanStyle() {
        return DBCPlanStyle.PLAN;
    }

    @Override
    public void serialize(@NotNull Writer writer, @NotNull DBCPlan plan) throws IOException, InvocationTargetException {

        serializeJson(writer, plan, dataSource.getInfo().getDriverName(), new DBCQueryPlannerSerialInfo() {

            @Override
            public String version() {
                return plan instanceof MySQLPlanClassic ? "classic" : "json";
            }

            @Override
            public void addNodeProperties(DBCPlanNode node, JsonObject nodeJson) {

                JsonObject attributes = new JsonObject();
                if (node instanceof MySQLPlanNodePlain) {
                    MySQLPlanNodePlain plainNode = (MySQLPlanNodePlain) node;
                    attributes.add("id", new JsonPrimitive(plainNode.getId()));
                    attributes.add("select_type", new JsonPrimitive(CommonUtils.notEmpty((plainNode.getSelectType()))));
                    attributes.add("table", new JsonPrimitive(CommonUtils.notEmpty(plainNode.getTable())));
                    attributes.add("type", new JsonPrimitive(CommonUtils.notEmpty(plainNode.getNodeType())));
                    attributes.add("possible_keys", new JsonPrimitive(CommonUtils.notEmpty(plainNode.getPossibleKeys())));
                    attributes.add("key", new JsonPrimitive(CommonUtils.notEmpty(plainNode.getKey())));
                    attributes.add("key_len", new JsonPrimitive(CommonUtils.notEmpty(plainNode.getKeyLength())));
                    attributes.add("ref", new JsonPrimitive(CommonUtils.notEmpty(plainNode.getRef())));
                    attributes.add("rows", new JsonPrimitive(plainNode.getRowCount()));
                    attributes.add("filtered", new JsonPrimitive(plainNode.getFiltered()));
                    attributes.add("extra", new JsonPrimitive(CommonUtils.notEmpty(plainNode.getExtra())));
                } else if (node instanceof MySQLPlanNodeJSON) {
                    MySQLPlanNodeJSON jsNode = (MySQLPlanNodeJSON) node;
                    for(Map.Entry<String, String>  e : jsNode.getNodeProps().entrySet()) {
                        attributes.add(e.getKey(), new JsonPrimitive(CommonUtils.notEmpty(e.getValue())));
                    }
                }
                nodeJson.add(PROP_ATTRIBUTES, attributes);
            }
        });

/*
        if (plan instanceof MySQLPlanClassic) {
            serializeJson(planData, plan,dataSource.getInfo().getDriverName(),(MySQLPlanClassic) plan);
        } else if (plan instanceof MySQLPlanJSON) {
            serializeJson(planData, plan,dataSource.getInfo().getDriverName(),(MySQLPlanJSON) plan);
        }
*/

    }

    private static Map<String, String> getNodeAttributes(JsonObject nodeObject){
        Map<String,String> attributes = new HashMap<>();

        JsonObject attrs =  nodeObject.getAsJsonObject(PROP_ATTRIBUTES);
        for(Map.Entry<String, JsonElement> attr : attrs.entrySet()) {
            attributes.put(attr.getKey(), attr.getValue().getAsString());
        }

        return attributes;
    }

    @Override
    public DBCPlan deserialize(@NotNull Reader planData) throws IOException, InvocationTargetException {

        JsonObject jo = new JsonParser().parse(planData).getAsJsonObject();
 
        String savedVersion = getVersion(jo);
        
        String query = getQuery(jo);

        if (savedVersion.equals("classic")) {
            ExecutionPlanDeserializer<MySQLPlanNodePlain> loader = new ExecutionPlanDeserializer<>();
            List<MySQLPlanNodePlain> rootNodes = loader.loadRoot(dataSource, jo,
                (datasource, node, parent) -> new MySQLPlanNodePlain(parent, getNodeAttributes(node)));
            return new MySQLPlanClassic(dataSource, query, rootNodes);
        } else {
            ExecutionPlanDeserializer<MySQLPlanNodeJSON> loader = new ExecutionPlanDeserializer<>();
            List<MySQLPlanNodeJSON> rootNodes = loader.loadRoot(dataSource, jo,
                (datasource, node, parent) -> new MySQLPlanNodeJSON(parent, getNodeAttributes(node)));
            return new MySQLPlanJSON(dataSource,query,rootNodes);
        }

    }

}
