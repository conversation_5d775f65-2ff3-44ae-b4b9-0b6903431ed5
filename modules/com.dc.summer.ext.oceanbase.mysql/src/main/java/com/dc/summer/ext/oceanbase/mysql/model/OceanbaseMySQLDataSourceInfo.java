package com.dc.summer.ext.oceanbase.mysql.model;

import com.dc.summer.ext.mysql.model.MySQLDataSourceInfo;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;

public class OceanbaseMySQLDataSourceInfo extends MySQLDataSourceInfo {
    public OceanbaseMySQLDataSourceInfo(JDBCDatabaseMetaData metaData) {
        super(metaData);
    }

    @Override
    public boolean isQueryStatementLockTable() {
        return false;
    }
}
