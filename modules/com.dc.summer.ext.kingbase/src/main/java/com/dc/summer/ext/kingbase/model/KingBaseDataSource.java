package com.dc.summer.ext.kingbase.model;

import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.ext.kingbase.model.jdbc.KingBaseJdbcFactory;
import com.dc.summer.ext.postgresql.model.PostgreDataSource;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.DBPDataSourceInfo;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.summer.model.exec.jdbc.JDBCFactory;
import com.dc.summer.model.impl.jdbc.JDBCExecutionContext;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import lombok.extern.slf4j.Slf4j;

import java.sql.Connection;
import java.sql.SQLException;

@Slf4j
public class KingBaseDataSource extends PostgreDataSource {

    private String instancePrefix;

    public KingBaseDataSource(DBRProgressMonitor monitor, DBPDataSourceContainer container) throws DBException {
        super(monitor, container, new KingBaseDialect());
    }

    @Override
    protected DBPDataSourceInfo createDataSourceInfo(DBRProgressMonitor monitor, JDBCDatabaseMetaData metaData) {
        return new KingBaseDataSourceInfo(this, metaData);
    }

    @Override
    public String getInstancePrefix() {
        return instancePrefix;
    }

    @Override
    protected String getDefaultDatabase() {
        return "";
    }

    @Override
    protected Connection openConnection(DBRProgressMonitor monitor, JDBCExecutionContext context, String purpose, DBPConnectionConfiguration configuration) throws DBCException {

        Connection connection = super.openConnection(monitor, context, purpose, configuration);

        if (instancePrefix == null) {

            if (configuration != null) {
                String kingBaseModel = configuration.getProviderProperty("@summer-kingbase-model@");
                if ("oracle".equals(kingBaseModel)) {
                    instancePrefix = "sys";
                } else if ("postgresql".equals(kingBaseModel)) {
                    instancePrefix = "pg";
                } else {
                    setInstancePrefix(connection);
                }
            } else {
                setInstancePrefix(connection);
            }

        }

        return connection;
    }

    public boolean isPg() {
        return "pg".equals(this.instancePrefix);
    }

    private void setInstancePrefix(Connection connection) {
        try {
            Object o = JDBCUtils.executeQuery(connection, " SHOW database_mode");
            if (o instanceof String) {
                if (o.equals("pg")) {
                    instancePrefix = "pg";
                } else {
                    instancePrefix = "sys";
                }
            }
        } catch (SQLException e) {
            instancePrefix = "sys";
            log.warn("查询数据库模式失败，默认使用 [oracle]，错误：" + e.getMessage());
        }
    }

    @NotNull
    @Override
    protected JDBCFactory createJdbcFactory() {
        return new KingBaseJdbcFactory();
    }

}
