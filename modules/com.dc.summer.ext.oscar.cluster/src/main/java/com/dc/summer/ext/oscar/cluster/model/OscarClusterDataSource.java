package com.dc.summer.ext.oscar.cluster.model;

import com.dc.code.Nullable;
import com.dc.function.RuntimeRunnable;
import com.dc.summer.DBException;
import com.dc.summer.ModelPreferences;
import com.dc.summer.ext.generic.model.meta.GenericMetaModel;
import com.dc.summer.ext.oscar.cluster.data.OscarClusterValueHandlerProvider;
import com.dc.summer.ext.oscar.data.OscarValueHandlerProvider;
import com.dc.summer.ext.oscar.model.OscarDataSource;
import com.dc.summer.ext.oscar.model.OscarDataSourceInfo;
import com.dc.summer.ext.oscar.model.OscarUtils;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.DBPDataSourceInfo;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.connection.DBPDriver;
import com.dc.summer.model.data.DBDValueHandlerProvider;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.summer.model.impl.jdbc.JDBCExecutionContext;
import com.dc.summer.model.impl.jdbc.JDBCRemoteInstance;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.utils.PrefUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;

public class OscarClusterDataSource extends OscarDataSource {

    public OscarClusterDataSource(DBRProgressMonitor monitor, DBPDataSourceContainer container, GenericMetaModel metaModel) throws DBException {
        super(monitor, container, metaModel, new OscarClusterDialect());
        // 没找到神通集群版，能执行的测试SQL
        PrefUtils.setPreferenceValue(container.getPreferenceStore(), ModelPreferences.CONNECT_VALIDATION_QUERY, "");
    }

    @Override
    protected DBPDataSourceInfo createDataSourceInfo(DBRProgressMonitor monitor, JDBCDatabaseMetaData metaData) {
        return new OscarClusterDataSourceInfo(metaData);
    }

    @Override
    protected JDBCExecutionContext createExecutionContext(JDBCRemoteInstance instance, String type) {
        return new OscarClusterExecutionContext(instance, type);
    }

    @Override
    protected Map<String, String> getInternalConnectionProperties(DBRProgressMonitor monitor, DBPDriver driver, JDBCExecutionContext context, String purpose, DBPConnectionConfiguration connectionInfo) throws DBCException {
        Map<String, String> connectionsProps = super.getInternalConnectionProperties(monitor, driver, context, purpose, connectionInfo);
        if (purpose.equals("Data transfer consumer")) {
            connectionsProps.put("useBulkInsertBatch", "true");
        }
        return connectionsProps;
    }

    @Override
    public Object getAdapter(Class adapter) {
        return adapter == DBDValueHandlerProvider.class ? new OscarClusterValueHandlerProvider() : super.getAdapter(adapter);
    }

    @Override
    protected String getQuerySetActiveDB(String dbName) {
        String searchPathSql = OscarUtils.makeSafeSearchPathSql(new String[]{dbName}, null);
        return getQuerySetActiveDB().replaceFirst("\\?", Matcher.quoteReplacement(searchPathSql));
    }


    @Override
    public RuntimeRunnable killConnection(DBRProgressMonitor monitor, JDBCExecutionContext defaultContext, String processId) {
        return null;
    }
}
