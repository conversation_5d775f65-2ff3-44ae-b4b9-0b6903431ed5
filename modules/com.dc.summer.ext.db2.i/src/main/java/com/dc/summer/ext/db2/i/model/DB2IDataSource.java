
package com.dc.summer.ext.db2.i.model;

import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.ext.generic.model.GenericDataSource;
import com.dc.summer.ext.generic.model.meta.GenericMetaModel;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.DBPDataSourceInfo;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.summer.model.runtime.DBRProgressMonitor;

public class DB2IDataSource extends GenericDataSource {

    private static final Log log = Log.getLog(DB2IDataSource.class);

    public DB2IDataSource(DBRProgressMonitor monitor, DBPDataSourceContainer container, GenericMetaModel metaModel)
        throws DBException
    {
        super(monitor, container, metaModel, new DB2ISQLDialect());
    }

    @Override
    protected DBPDataSourceInfo createDataSourceInfo(DBRProgressMonitor monitor, JDBCDatabaseMetaData metaData) {
        return new DB2IDataSourceInfo(metaData);
    }

}
